// 这里将导出所有图片资源

// 导出图片路径
export const IMAGES = {
  // 登录页背景图片
  loginBackground: require('./login-bg.jpg'),

  // 登录页图标
  loginIcons: {
    aivgen: require('./login/aivgenIcon.png'),
    google: require('./login/googleIcon.png'),
    facebook: require('./login/facebookIcon.png'),
  },

  // 注册页背景图
  signupBackground: require('./login/bg.png'),

  // 常用图标
  backIcon: require('./back.png'),
  logo: require('./logo.png'),
  arrowRight: require('./arrow-right.png'),
  warningIcon: require('./warning-icon.png'),
  eyeOpen: require('./eye-open.png'),
  eyeClosed: require('./eye-closed.png'),
  aivgenIcon: require('./aivgenIcon.png'),
  emailIcon: require('./email-icon.png'),
  sendIcon: require('./send-icon.png'),

  // 首页图标
  searchIcon: require('./home/<USER>'),
  menuIcon: require('./home/<USER>'),
  defaultAvatar: require('./home/<USER>'),
  banner: require('./home/<USER>'),
  musicIcon: require('./home/<USER>'),

  // 分类图标
  categoryIcons: {
    all: require('./home/<USER>'),
    musician: require('./home/<USER>'),
    actor: require('./home/<USER>'),
    broadcaster: require('./home/<USER>'),
    sports: require('./home/<USER>'),
    director: require('./home/<USER>'),
    producer: require('./home/<USER>'),
    dancer: require('./home/<USER>'),
  },

  // 底部导航图标
  tabIcons: {
    home: require('./home/<USER>'),
    subscript: require('./home/<USER>'),
    inbox: require('./home/<USER>'),
    my: require('./home/<USER>'),
  },

  // 协议背景图
  agreement1: require('./login/agreement1.png'),
  agreement2: require('./login/agreement2.png'),

  // 复选框图标
  checkboxOn: require('./login/checkbox_on.png'),
  checkboxOff: require('./login/checkbox_off.png'),

  // 名人详情页图标
  heart: require('./home/<USER>'),
  heartFilled: require('./home/<USER>'),
  alarmIcon: require('./home/<USER>'),
  messageIcon: require('./home/<USER>'),
  bannerIcon: require('./banner.png'),
  // 其他图标
  checkIcon: require('./check.png'),
  editIcon: require('./edit.png'),
};

export default IMAGES;
