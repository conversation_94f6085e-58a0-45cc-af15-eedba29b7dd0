import React, { createContext, useState, useContext, useEffect } from 'react';
import { LanguageType, changeLanguage, getStoredLanguage, languageOptionMap } from '../i18n';
import { LanguageOption } from '../components/LanguageBottomSheet';

// 语言上下文类型
interface LanguageContextType {
  currentLanguage: LanguageType;
  currentLanguageOption: LanguageOption;
  setLanguage: (language: LanguageType) => Promise<void>;
  setLanguageByOption: (option: LanguageOption) => Promise<void>;
  isLoading: boolean;
}

// 创建语言上下文
const LanguageContext = createContext<LanguageContextType | undefined>(undefined);

// 语言选项映射到语言代码
const optionToLanguageMap: Record<LanguageOption, LanguageType> = {
  'ENGLISH': 'en',
  'CHINESE': 'zh',
  'THAI': 'th',
  'KOREAN': 'en', // 暂时映射到英文
  'JAPANESE': 'en', // 暂时映射到英文
};

// 语言代码映射到语言选项
const languageToOptionMap: Record<LanguageType, LanguageOption> = {
  'en': 'ENGLISH',
  'zh': 'CHINESE',
  'th': 'THAI',
};

// 语言提供者组件
export const LanguageProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [currentLanguage, setCurrentLanguage] = useState<LanguageType>('en');
  const [isLoading, setIsLoading] = useState(true);

  // 初始化语言设置
  useEffect(() => {
    const initLanguage = async () => {
      try {
        const storedLanguage = await getStoredLanguage();
        if (storedLanguage) {
          setCurrentLanguage(storedLanguage);
          await changeLanguage(storedLanguage);
        }
      } catch (error) {
        console.error('Error initializing language:', error);
      } finally {
        setIsLoading(false);
      }
    };

    initLanguage();
  }, []);

  // 设置语言
  const setLanguage = async (language: LanguageType) => {
    try {
      setIsLoading(true);
      await changeLanguage(language);
      setCurrentLanguage(language);
    } catch (error) {
      console.error('Error setting language:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // 通过语言选项设置语言
  const setLanguageByOption = async (option: LanguageOption) => {
    const language = optionToLanguageMap[option];
    if (language) {
      await setLanguage(language);
    }
  };

  return (
    <LanguageContext.Provider
      value={{
        currentLanguage,
        currentLanguageOption: languageToOptionMap[currentLanguage],
        setLanguage,
        setLanguageByOption,
        isLoading,
      }}
    >
      {children}
    </LanguageContext.Provider>
  );
};

// 使用语言上下文的钩子
export const useLanguage = () => {
  const context = useContext(LanguageContext);
  if (context === undefined) {
    throw new Error('useLanguage must be used within a LanguageProvider');
  }
  return context;
};
