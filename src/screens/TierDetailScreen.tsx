import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Image,
  TouchableOpacity,
  ScrollView,
  StatusBar,
  ActivityIndicator,
  Platform,
  Alert,
} from 'react-native';
import { FONTS } from '../assets/fonts';
import { Header } from '../components';
import { useNavigation, useRoute, RouteProp, CommonActions } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { get, post } from '../services/api/apiService';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { showSuccess, showError } from '../services/toast';
import { useTranslation } from 'react-i18next';
import * as RNIap from 'react-native-iap';

// 定义导航参数类型
type RootStackParamList = {
  Home: undefined;
  Celebrity: undefined;
  Subscription: undefined;
  TierList: undefined;
  TierDetail: {
    id: number;
    name: string;
    description: string;
    price: string;
    celebrityNm?: string; // 添加名人nm参数
    subscriptionType?: 'upgrade' | 'downgrade' | 'new'; // 添加订阅类型参数
  };
  Alarm: {
    id: number;
    name: string;
    type: string;
    image: any;
    nm?: string; // 添加nm参数
  };
};

type TierDetailScreenNavigationProp = StackNavigationProp<RootStackParamList, 'TierDetail'>;
type TierDetailScreenRouteProp = RouteProp<RootStackParamList, 'TierDetail'>;

// 获取屏幕宽度，用于响应式设计（当前未使用）
// const { width } = Dimensions.get('window');

// 定义套餐详情接口
interface PackageInfo {
  id?: number;
  nm?: string;
  packageTitle?: string;
  packageDetails?: string;
  amount?: string;
  introduce?: string;
  generationTime?: number;
  surcharge?: number;
  [key: string]: any;
}

// 默认详情数据（当API调用失败时使用）
const dummyDetailText = `vero eos et accusamus et iusto odio dignissimos ducimus qui blanditiis praesentium voluptatum deleniti atque corrupti quos dolores et quas molestias excepturi sint occaecati cupiditate non provident, similique sunt in culpa qui officia deserunt mollitia animi, id est laborum et dolorum fuga.`;

const TierDetailScreen = () => {
  // 获取导航对象和路由参数
  const navigation = useNavigation<TierDetailScreenNavigationProp>();
  const route = useRoute<TierDetailScreenRouteProp>();
  const { t } = useTranslation();

  // 从路由参数中获取 Tier 信息
  const { id, name, price } = route.params; // description 未使用

  // 状态管理
  const [packageInfo, setPackageInfo] = useState<PackageInfo | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [flag, setFlag] = useState<boolean>(false); // 添加 flag 状态

  // 在组件挂载时获取套餐详情
  useEffect(() => {
    const fetchPackageInfo = async () => {
      setIsLoading(true);
      setError(null);

      try {
        // 调用 API 获取套餐详情
        const response = await get('/packageInformation/' + id);
        console.log('Package info response:', JSON.stringify(response.data));

        if (response.data && response.data.code === '200' && response.data.data) {
          // 保存套餐详情数据
          const packageData = response.data.data;

          // 确保 name 字段存在，这是订阅ID
          console.log('API返回的套餐详情:', JSON.stringify(packageData));

          setPackageInfo(packageData);

          // 检查是否为 Tier 1，如果是则设置 flag 为 true
          if (packageData.packageTitle === 'Tier 1' ||
              packageData.packageTitle?.toLowerCase().includes('tier 1') ||
              name === 'Tier 1' ||
              name.toLowerCase().includes('tier 1')) {
            setFlag(true);
          }
        } else {
          setError(response.data?.msg || t('subscription.failedToGetPackageDetails'));
        }
      } catch (err) {
        console.error('Error fetching package info:', err);
        setError(t('subscription.errorFetchingPackageDetails'));
      } finally {
        setIsLoading(false);
      }
    };

    fetchPackageInfo();
  }, [id, name]);

  // 初始化IAP连接
  useEffect(() => {
    // 连接到应用商店
    const initializeIAP = async () => {
      try {
        // 初始化连接
        await RNIap.initConnection();
        console.log('IAP连接初始化成功');

        // 记录初始化成功日志
        await post('/logMessage/paylogs', {
          type: 'init',
          status: 'success',
          message: 'IAP连接初始化成功',
          timestamp: new Date().toISOString(),
          device: Platform.OS,
        });
      } catch (err) {
        console.error('IAP连接初始化失败:', err);

        // 记录初始化失败日志
        await post('/logMessage/paylogs', {
          type: 'init',
          status: 'error',
          message: '初始化失败: ' + (err instanceof Error ? err.message : String(err)),
          timestamp: new Date().toISOString(),
          device: Platform.OS,
        });
      }
    };

    initializeIAP();

    // 组件卸载时关闭连接
    return () => {
      RNIap.endConnection();
    };
  }, []);

  // 处理订阅购买
  const handlePurchase = async () => {
    try {
      console.log('packageInfo',packageInfo)
      setIsLoading(true);
      const subId = packageInfo?.nm;
      // 获取用户信息
      const userInfoStr = await AsyncStorage.getItem('@aivgen:user_info');
      if (!userInfoStr) {
        showError(t('common.pleaseLoginFirst'));
        setIsLoading(false);
        return;
      }

      const userInfo = JSON.parse(userInfoStr);
      const account = userInfo.nm || '';

      // 获取名人nm
      const voiceNm = route.params.celebrityNm || '';

      // 获取订阅ID - 使用 packageInfo.name 作为订阅ID
      const subscriptionId = packageInfo?.name;
      if (!subscriptionId) {
        showError(t('subscription.subscriptionIdEmpty'));
        setIsLoading(false);
        return;
      }


      // 在购买前先检查用户是否已经购买过该订阅
      try {

        // 获取用户的已有购买
        const availablePurchases = await RNIap.getAvailablePurchases();

        // 查找匹配的购买
        const matchingPurchase = availablePurchases.find(
          (purchase) => purchase.productId === subscriptionId
        );

        if (matchingPurchase) {
          console.log('找到匹配的购买:', matchingPurchase);

          // 用户已经购买过该订阅，直接使用已有的购买继续处理
          // 记录已拥有订阅的日志
          await post('/logMessage/paylogs', {
            type: 'purchase',
            status: 'info',
            message: '用户已拥有此订阅，直接使用已有购买',
            productId: subscriptionId,
            timestamp: new Date().toISOString(),
            device: Platform.OS,
            userInfo: { account: account },
          });

          // 获取购买凭证
          let purchaseToken = '';

          // 生成随机的purchaseToken作为备用
          const randomToken = `purchase_${Date.now()}_${Math.random().toString(36).substring(2, 10)}`;

          if (Platform.OS === 'android') {
            // Android 使用 purchaseToken
            if (typeof matchingPurchase === 'object' && 'purchaseToken' in matchingPurchase) {
              purchaseToken = matchingPurchase.purchaseToken as string || randomToken;
            } else {
              purchaseToken = randomToken;
            }
          } else {
            // iOS 使用 transactionReceipt
            if (typeof matchingPurchase === 'object' && 'transactionReceipt' in matchingPurchase) {
              purchaseToken = matchingPurchase.transactionReceipt as string || randomToken;
            } else {
              purchaseToken = randomToken;
            }
          }

          // 准备请求参数
          const params = {
            'account': account,
            'packageNm': packageInfo?.nm || '',
            'voiceNm': voiceNm,
            'purchaseToken': purchaseToken,
          };

          console.log('使用已有购买提交订阅数据:', params);

          // 调用API
          const response = await post('/packageInformationAccount/save', params);

          if (response.data && (response.data.code === '200' || response.data.code === 200)) {
            // 购买成功
            showSuccess(t('subscription.subscriptionSuccess'));

            // 使用CommonActions.reset完全重置导航堆栈
            setTimeout(() => {
              navigation.dispatch(
                CommonActions.reset({
                  index: 1,
                  routes: [
                    { name: 'MainTabs' },
                    {
                      name: 'Alarm',
                      params: {
                        id: id,
                        name: name,
                        type: '',
                        image: null,
                        nm: voiceNm,
                      },
                    },
                  ],
                })
              );
            }, 1500);

            setIsLoading(false);
            return;
          } else {
            // API调用失败
            showError(response.data?.msg || t('subscription.subscriptionFailed'));
            setIsLoading(false);
            return;
          }
        }

        // 如果没有找到匹配的购买，继续正常的购买流程
        console.log('未找到匹配的购买，继续正常购买流程');
      } catch (err) {
        // 如果检查已有购买时出错，记录错误但继续正常购买流程
        console.error('检查已有购买时出错:', err);

        await post('/logMessage/paylogs', {
          type: 'checkExistingPurchase',
          status: 'error',
          message: '检查已有订阅时出错',
          productId: subscriptionId,
          error: err instanceof Error ? err.message : String(err),
          timestamp: new Date().toISOString(),
          device: Platform.OS,
          userInfo: { account: account },
        });
      }

      try {
        // 获取产品信息
        const products = await RNIap.getSubscriptions({ skus: [subscriptionId] });
        console.log('获取到的产品信息:', products);

        // 记录获取产品信息成功的日志
        await post('/logMessage/paylogs', {
          type: 'getProducts',
          status: 'success',
          message: '成功获取订阅产品信息',
          productId: subscriptionId,
          productsCount: products.length,
          products: JSON.stringify(products),
          timestamp: new Date().toISOString(),
          device: Platform.OS,
          result: {
            success: true,
            data: products
          }
        });

        if (products.length === 0) {
          showError(t('subscription.productNotFound'));
          setIsLoading(false);
          return;
        }

        // 记录开始购买的日志
        await post('/logMessage/paylogs', {
          type: 'purchase',
          status: 'start',
          message: '开始购买订阅',
          productId: subscriptionId,
          timestamp: new Date().toISOString(),
          device: Platform.OS,
          userInfo: { account: account },
        });
        // 检查产品是否有订阅详情
        const subscriptionOfferDetails = products[0]?.subscriptionOfferDetails;

        if (subscriptionOfferDetails && Array.isArray(subscriptionOfferDetails)) {
          const targetSubscription = subscriptionOfferDetails.find((sub: { basePlanId: string; }) => sub.basePlanId === subId);

          if (targetSubscription && targetSubscription.offerToken) {
            console.log('获取当前的订阅信息:', targetSubscription.offerToken);
          } else {
            console.error('Subscription not found for ID:', subId);
          }
        } else {
          console.log('使用标准订阅流程，无需offerToken');
        }
        // 请求购买
        let purchase;

        // 检查是否有订阅详情和offerToken
        if (subscriptionOfferDetails && Array.isArray(subscriptionOfferDetails)) {
          const targetSubscription = subscriptionOfferDetails.find((sub: { basePlanId: string; }) => sub.basePlanId === subId);

          if (targetSubscription && targetSubscription.offerToken) {
            // 使用offerToken购买
            purchase = await RNIap.requestSubscription({
              sku: subscriptionId,
              subscriptionOffers: [
                {
                  sku: subscriptionId,
                  offerToken: targetSubscription.offerToken,
                },
              ],
            });
          } else {
            // 没有找到匹配的订阅计划，使用标准方式
            purchase = await RNIap.requestSubscription({
              sku: subscriptionId,
            });
          }
        } else {
          // 没有订阅详情，使用标准方式
          purchase = await RNIap.requestSubscription({
            sku: subscriptionId,
          });
        }
        console.log('购买结果:', purchase);

        // 记录购买成功的日志
        await post('/logMessage/paylogs', {
          type: 'purchase',
          status: 'success',
          message: '订阅购买成功',
          productId: subscriptionId,
          purchaseInfo: JSON.stringify(purchase),
          timestamp: new Date().toISOString(),
          device: Platform.OS,
          result: {
            success: true,
            data: purchase
          },
        });

        // 购买成功，调用API保存订阅信息
        if (purchase) {
          // 获取购买凭证
          let purchaseToken = '';

          // 生成随机的purchaseToken作为备用
          const randomToken = `purchase_${Date.now()}_${Math.random().toString(36).substring(2, 10)}`;

          if (Platform.OS === 'android') {
            // Android 使用 purchaseToken
            if (typeof purchase === 'object' && 'purchaseToken' in purchase) {
              purchaseToken = purchase.purchaseToken as string || randomToken;
            } else {
              purchaseToken = randomToken;
            }
          } else {
            // iOS 使用 transactionReceipt
            if (typeof purchase === 'object' && 'transactionReceipt' in purchase) {
              purchaseToken = purchase.transactionReceipt as string || randomToken;
            } else {
              purchaseToken = randomToken;
            }
          }

          // 准备请求参数
          const params = {
            'account': account,
            'packageNm': packageInfo?.nm || '',  // 使用 packageInfo.nm 作为包名
            'voiceNm': voiceNm,
            'purchaseToken': purchaseToken,
          };

          console.log('Purchase params:', params);

          // 记录开始保存订阅信息的日志
          await post('/logMessage/paylogs', {
            type: 'saveSubscription',
            status: 'start',
            message: '开始保存订阅信息',
            productId: subscriptionId,
            packageNm: params.packageNm,
            timestamp: new Date().toISOString(),
            device: Platform.OS,
            userInfo: { account: account },
          });

          // 调用API
          const response = await post('/packageInformationAccount/save', params);
          console.log('Purchase response:', JSON.stringify(response.data));

          if (response.data && (response.data.code === '200' || response.data.code === 200)) {
            // 记录保存订阅信息成功的日志
            await post('/logMessage/paylogs', {
              type: 'saveSubscription',
              status: 'success',
              message: '订阅信息保存成功',
              productId: subscriptionId,
              packageNm: params.packageNm,
              responseData: JSON.stringify(response.data),
              timestamp: new Date().toISOString(),
              device: Platform.OS,
              result: {
                success: true,
                data: response.data
              },
            });

            // 购买成功
            showSuccess(t('subscription.subscriptionSuccess'));

            // 使用CommonActions.reset完全重置导航堆栈
            setTimeout(() => {
              navigation.dispatch(
                CommonActions.reset({
                  index: 1, // 设置为1，表示当前活动页面是Alarm
                  routes: [
                    { name: 'MainTabs' }, // 首先添加主标签页作为基础
                    {
                      name: 'Alarm',
                      params: {
                        id: id,
                        name: name,
                        type: '',
                        image: null,
                        nm: voiceNm,
                      },
                    },
                  ],
                })
              );
            }, 1500); // 延迟1.5秒，让用户看到成功提示
          } else {
            // 记录保存订阅信息失败的日志
            await post('/logMessage/paylogs', {
              type: 'saveSubscription',
              status: 'error',
              message: '订阅信息保存失败',
              productId: subscriptionId,
              packageNm: params.packageNm,
              errorMsg: response.data?.msg || '未知错误',
              responseData: JSON.stringify(response.data),
              timestamp: new Date().toISOString(),
              device: Platform.OS,
              result: {
                success: false,
                error: response.data?.msg || '未知错误',
                data: response.data
              },
            });

            // API调用失败
            showError(response.data?.msg || t('subscription.subscriptionFailed'));
          }
        }
      } catch (iapError: any) {
        console.log('IAP购买过程中出错:', iapError);

        // 记录IAP购买错误日志
        const errorCode = iapError && iapError.code ? iapError.code : '未知错误码';
        const errorMessage = iapError && typeof iapError === 'object' && 'message' in iapError
          ? iapError.message
          : '未知错误';

        await post('/logMessage/paylogs', {
          type: 'purchase',
          status: 'error',
          message: t('subscription.purchaseError'),
          productId: subscriptionId,
          errorCode: errorCode,
          errorMessage: errorMessage,
          timestamp: new Date().toISOString(),
          device: Platform.OS,
          userInfo: { account: account },
          result: {
            success: false,
            error: errorMessage,
            errorCode: errorCode,
            errorDetails: JSON.stringify(iapError)
          },
        });

        // 处理用户取消购买的情况
        if (iapError && iapError.code === 'E_USER_CANCELLED') {
          showError(t('subscription.userCancelled'));
        }
        // 处理用户已拥有商品的情况
        else if (iapError && iapError.code === 'E_ALREADY_OWNED') {
          console.log('用户已拥有此订阅，尝试继续处理...');

          // 记录已拥有商品的日志
          await post('/logMessage/paylogs', {
            type: 'purchase',
            status: 'warning',
            message: '用户已拥有此订阅，尝试继续处理',
            productId: subscriptionId,
            timestamp: new Date().toISOString(),
            device: Platform.OS,
            userInfo: { account: account },
          });

          // 尝试获取用户的已有购买
          try {
            const availablePurchases = await RNIap.getAvailablePurchases();
            console.log('可用的购买:', availablePurchases);

            // 查找匹配的购买
            const matchingPurchase = availablePurchases.find(
              (purchase) => purchase.productId === subscriptionId
            );

            if (matchingPurchase) {
              console.log('找到匹配的购买:', matchingPurchase);

              // 获取购买凭证
              let purchaseToken = '';

              // 生成随机的purchaseToken作为备用
              const randomToken = `purchase_${Date.now()}_${Math.random().toString(36).substring(2, 10)}`;

              if (Platform.OS === 'android') {
                // Android 使用 purchaseToken
                if (typeof matchingPurchase === 'object' && 'purchaseToken' in matchingPurchase) {
                  purchaseToken = matchingPurchase.purchaseToken as string || randomToken;
                } else {
                  purchaseToken = randomToken;
                }
              } else {
                // iOS 使用 transactionReceipt
                if (typeof matchingPurchase === 'object' && 'transactionReceipt' in matchingPurchase) {
                  purchaseToken = matchingPurchase.transactionReceipt as string || randomToken;
                } else {
                  purchaseToken = randomToken;
                }
              }

              // 准备请求参数
              const params = {
                'account': account,
                'packageNm': packageInfo?.nm || '',
                'voiceNm': voiceNm,
                'purchaseToken': purchaseToken,
              };

              console.log('使用已有购买提交订阅数据:', params);

              // 调用API
              const response = await post('/packageInformationAccount/save', params);

              if (response.data && (response.data.code === '200' || response.data.code === 200)) {
                // 购买成功
                showSuccess(t('subscription.subscriptionSuccess'));

                // 使用CommonActions.reset完全重置导航堆栈
                setTimeout(() => {
                  navigation.dispatch(
                    CommonActions.reset({
                      index: 1,
                      routes: [
                        { name: 'MainTabs' },
                        {
                          name: 'Alarm',
                          params: {
                            id: id,
                            name: name,
                            type: '',
                            image: null,
                            nm: voiceNm,
                          },
                        },
                      ],
                    })
                  );
                }, 1500);
              } else {
                // API调用失败
                showError(response.data?.msg || t('subscription.subscriptionFailed'));
              }
            } else {
              // 没有找到匹配的购买，显示友好的错误信息
              showError(t('subscription.alreadyOwnedButNotFound'));
            }
          } catch (err) {
            console.error('处理已有购买时出错:', err);
            showError(t('subscription.alreadyOwnedProcessError'));
          }
        } else {
          showError(t('subscription.purchaseError') + errorMessage);
        }
      }
    } catch (err) {
      console.error('Error purchasing tier:', err);

      // 记录整体购买流程错误日志
      await post('/logMessage/paylogs', {
        type: 'purchaseProcess',
        status: 'error',
        message: t('subscription.errorDuringSubscription'),
        error: err instanceof Error ? err.message : String(err),
        timestamp: new Date().toISOString(),
        device: Platform.OS,
        result: {
          success: false,
          error: err instanceof Error ? err.message : String(err),
          errorDetails: JSON.stringify(err)
        },
      });

      showError(t('subscription.errorDuringSubscription'));
    } finally {
      setIsLoading(false);
    }
  };

  // 帮助按钮
  const helpButton = (
    <TouchableOpacity style={styles.helpButton}>
      <Text style={styles.helpButtonText}>?</Text>
    </TouchableOpacity>
  );

  // 获取详情内容
  const getDetailContent = () => {
    if (packageInfo && packageInfo.introduce) {
      return packageInfo.introduce;
    }
    return dummyDetailText;
  };

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="transparent" translucent={true} />

      {/* 使用全局 Header 组件 */}
      <Header title={t('subscription.title')} />

      {/* 帮助按钮 */}
      {helpButton}

      {/* Tier 标题 */}
      <View style={styles.tierTitleContainer}>
        <Image
          source={require('../assets/images/home/<USER>')}
          style={styles.tierIcon}
        />
        <Text style={styles.tierTitle}>{packageInfo?.packageTitle || name}</Text>
      </View>

      {/* 加载状态 */}
      {isLoading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#8A6BFF" />
          <Text style={styles.loadingText}>{t('common.loading')}</Text>
        </View>
      ) : error ? (
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>{error}</Text>
        </View>
      ) : (
        /* 详情内容 */
        <ScrollView style={styles.scrollView}>
          <Text style={styles.detailText}>{getDetailContent()}</Text>
        </ScrollView>
      )}

      {/* 底部价格按钮 */}
      <View style={styles.priceButtonContainer}>
        <TouchableOpacity
          style={[
            styles.priceButton,
            isLoading && styles.disabledButton
          ]}
          onPress={handlePurchase}
          disabled={isLoading}
        >
          <Text style={styles.priceButtonText}>
            {isLoading ? t('subscription.beingProcessed') : `₿ ${packageInfo?.amount || price}`}
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000000',
  },
  headerRight: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  searchButton: {
    marginRight: 15,
  },
  menuButton: {},
  icon: {
    width: 20,
    height: 24,
    tintColor: '#FFFFFF',
  },
  helpButton: {
    position: 'absolute',
    top: 100,
    right: 20,
    width: 25,
    height: 25,
    borderRadius: 15,
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: '#FFFFFF',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 10,
  },
  helpButtonText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontFamily: FONTS.pretendard.light,
  },
  tierTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 15,
  },
  tierIcon: {
    width: 15,
    height: 20,
    marginRight: 10,
    tintColor: '#FFFFFF',
  },
  tierTitle: {
    color: '#FFFFFF',
    fontSize: 20,
    fontFamily: FONTS.bebasNeue.regular,
  },
  scrollView: {
    flex: 1,
    paddingHorizontal: 20,
  },
  detailText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontFamily: FONTS.pretendard.light,
    lineHeight: 24,
    marginBottom: 100, // 减小底部边距，因为scrollView已经有了paddingBottom
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontFamily: FONTS.pretendard.light,
    marginTop: 10,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  errorText: {
    color: '#FF6B6B',
    fontSize: 16,
    fontFamily: FONTS.pretendard.light,
    textAlign: 'center',
  },
  priceButtonContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    paddingBottom: 20,
    backgroundColor: '#000000', // 添加背景色，避免透明
    paddingTop: 10, // 添加顶部内边距
    borderTopWidth: 1, // 添加顶部边框
    borderTopColor: '#333333', // 边框颜色
  },
  priceButton: {
    height: 40,
    overflow: 'hidden',
    backgroundColor: '#7246BB',
    marginHorizontal: 20,
    borderRadius: 5,
  },
  disabledButton: {
    backgroundColor: '#555555',
    opacity: 0.7,
  },
  priceButtonText: {
    color: '#FFFFFF',
    fontSize: 18,
    fontFamily: FONTS.bebasNeue.regular,
    letterSpacing: 1,
    lineHeight: 40,
    textAlign: 'center',
  },
});

export default TierDetailScreen;
