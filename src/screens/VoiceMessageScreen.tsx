import React, { useState, useEffect,useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Image,
  TouchableOpacity,
  StatusBar,
  ActivityIndicator,
  ScrollView,
} from 'react-native';
import { FONTS } from '../assets/fonts';
import { IMAGES } from '../assets/images';
import { Header } from '../components';
import { get, post } from '../services/api/apiService';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useNavigation, useRoute, RouteProp,useFocusEffect } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import LinearGradient from 'react-native-linear-gradient';
import { getUserInfo } from '../services/storage/userStorage';
import { useTranslation } from 'react-i18next';
// 定义导航参数类型
type RootStackParamList = {
  Home: undefined;
  Inbox: undefined;
  VoiceMessage: {
    id: number;
    name: string;
    image: any;
    nm?: string; // 添加nm参数
  };
  VoiceMessageDetail: {
    id: string | number;
    name?: string;
    image?: any;
    nm?: string;
    status?: 'normal' | 'countdown' | 'reject';
  };
  SelectVoiceMessage: {
    id: number;
    name: string;
    image: any;
    nm?: string; // 添加nm参数
  };
  MainTabs: {
    screen?: string;
    params?: any;
  };
  Profile: undefined;
  Subscription: undefined;
};

type VoiceMessageRouteProp = RouteProp<
  { VoiceMessage: { id: number; name: string; image: any; nm?: string } },
  'VoiceMessage'
>;

type VoiceMessageScreenNavigationProp = StackNavigationProp<RootStackParamList, 'VoiceMessage'>;



// 消息数据类型
type MessageItem = {
  id: number;
  nm?: string;
  code?: string;
  name: string;
  image: any;
  date: string;
  status: 'normal' | 'countdown' | 'reject';
  countdown?: string;
  createDate?: string;
  setTime?: string;
  messageText?: string;
  voicePersonImages?: string;
  voiceHeadImages?: string;
  generationTime?: string;
  voicePersonName?: string;
  packageInformationVoiceMessageNm?: string;
  type?: string;
  createUser?: string;
};

const VoiceMessageScreen = () => {
  const navigation = useNavigation<VoiceMessageScreenNavigationProp>();
  const route = useRoute<VoiceMessageRouteProp>();
  const { t } = useTranslation();

  // 从路由参数中获取名人信息
  const { id, name, image, nm } = route.params || {
    id: 1,
    name: 'ARINDA GRANDE',
    image: IMAGES.defaultAvatar,
    nm: '',
  };

  // 添加状态管理
  const [celebrityData, setCelebrityData] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [messageData, setMessageData] = useState<MessageItem[]>([]);
  const [countdowns, setCountdowns] = useState<{[key: string]: string}>({});



  // 在组件挂载时调用接口获取名人详情
  useEffect(() => {
    const fetchCelebrityData = async () => {
      try {
        setIsLoading(true);
        setError(null);

        // 使用 AsyncStorage 获取用户信息
        const _userInfo = await getUserInfo();
        let account = '';

        if (_userInfo) {
          account = _userInfo.nm || '';
        }

        // 如果没有nm参数，则不调用接口
        if (!nm) {
          setIsLoading(false);
          return;
        }

        // 调用接口获取名人详情
        const response = await get('/voicePerson/detail/', {
          nm: nm,
          account: account,
        });

        console.log('Celebrity detail response:', JSON.stringify(response.data));

        if (response.data && response.data.code === '200' && response.data.data) {
          // 保存接口返回的数据
          setCelebrityData(response.data.data);
        } else {
          setError(response.data.msg || t('voiceMessage.failedToGetData'));
        }
      } catch (err) {
        console.error('Error fetching celebrity data:', err);
        setError(t('voiceMessage.errorFetchingCelebrityData'));
      } finally {
        setIsLoading(false);
      }
    };

    fetchCelebrityData();
  }, [nm]);

  // 定义获取消息列表的函数
  const fetchMessageList = useCallback(async () => {
    try {
      // 获取最新的用户信息
      const userInfoData = await getUserInfo();

      // 如果没有用户信息或nm，则不调用接口
      if (!userInfoData || !userInfoData.nm || !nm) {
        return;
      }

      setIsLoading(true);

      const params = {
        createUser: userInfoData.nm,
        voicePersonNm: nm,
      };

      console.log(t('logs.callingVoiceMessageListAPI'), params);
      const listResponse = await post('/voiceMessage/list', params);
      console.log(t('logs.voiceMessageListResponse'), JSON.stringify(listResponse.data));

      // 处理返回的数据
      if (listResponse.data && listResponse.data.code === '200' && listResponse.data.data) {
        const messages = listResponse.data.data.map((item: any) => {
          // 根据 status 字段确定显示状态
          let status: 'normal' | 'countdown' | 'reject' = 'normal';

          if (item.status === '0') {
            status = 'countdown'; // AFTER状态
          } else if (item.status === '2') {
            status = 'reject'; // reject状态
          }

          // 格式化日期
          const createDate = item.createDate ? new Date(item.createDate) : new Date();
          const formattedDate = `${createDate.getFullYear()}.${String(createDate.getMonth() + 1).padStart(2, '0')}.${String(createDate.getDate()).padStart(2, '0')}`;

          return {
            id: item.id,
            nm: item.nm,
            code: item.code,
            name: item.voicePersonName || name,
            image: item.voiceHeadImages ? { uri: item.voiceHeadImages } : (celebrityData?.headImage ? { uri: celebrityData.headImage } : image),
            date: formattedDate,
            status: status,
            countdown: '',  // 初始化为空，将在倒计时函数中更新
            createDate: item.createDate,
            setTime: item.setTime,
            messageText: item.messageText,
            voicePersonImages: item.voicePersonImages,
            voiceHeadImages: item.voiceHeadImages,
            generationTime: item.generationTime,
            voicePersonName: item.voicePersonName,
            packageInformationVoiceMessageNm: item.packageInformationVoiceMessageNm,
            type: item.type,
            createUser: item.createUser,
          };
        });

        setMessageData(messages);
      } else {
        setMessageData([]);
      }
    } catch (err) {
      console.error('Error fetching message list:', err);
      // 不设置全局错误，因为名人详情已经显示
    } finally {
      setIsLoading(false);
    }
  }, [nm, celebrityData, name, image]);

  // 当组件挂载时调用语音消息列表接口
  useEffect(() => {
    fetchMessageList();
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);


   // 使用 useFocusEffect 在页面获取焦点时检查刷新标志
   useFocusEffect(
    useCallback(() => {
      // 检查刷新标志
      const checkRefreshFlag = async () => {
        try {
          const refreshFlag = await AsyncStorage.getItem('@aivgen:message_refresh');
          if (refreshFlag === 'true') {
            // 清除刷新标志
            await AsyncStorage.removeItem('@aivgen:message_refresh');
            // 刷新数据
            fetchMessageList();
          }
        } catch (err) {
          console.error('Error checking refresh flag:', err);
        }
      };

      checkRefreshFlag();
    }, [fetchMessageList]) // 添加 fetchMessageList 作为依赖项
  );
  // 解析时间字符串为小时、分钟、秒
  const parseTimeString = (timeString: string) => {
    if (!timeString) {
      return { hours: 0, minutes: 0, seconds: 0 };
    }

    const parts = timeString.split(':');
    if (parts.length !== 3) {
      return { hours: 0, minutes: 0, seconds: 0 };
    }

    return {
      hours: parseInt(parts[0], 10) || 0,
      minutes: parseInt(parts[1], 10) || 0,
      seconds: parseInt(parts[2], 10) || 0,
    };
  };

  // 格式化倒计时时间
  const formatCountdown = (hours: number, minutes: number, seconds: number) => {
    return `${String(hours).padStart(2, '0')}:${String(minutes).padStart(2, '0')}:${String(seconds).padStart(2, '0')}`;
  };

  // 倒计时效果
  useEffect(() => {
    // 初始化倒计时
    const initialCountdowns: {[key: string]: string} = {};

    messageData.forEach(item => {
      if (item.status === 'countdown' && item.generationTime) {
        const { hours, minutes, seconds } = parseTimeString(item.generationTime);
        initialCountdowns[item.id.toString()] = formatCountdown(hours, minutes, seconds);
      }
    });

    setCountdowns(initialCountdowns);

    // 每秒更新倒计时
    const timer = setInterval(() => {
      setCountdowns(prevCountdowns => {
        const newCountdowns = { ...prevCountdowns };

        Object.keys(newCountdowns).forEach(itemId => {
          const timeString = newCountdowns[itemId];
          const { hours, minutes, seconds } = parseTimeString(timeString);

          // 计算新的倒计时时间
          let newSeconds = seconds - 1;
          let newMinutes = minutes;
          let newHours = hours;

          if (newSeconds < 0) {
            newSeconds = 59;
            newMinutes -= 1;
          }

          if (newMinutes < 0) {
            newMinutes = 59;
            newHours -= 1;
          }

          // 如果倒计时结束，则移除该项
          if (newHours < 0) {
            delete newCountdowns[itemId];
          } else {
            newCountdowns[itemId] = formatCountdown(newHours, newMinutes, newSeconds);
          }
        });

        return newCountdowns;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, [messageData]);

  // 处理搜索按钮点击
  const handleSearchPress = () => {
    console.log('Search pressed in VoiceMessageScreen');
  };

  // 处理菜单按钮点击
  const handleMenuPress = () => {
    console.log('Menu pressed in VoiceMessageScreen');
    // 如果需要导航到Menu页面，请使用正确的导航方式
    // navigation.navigate('Menu');
    console.log('Menu navigation not implemented');
  };

  // 处理添加语音消息
  const handleAddVoiceMessage = () => {
    console.log('Add voice message');
    // 导航到选择语音消息页面
    navigation.navigate('SelectVoiceMessage', {
      id,
      name,
      image,
      nm, // 传递nm参数
    });
  };

  // 自定义右侧组件
  const headerRightComponent = (
    <View style={styles.headerRight}>
      <TouchableOpacity style={styles.searchButton} onPress={handleSearchPress}>
        <Image source={IMAGES.searchIcon} style={styles.icon} />
      </TouchableOpacity>
      <TouchableOpacity style={styles.menuButton} onPress={handleMenuPress}>
        <Image source={IMAGES.menuIcon} style={styles.icon} />
      </TouchableOpacity>
    </View>
  );

  // 处理消息项点击
  const handleMessageItemPress = (item: MessageItem) => {
    console.log('Message item pressed:', item.id);
    // 导航到消息详情页面，无论消息状态如何
    navigation.navigate('VoiceMessageDetail', {
      id: item.id,
      name: item.name,
      image: item.image,
      nm: item.nm || nm,
      status: item.status, // 传递状态，以便详情页面可以相应地显示
    });
  };

  // 渲染消息项
  const renderMessageItem = (item: MessageItem) => {
    let backgroundColor = '#0088FF'; // 默认背景色
    let overlay = null;

    if (item.status === 'countdown') {
      backgroundColor = '#333333';
      overlay = (
        <View style={styles.fullOverlay}>
          <Text style={styles.statusText}>{t('inbox.after')}</Text>
          <Text style={styles.countdownText}>{countdowns[item.id.toString()] || item.generationTime || '00:00:00'}</Text>
        </View>
      );
    } else if (item.status === 'reject') {
      backgroundColor = '#333333';
      overlay = (
        <View style={styles.fullOverlay}>
          <Text style={styles.messageDate}>{item.date}</Text>
          <Text style={styles.rejectText}>{t('inbox.reject')}</Text>
        </View>
      );
    }

    return (
      <TouchableOpacity
        style={[styles.messageItem, { backgroundColor }]}
        key={item.id.toString()}
        onPress={() => handleMessageItemPress(item)}
      >
        <Image
          source={item.voicePersonImages ? { uri: item.voicePersonImages } : item.image}
          style={styles.messageImage}
          resizeMode="cover"
        />
        <View style={styles.messageContent}>
          <Text style={styles.messageDate}>{item.date}</Text>
          <Text style={styles.messageName}>{item.name}</Text>
        </View>
        {overlay}
      </TouchableOpacity>
    );
  };

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="transparent" translucent={true} />

      {/* 使用全局 Header 组件 */}
      <Header title={celebrityData?.name || name}   rightComponentType="searchAndMenu" />

      {isLoading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#00FF47" />
        </View>
      ) : error ? (
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>{error}</Text>
        </View>
      ) : (
        <ScrollView
          style={styles.scrollView}
          contentContainerStyle={styles.scrollViewContent}
        >
          {/* 名人头像 */}
          <View style={styles.profileContainer}>
            <LinearGradient
              colors={['#00C853', '#2980B9', '#8E44AD', '#2980B9']}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 1 }}
              style={styles.profileBorder}
            >
              <View style={styles.profileImageContainer}>
                <Image
                  source={celebrityData?.headImage ? { uri: celebrityData.headImage } : image}
                  style={styles.profileImage}
                  resizeMode="cover"
                />
              </View>
            </LinearGradient>
            <Text style={styles.nameText}>{celebrityData?.name || name}</Text>
          </View>

          {/* 消息列表 */}
          <View style={styles.messageListContainer}>
            {messageData.length > 0 && (
              <Text style={styles.messageListTitle}>{t('inbox.celebVoiceMessageList')}</Text>
            )}
            {messageData.length > 0 ? (
              messageData.map(item => renderMessageItem(item))
            ) : (
              <></>
            )}
          </View>

          {/* 添加语音消息部分 */}
          <View style={styles.addVoiceMessageContainer}>
            {messageData.length === 0 && (
              <Text style={styles.addVoiceMessageTitle}>{t('inbox.addVoiceMessage')}</Text>
            )}
            <TouchableOpacity style={styles.addButton} onPress={handleAddVoiceMessage}>
              <Image
                source={require('../assets/images/home/<USER>')}
                style={styles.addButtonIcon}
              />
            </TouchableOpacity>
          </View>
        </ScrollView>
      )}

      {/* 底部导航栏 */}
      <View style={styles.bottomTabBar}>
        <TouchableOpacity
          style={styles.tabItem}
          onPress={() => navigation.navigate('MainTabs', { screen: 'Home' })}
        >
          <Image
            source={IMAGES.tabIcons.home}
            style={[styles.tabIcon, styles.tabIconInactive]}
          />
          <Text style={[styles.tabText, styles.tabTextInactive]}>{t('navigation.home')}</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.tabItem}
          onPress={() => navigation.navigate('MainTabs', { screen: 'Subscription' })}
        >
          <Image
            source={IMAGES.tabIcons.subscript}
            style={[styles.tabIcon, styles.tabIconInactive]}
          />
          <Text style={[styles.tabText, styles.tabTextInactive]}>{t('navigation.subscription')}</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.tabItem}
          onPress={() => navigation.navigate('MainTabs', { screen: 'Inbox' })}
        >
          <Image
            source={IMAGES.tabIcons.inbox}
            style={[styles.tabIcon, styles.tabIconActive]}
          />
          <Text style={[styles.tabText, styles.tabTextActive]}>{t('navigation.inbox')}</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.tabItem}
          onPress={() => navigation.navigate('MainTabs', { screen: 'Profile' })}
        >
          <Image
            source={IMAGES.tabIcons.my}
            style={[styles.tabIcon, styles.tabIconInactive]}
          />
          <Text style={[styles.tabText, styles.tabTextInactive]}>{t('navigation.my')}</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  emptyListContainer: {
    padding: 30,
    alignItems: 'center',
    justifyContent: 'center',
  },
  emptyListText: {
    color: '#AAAAAA',
    fontFamily: FONTS.pretendard.light,
    fontSize: 16,
    textAlign: 'center',
  },
  tabIconActive: {
    tintColor: '#D9C091',
  },
  tabIconInactive: {
    tintColor: '#767676',
  },
  tabTextActive: {
    color: '#D9C091',
  },
  tabTextInactive: {
    color: '#767676',
  },
  container: {
    flex: 1,
    backgroundColor: '#000000',
  },
  scrollView: {
    flex: 1,
  },
  scrollViewContent: {
    paddingHorizontal: 20,
    paddingBottom: 100, // 为底部导航栏留出空间
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorContainer: {
    padding: 20,
    backgroundColor: 'rgba(255, 0, 0, 0.1)',
    borderRadius: 10,
    margin: 20,
  },
  errorText: {
    color: '#FF0000',
    fontFamily: FONTS.pretendard.light,
    fontSize: 16,
    textAlign: 'center',
  },
  headerRight: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  searchButton: {
    marginRight: 15,
  },
  menuButton: {
    marginRight: 5,
  },
  icon: {
    width: 24,
    height: 24,
    tintColor: '#FFFFFF',
  },
  profileContainer: {
    alignItems: 'center',
    marginTop: 30,
    marginBottom: 30,
  },
  profileBorder: {
    width: 160,
    height: 160,
    borderRadius: 80,
    alignItems: 'center',
    justifyContent: 'center',
  },
  profileImageContainer: {
    width: 150,
    height: 150,
    borderRadius: 75,
    overflow: 'hidden',
    backgroundColor: '#000000',
  },
  profileImage: {
    width: '100%',
    height: '100%',
  },
  nameText: {
    color: '#FFFFFF',
    fontFamily: FONTS.bebasNeue.regular,
    fontSize: 28,
    letterSpacing: 1,
    marginTop: 20,
  },
  messageListContainer: {
    width: '100%',
    marginBottom: 30,
  },
  messageListTitle: {
    color: '#D9C091',
    fontSize: 20,
    fontFamily: FONTS.bebasNeue.regular,
    letterSpacing: 1,
    textAlign: 'center',
    marginBottom: 15,
  },
  messageItem: {
    flexDirection: 'row',
    borderRadius: 15,
    marginBottom: 15,
    overflow: 'hidden',
    height: 100, // 固定高度确保所有卡片高度一致
    position: 'relative',
  },
  messageImage: {
    width: 80,
    height: 80,
    borderRadius: 40,
    margin: 10,
  },
  messageContent: {
    flex: 1,
    padding: 15,
    justifyContent: 'center',
  },
  messageDate: {
    color: '#FFFFFF',
    fontSize: 16,
    fontFamily: FONTS.pretendard.light,
    fontWeight: 'bold',
  },
  messageName: {
    color: '#FFFFFF',
    fontSize: 20,
    fontFamily: FONTS.pretendard.light,
    fontWeight: 'bold',
    marginTop: 5,
  },
  fullOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 10,
  },
  statusText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontFamily: FONTS.pretendard.light,
    fontWeight: 'bold',
  },
  countdownText: {
    color: '#FF4D4D',
    fontSize: 24,
    fontFamily: FONTS.bebasNeue.regular,
    marginVertical: 5,
  },
  overlayNameText: {
    color: '#AAAAAA',
    fontSize: 14,
    fontFamily: FONTS.pretendard.light,
  },
  rejectText: {
    color: '#FF4D4D',
    fontSize: 24,
    fontFamily: FONTS.pretendard.light,
    fontWeight: 'bold',
    marginVertical: 5,
  },
  addVoiceMessageContainer: {
    width: '100%',
    alignItems: 'center',
  },
  addVoiceMessageTitle: {
    color: '#FFFFFF',
    fontFamily: FONTS.bebasNeue.regular,
    fontSize: 24,
    letterSpacing: 1,
    marginBottom: 30,
  },
  addButton: {
    width: 80,
    height: 80,
    borderRadius: 40,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 30,
  },
  addButtonIcon: {
    width: 40,
    height: 40,
    tintColor: '#FFFFFF',
  },
  bottomTabBar: {
    flexDirection: 'row',
    backgroundColor: '#000000',
    borderTopWidth: 0.01,
    borderTopColor: '#333333',
    height: 60,
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
  },
  tabItem: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  tabIcon: {
    width: 20,
    height: 20,
    marginBottom: 5,
  },
  tabText: {
    fontFamily: 'Pretendard-Light',
    fontSize: 12,
  },
});

export default VoiceMessageScreen;
