import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Image,
  TouchableOpacity,
  StatusBar,
  Dimensions,
  TextInput,
  ScrollView,
} from 'react-native';
import { FONTS } from '../assets/fonts';
import { IMAGES } from '../assets/images';
import { Header } from '../components';
import { get } from '../services/api/apiService';
import { useNavigation, useRoute, RouteProp } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import LinearGradient from 'react-native-linear-gradient';
import { useTranslation } from 'react-i18next';

// 定义导航参数类型
type RootStackParamList = {
  Home: undefined;
  VoiceMessage: {
    id: number;
    name: string;
    image: any;
    nm?: string;
  };
  SelectVoiceMessage: {
    id: number;
    name: string;
    image: any;
    nm?: string;
  };
  RecommendVoiceMessage: {
    id: number;
    name: string;
    image: any;
    nm?: string;
  };
  WriteVoiceMessage: {
    id: number;
    name: string;
    image: any;
    nm?: string;
    categoryNm?: string;
  };
  VoiceMessageConfirm: undefined;
};

type RecommendVoiceMessageRouteProp = RouteProp<
  { RecommendVoiceMessage: { id: number; name: string; image: any; nm?: string } },
  'RecommendVoiceMessage'
>;

type RecommendVoiceMessageScreenNavigationProp = StackNavigationProp<RootStackParamList, 'RecommendVoiceMessage'>;

const { width } = Dimensions.get('window');

const RecommendVoiceMessageScreen = () => {
  const navigation = useNavigation<RecommendVoiceMessageScreenNavigationProp>();
  const route = useRoute<RecommendVoiceMessageRouteProp>();
  const { t } = useTranslation();

  // 从路由参数中获取名人信息
  const { id, name, image, nm } = route.params || {
    id: 1,
    name: 'ARINDA GRANDE',
    image: IMAGES.defaultAvatar,
    nm: '',
  };

  // 状态管理
  const [recipient, setRecipient] = useState('');
  const [categories, setCategories] = useState<any[]>([]);
  const [expandedCategory, setExpandedCategory] = useState<string>('');
  const [selectedCategory, setSelectedCategory] = useState<string>('');
  const [selectedSubOccasion, setSelectedSubOccasion] = useState<string>('');
  const [selectedSubOccasionObj, setSelectedSubOccasionObj] = useState<any>(null);
  const [alertVisible, setAlertVisible] = useState(false);
  const [alertMessage, setAlertMessage] = useState('');

  // 处理搜索按钮点击
  const handleSearchPress = () => {
    console.log('Search pressed in RecommendVoiceMessageScreen');
  };

  // 处理菜单按钮点击
  const handleMenuPress = () => {
    console.log('Menu pressed in RecommendVoiceMessageScreen');
    navigation.navigate('Menu' as never);
  };

  // 显示提示弹窗
  const showAlert = (message: string) => {
    setAlertMessage(message);
    setAlertVisible(true);
  };

  // 处理下一步按钮点击
  const handleNextPress = () => {
    // 检查收件人名称是否为空
    if (!recipient.trim()) {
      showAlert(t('voiceMessage.enterNameAtLeastOneLetter'));
      return;
    }

    // 使用选中的子场合对象的nm值
    const categoryNm = selectedSubOccasionObj?.nm || '';

    console.log('Next pressed with:', {
      recipient,
      selectedSubOccasion,
      selectedCategory,
      categoryNm,
      selectedSubOccasionObj,
    });

    // 导航到WriteVoiceMessage页面，并传递所有必要参数
    navigation.navigate('WriteVoiceMessage', {
      id,
      name,
      image,
      nm,
      categoryNm, // 使用选中的子场合的nm值
    });
  };

  // 处理子场合选择
  const handleSubOccasionPress = (subOccasion: any) => {
    setSelectedSubOccasion(subOccasion.name);
    setSelectedSubOccasionObj(subOccasion);
  };

  // 处理类别选择
  const handleCategoryPress = (category: string) => {
    // 如果点击已经展开的类别，则收起它
    if (expandedCategory === category) {
      setExpandedCategory('');
    } else {
      // 否则展开点击的类别
      setExpandedCategory(category);
      setSelectedCategory(category);
    }
  };

  // 加载场景树数据
  const loadVoiceMessageSceneTree = useCallback(async () => {
    try {
      const response = await get('/voiceMessageScene/myTree');

      if (response.data && response.data.code === '200' && response.data.data) {
        // 按照指定的顺序排序类别：CELEBRATE, COMFORT, THANKS
        const orderedCategories: any[] = [];
        const data = response.data.data;
        const celebrateCategory = data.find((cat: any) => cat.name === 'CELEBRATE');
        const comfortCategory = data.find((cat: any) => cat.name === 'COMFORT');
        const thanksCategory = data.find((cat: any) => cat.name === 'THANKS');

        if (celebrateCategory) {
          orderedCategories.push(celebrateCategory);
        }
        if (comfortCategory) {
          orderedCategories.push(comfortCategory);
        }
        if (thanksCategory) {
          orderedCategories.push(thanksCategory);
        }

        // 添加其他可能存在的类别
        data.forEach((cat: any) => {
          if (!['CELEBRATE', 'COMFORT', 'THANKS'].includes(cat.name)) {
            orderedCategories.push(cat);
          }
        });

        // 只保存categories数据
        setCategories(orderedCategories);

        // 默认选择CELEBRATE类别
        const defaultCategory = celebrateCategory || (orderedCategories.length > 0 ? orderedCategories[0] : null);

        if (defaultCategory) {
          setSelectedCategory(defaultCategory.name);
          setExpandedCategory(defaultCategory.name); // 默认展开CELEBRATE类别

          // 设置选中的子场合
          if (defaultCategory.list && defaultCategory.list.length > 0) {
            const defaultSubOccasion = defaultCategory.list[0];
            setSelectedSubOccasion(defaultSubOccasion.name);
            setSelectedSubOccasionObj(defaultSubOccasion);
          }
        }
      }
    } catch (error) {
      console.error(t('voiceMessage.failedToGetSceneTreeData'), error);
    }
  }, [t]);

  // 组件加载时获取数据
  useEffect(() => {
    loadVoiceMessageSceneTree();
  }, [loadVoiceMessageSceneTree]);

  // 当选择类别变化时，更新选中的子场合
  useEffect(() => {
    if (categories.length > 0 && selectedCategory) {
      const category = categories.find((cat: any) => cat.name === selectedCategory);
      if (category && category.list && category.list.length > 0) {
        const firstSubOccasion = category.list[0];
        setSelectedSubOccasion(firstSubOccasion.name);
        setSelectedSubOccasionObj(firstSubOccasion);
      }
    }
  }, [selectedCategory, categories]);

  // 自定义右侧组件
  const headerRightComponent = (
    <View style={styles.headerRight}>
      <TouchableOpacity style={styles.searchButton} onPress={handleSearchPress}>
        <Image source={IMAGES.searchIcon} style={styles.icon} />
      </TouchableOpacity>
      <TouchableOpacity style={styles.menuButton} onPress={handleMenuPress}>
        <Image source={IMAGES.menuIcon} style={styles.icon} />
      </TouchableOpacity>
    </View>
  );

  // 不再需要硬编码的场合选项，现在从API获取

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="transparent" translucent={true} />

      {/* 使用全局 Header 组件 */}
      <Header title={t('voiceMessage.title')} rightComponent={headerRightComponent} />

      {/* 自定义提示弹窗 */}
      {alertVisible && (
        <View style={styles.alertOverlay}>
          <View style={styles.alertContainer}>
            <Text style={styles.alertMessage}>{alertMessage}</Text>
            <View style={styles.alertDivider} />
            <TouchableOpacity
              style={styles.alertButton}
              onPress={() => setAlertVisible(false)}
            >
              <Text style={styles.alertButtonText}>{t('common.ok')}</Text>
            </TouchableOpacity>
          </View>
        </View>
      )}

      <ScrollView 
        style={styles.scrollView}
        contentContainerStyle={styles.scrollViewContent}
      >
        <View style={styles.content}>
          {/* 名人头像 */}
          <View style={styles.mainContent}>
            <View style={styles.profileContainer}>
              <LinearGradient
                colors={['#00C853', '#2980B9', '#8E44AD', '#2980B9']}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 1 }}
                style={styles.profileBorder}
              >
                <View style={styles.profileImageContainer}>
                  <Image
                    source={image}
                    style={styles.profileImage}
                    resizeMode="cover"
                  />
                </View>
              </LinearGradient>
              <Text style={styles.nameText}>{name}</Text>
            </View>

            {/* 收件人部分 */}
            <View style={styles.recipientSection}>
              <View style={styles.toContainer}>
                <Image
                  source={require('../assets/images/home/<USER>')}
                  style={styles.toIcon}
                />
                <Text style={styles.toText}>{t('voiceMessage.to')}</Text>
              </View>

              <View style={styles.recipientInputContainer}>
                <TextInput
                  style={styles.recipientInput}
                  placeholder=""
                  placeholderTextColor="#666666"
                  value={recipient}
                  onChangeText={setRecipient}
                  maxLength={20}
                />
                <Text style={styles.charCount}>[{recipient.length}/20]</Text>
              </View>

              <Text style={styles.recipientHintText}>
                {t('voiceMessage.enterNameOrNickname')}
              </Text>
            </View>

            {/* 选择场合部分 */}
            <View style={styles.occasionSection}>
              <View style={styles.occasionTitleContainer}>
                <Image
                  source={require('../assets/images/home/<USER>')}
                  style={styles.occasionIcon}
                />
                <Text style={styles.occasionTitleText}>{t('voiceMessage.selectOccasion')}</Text>
              </View>
            </View>

            {/* 类别选择部分 */}
            <View style={styles.categorySection}>
              {categories.map((category, index) => {
                // 根据类别名称设置背景色和图标
                const bgColor =
                  category.name === 'CELEBRATE' ? '#7246BB' :
                  category.name === 'COMFORT' ? '#0896A6' :
                  category.name === 'THANKS' ? '#072622' :
                  '#7246BB'; // 默认颜色

                const iconSource =
                  category.name === 'COMFORT' ? require('../assets/images/home/<USER>') :
                  category.name === 'THANKS' ? require('../assets/images/home/<USER>') :
                  require('../assets/images/home/<USER>');

                const isExpanded = expandedCategory === category.name;

                return (
                  <View key={category.nm || index}>
                    <TouchableOpacity
                      style={[
                        styles.categoryButton,
                        { backgroundColor: bgColor },
                        selectedCategory === category.name && styles.categoryButtonSelected,
                      ]}
                      onPress={() => handleCategoryPress(category.name)}
                    >
                      <Image
                        source={iconSource}
                        style={styles.categoryIcon}
                      />
                      <Text style={styles.categoryText}>{category.name}</Text>
                      <Image
                        source={require('../assets/images/home/<USER>')}
                        style={[
                          styles.categoryArrow,
                          isExpanded && { transform: [{ rotate: '90deg' }] },
                        ]}
                      />
                    </TouchableOpacity>

                    {/* 展开的子项列表 */}
                    {isExpanded && category.list && (
                      <View style={styles.subOccasionList}>
                        {category.list.map((subItem: any, subIndex: number) => (
                          <TouchableOpacity
                            key={subItem.nm || subIndex}
                            style={styles.occasionOption}
                            onPress={() => handleSubOccasionPress(subItem)}
                          >
                            <View style={styles.radioContainer}>
                              <View
                                style={[
                                  styles.radioOuter,
                                  selectedSubOccasion === subItem.name && styles.radioOuterSelected,
                                ]}
                              >
                                {selectedSubOccasion === subItem.name && (
                                  <View style={styles.radioInner} />
                                )}
                              </View>
                            </View>
                            <Text
                              style={[
                                styles.occasionOptionText,
                                selectedSubOccasion === subItem.name && styles.occasionOptionTextSelected,
                              ]}
                            >
                              {subItem.name}
                            </Text>
                          </TouchableOpacity>
                        ))}
                      </View>
                    )}
                  </View>
                );
              })}
            </View>
          </View>

          {/* Next 按钮 */}
          <View style={styles.buttonContainer}>
            <TouchableOpacity
              style={styles.nextButton}
              onPress={handleNextPress}
            >
              <Text style={styles.nextButtonText}>{t('common.next')}</Text>
              <Image
                source={require('../assets/images/home/<USER>')}
                style={styles.nextButtonIcon}
              />
            </TouchableOpacity>
          </View>
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  // 弹窗样式
  alertOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 9999,
    elevation: 10,
  },
  alertContainer: {
    width: width * 0.85,
    backgroundColor: '#222222',
    borderRadius: 10,
    overflow: 'hidden',
  },
  alertMessage: {
    color: '#FF0066',
    fontSize: 16,
    fontFamily: FONTS.pretendard.light,
    textAlign: 'center',
    paddingVertical: 30,
    paddingHorizontal: 20,
  },
  alertDivider: {
    height: 1,
    backgroundColor: '#444444',
    marginHorizontal: 0,
  },
  alertButton: {
    backgroundColor: '#007AFF',
    paddingVertical: 15,
    alignItems: 'center',
    margin: 15,
    borderRadius: 10,
  },
  alertButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontFamily: FONTS.pretendard.light,
    fontWeight: '500',
  },
  container: {
    flex: 1,
    backgroundColor: '#000000',
  },
  headerRight: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  searchButton: {
    marginRight: 15,
  },
  menuButton: {
    marginRight: 5,
  },
  icon: {
    width: 24,
    height: 24,
    tintColor: '#FFFFFF',
  },
  scrollView: {
    flex: 1,
  },
  scrollViewContent: {
    flexGrow: 1,
  },
  content: {
    flex: 1,
    minHeight: '100%',
  },
  mainContent: {
    flex: 1,
  },
  profileContainer: {
    alignItems: 'center',
    marginTop: 20,
    marginBottom: 20,
  },
  profileBorder: {
    width: 100,
    height: 100,
    borderRadius: 50,
    alignItems: 'center',
    justifyContent: 'center',
  },
  profileImageContainer: {
    width: 90,
    height: 90,
    borderRadius: 45,
    overflow: 'hidden',
    backgroundColor: '#000000',
  },
  profileImage: {
    width: '100%',
    height: '100%',
  },
  nameText: {
    color: '#FFFFFF',
    fontFamily: FONTS.bebasNeue.regular,
    fontSize: 24,
    letterSpacing: 1,
    marginTop: 10,
    
  },
  recipientSection: {
    alignItems: 'center',
    marginBottom: 30,
  },
  toContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 10,
  },
  toIcon: {
    width: 20,
    height: 20,
    tintColor: '#FFFFFF',
    marginRight: 8,
  },
  toText: {
    color: '#FFFFFF',
    fontFamily: FONTS.pretendard.light,
    fontSize: 18,
  },
  recipientInputContainer: {
    width: '80%',
    borderBottomWidth: 1,
    borderBottomColor: '#333333',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingBottom: 5,
    marginBottom: 15,
  },
  recipientInput: {
    color: '#FFFFFF',
    fontFamily: FONTS.pretendard.light,
    fontSize: 16,
    textAlign: 'center',
    flex: 1,
  },
  charCount: {
    color: '#666666',
    fontFamily: FONTS.pretendard.light,
    fontSize: 12,
  },
  recipientHintText: {
    color: '#FFFFFF',
    fontFamily: FONTS.pretendard.light,
    fontSize: 14,
    textAlign: 'center',
    width: '80%',
  },
  occasionSection: {
    marginBottom: 30,
  },
  occasionTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 15,
  },
  occasionIcon: {
    width: 20,
    height: 20,
    tintColor: '#D9C091',
    marginRight: 8,
  },
  occasionTitleText: {
    color: '#D9C091',
    fontFamily: FONTS.bebasNeue.regular,
    fontSize: 20,
    letterSpacing: 1,
  },
  occasionDropdown: {
    marginHorizontal: 20,
    borderRadius: 10,
    overflow: 'hidden',
  },
  occasionDropdownHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 15,
  },
  occasionDropdownIcon: {
    width: 20,
    height: 20,
    tintColor: '#FFFFFF',
    marginRight: 10,
  },
  occasionDropdownText: {
    color: '#FFFFFF',
    fontFamily: FONTS.bebasNeue.regular,
    fontSize: 18,
    letterSpacing: 1,
    flex: 1,
  },
  dropdownArrow: {
    width: 15,
    height: 15,
    tintColor: '#FFFFFF',
  },
  dropdownArrowUp: {
    transform: [{ rotate: '180deg' }],
  },
  subOccasionList: {
    overflow: 'hidden',
    marginHorizontal: 20,
    marginTop: 5,
    paddingVertical: 10,
  },
  occasionDropdownContent: {
    marginHorizontal: 20,
    backgroundColor: '#111111',
    borderBottomLeftRadius: 5,
    borderBottomRightRadius: 5,
    overflow: 'hidden',
    marginTop: -5,
    paddingVertical: 10,
  },
  occasionOption: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 10,
    paddingHorizontal: 15,
  },
  radioContainer: {
    marginRight: 10,
  },
  radioOuter: {
    width: 20,
    height: 20,
    borderRadius: 10,
    borderWidth: 1,
    borderColor: '#FFFFFF',
    alignItems: 'center',
    justifyContent: 'center',
  },
  radioOuterSelected: {
    borderColor: '#6A4BFF',
  },
  radioInner: {
    width: 10,
    height: 10,
    borderRadius: 5,
    backgroundColor: '#6A4BFF',
  },
  occasionOptionText: {
    color: '#FFFFFF',
    fontFamily: FONTS.pretendard.light,
    fontSize: 16,
  },
  occasionOptionTextSelected: {
    color: '#6A4BFF',
  },
  categorySection: {
    marginHorizontal: 20,
    marginBottom: 30,
  },
  categoryButton: {
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: 5,
    padding: 15,
    marginBottom: 10,
  },
  categoryButtonSelected: {
    opacity: 0.8,
  },
  categoryIcon: {
    width: 20,
    height: 20,
    tintColor: '#FFFFFF',
    marginRight: 10,
    resizeMode: 'contain'
  },
  categoryText: {
    color: '#FFFFFF',
    fontFamily: FONTS.bebasNeue.regular,
    fontSize: 18,
    letterSpacing: 1,
    flex: 1,
  },
  categoryArrow: {
    width: 15,
    height: 15,
    tintColor: '#FFFFFF',
  },
  buttonContainer: {
    width: '100%',
    paddingHorizontal: 20,
    marginTop: 'auto',
    marginBottom: 20,
  },
  nextButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 15,
    paddingHorizontal: 40,
    borderRadius: 30,
    backgroundColor: 'rgba(255, 0, 74, 0.1)',
  },
  nextButtonText: {
    color: '#FF004A',
    fontFamily: FONTS.bebasNeue.regular,
    fontSize: 20,
    letterSpacing: 1,
    marginRight: 5,
  },
  nextButtonIcon: {
    width: 15,
    height: 15,
    tintColor: '#FF004A',
  },
});

export default RecommendVoiceMessageScreen;
