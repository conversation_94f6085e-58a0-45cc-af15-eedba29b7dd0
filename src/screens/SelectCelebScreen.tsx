import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Image,
  TouchableOpacity,
  ScrollView,
  StatusBar,
  ActivityIndicator,
} from 'react-native';
import { FONTS } from '../assets/fonts';
import { IMAGES } from '../assets/images';
import { Header } from '../components';
import { useNavigation, useRoute, RouteProp } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { get } from '../services/api/apiService';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useTranslation } from 'react-i18next';

// 定义导航参数类型
type TabType = 'alarm' | 'message' | 'coupon';

// 自定义分类类型
type CategoryItem = {
  id: string;
  name: string;
  icon: any;
  nm?: string;
  originalData?: any;
};

// 扩展名人数据类型
interface ExtendedCelebrityItem {
  id: number;
  type: string;
  name: string;
  image: any;
  nm: string;
  category?: string;
  headImage?: string;
  collectStatus?: boolean;
  subscriptStatus?: boolean;
  categoryExplain?: string;
  reserve3?: string;
  reserve4?: string;
  originalData?: any;
}

type RootStackParamList = {
  Home: undefined;
  Celebrity: undefined;
  Subscription: undefined;
  Inbox: undefined;
  SelectCeleb: {
    sourceTab?: TabType;
  };
  Alarm: {
    id: number;
    name: string;
    type: string;
    image: any;
    nm?: string;
  };
  WriteVoiceMessage: {
    id: number;
    name: string;
    type: string;
    image: any;
    nm: number | string;
    categoryNm?: string;
  };
  SelectVoiceMessage: {
    id: number;
    name: string;
    type: string;
    image: any;
    nm: number | string;
    categoryNm?: string;
  };
  MainTabs: {
    screen: string;
  };
  Profile: undefined;
};

type SelectCelebScreenNavigationProp = StackNavigationProp<RootStackParamList, 'SelectCeleb'>;
type SelectCelebScreenRouteProp = RouteProp<RootStackParamList, 'SelectCeleb'>;

// 初始化空数组
const initialCelebrityData: ExtendedCelebrityItem[] = [];

const SelectCelebScreen = () => {
  const navigation = useNavigation<SelectCelebScreenNavigationProp>();
  const route = useRoute<SelectCelebScreenRouteProp>();
  const { t } = useTranslation();
  const [selectedCategory, setSelectedCategory] = useState('total');
  const [selectedCategoryId, setSelectedCategoryId] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [celebData, setCelebData] = useState<ExtendedCelebrityItem[]>(initialCelebrityData);
  const [sourceTab, setSourceTab] = useState<TabType | undefined>(undefined);

  // 获取路由参数
  useEffect(() => {
    if (route.params?.sourceTab) {
      setSourceTab(route.params.sourceTab);
      console.log('Source tab:', route.params.sourceTab);
    }
  }, [route.params]);

  // 获取标签页数据
  const [customCategories, setCustomCategories] = useState<CategoryItem[]>([]);

  useEffect(() => {
    const fetchCategoryTabs = async () => {
        try {
          setIsLoading(true);
          const response = await get('/peopleCategory/all');
          if (response.data && response.data.code === '200' && response.data.data) {
            const apiCategories = response.data.data.map((item: any) => ({
              id: item.category.toLowerCase(),
              name: item.category.toUpperCase(),
              icon: item.reserve3 ? { uri: item.reserve3 } : IMAGES.categoryIcons.all,
              nm: item.nm,
              originalData: item, // 保存原始数据，以便后续使用
            }));
            setCustomCategories(apiCategories);
          }
        } catch (err) {
          console.error('Error fetching category tabs data:', err);
        } finally {
          setIsLoading(false);
        }
    };

    fetchCategoryTabs();
  }, [sourceTab]);

  // 获取用户信息
  const [userInfo, setUserInfo] = useState<any>(null);

  // 获取名人列表数据
  const fetchCelebrityData = useCallback(async (categoryId?: string) => {
    try {
      setIsLoading(true);

      // 构建请求参数
      const params: any = {
        curAccount: userInfo?.nm || '',
      };
      if(categoryId) {
        params.categoryId = categoryId;
      }

      // 调用接口获取名人列表数据
      const response = await get('/voicePerson/commonList', params);
      console.log('Celebrity list data:', selectedCategoryId,response.data);

      if (response.data && response.data.code === '200' && response.data.data) {
        // 将API返回的数据转换为ExtendedCelebrityItem格式
        const formattedData: ExtendedCelebrityItem[] = response.data.data.map((item: any) => ({
          id: item.id || Math.random(),
          type: item.category || 'UNKNOWN',
          name: item.name || 'Unknown',
          image: item.headImage ? { uri: item.headImage } : IMAGES.defaultAvatar,
          nm: item.nm || '',
          category: item.category,
          headImage: item.headImage,
          collectStatus: item.collectStatus,
          subscriptStatus: item.subscriptStatus,
          categoryExplain: item.categoryExplain,
          reserve3: item.reserve3,
          reserve4: item.reserve4,
          originalData: item,
        }));

        setCelebData(formattedData);
      } else {
        // 如果接口返回错误或没有数据，使用空数组
        setCelebData(initialCelebrityData);
      }
    } catch (err) {
      console.error(t('logs.errorFetchingCelebrityData'), err);
      // 发生错误时使用空数组
      setCelebData(initialCelebrityData);
    } finally {
      setIsLoading(false);
    }
  }, [userInfo, t, selectedCategoryId]);

  // 获取首页数据
  useEffect(() => {
    const getUserInfo = async () => {
      try {
        const userInfoStr = await AsyncStorage.getItem('@aivgen:user_info');
        if (userInfoStr) {
          const info = JSON.parse(userInfoStr);
          setUserInfo(info);
        }
      } catch (err) {
        console.error(t('logs.errorGettingUserInfo'), err);
      }
    };

    getUserInfo();
  }, [t]);

  // 根据来源标签页和用户信息获取名人列表数据
  useEffect(() => {
    if (userInfo) {
      fetchCelebrityData();
    }
  }, [userInfo, sourceTab, fetchCelebrityData]);

  // 当选中的分类变化时，重新获取名人列表数据
  useEffect(() => {
    if (userInfo && selectedCategory) {
      fetchCelebrityData(selectedCategoryId);
    }
  }, [selectedCategory, userInfo, fetchCelebrityData, selectedCategoryId]);

  // 根据选择的分类过滤数据
  const filteredData = selectedCategory === 'total'
    ? celebData
    : celebData.filter(item =>
        item.type.toLowerCase() === selectedCategory.toLowerCase()
      );

  // 处理名人选择
  const handleCelebSelect = (celeb: any) => {
    console.log('Selected celeb:', celeb.name, 'Source tab:', sourceTab);

    // 根据来源标签页导航到不同的页面
    if (sourceTab === 'message') {
      // 查找当前选中的分类，获取其id值
      const selectedCategoryObj = customCategories.find(cat => cat.id === selectedCategory);
      const categoryId = selectedCategoryObj?.originalData?.id || '';

      console.log('Selected category:', selectedCategory, 'Category ID:', categoryId);

      // 如果来源是message标签页，则导航到SelectVoiceMessage页面
      navigation.navigate('SelectVoiceMessage', {
        id: celeb.id,
        name: celeb.name,
        type: celeb.type,
        image: celeb.image,
        nm: celeb.nm || celeb.id, // 使用nm字段或id作为备选
        categoryNm: categoryId, // 传递分类的id值
      });
    } else {
      // 否则导航到闹钟设置页面
      navigation.navigate('Alarm', {
        id: celeb.id,
        name: celeb.name,
        type: celeb.type,
        image: celeb.image,
        nm: celeb.nm || celeb.id,
      });
    }
  };

  // 处理分类选择
  const handleCategorySelect = (categoryId: string, categoryNm?: string) => {
    setSelectedCategory(categoryId);
    if (categoryNm) {
      setSelectedCategoryId(categoryNm);
    }
  };

  // 自定义右侧组件
  const headerRightComponent = (
    <View style={styles.headerRight}>
      <TouchableOpacity style={styles.searchButton}>
        <Image source={IMAGES.searchIcon} style={styles.icon} />
      </TouchableOpacity>
      <TouchableOpacity style={styles.menuButton}>
        <Image source={IMAGES.menuIcon} style={styles.icon} />
      </TouchableOpacity>
    </View>
  );

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="transparent" translucent={true} />

      {/* 使用全局 Header 组件 */}
      <Header title={t('celeb.selectCeleb')} rightComponent={headerRightComponent} />

      {/* 分类选项卡 - 可水平滚动 */}
      <View style={styles.categoriesContainer}>
        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={styles.categoriesScrollContent}
        >
          {customCategories.map(category => (
            <TouchableOpacity
              key={category.id}
              style={[
                styles.categoryTab,
                selectedCategory === category.id && styles.selectedCategoryTab
              ]}
              onPress={() => handleCategorySelect(category.id, category.nm)}
            >
              <Image
                source={category.icon}
                style={[
                  styles.categoryIcon,
                  selectedCategory === category.id && styles.selectedCategoryIcon
                ]}
              />
              <Text
                style={[
                  styles.categoryText,
                  selectedCategory === category.id && styles.selectedCategoryText
                ]}
              >
                {category.name}
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>
      </View>

      {/* 标题 */}
      <View style={styles.titleContainer}>
        <Text style={styles.titleText}>
          {sourceTab === 'message'
            ? t('celeb.selectCelebForVoiceMessage')
            : t('celeb.selectCelebForVoiceAlarm')}
        </Text>
      </View>

      {/* 名人列表 */}
      {isLoading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#D9C091" />
        </View>
      ) : (
        <ScrollView style={styles.scrollView}>
          {filteredData.map(item => (
            <TouchableOpacity
              key={item.id}
              style={styles.celebrityItem}
              onPress={() => handleCelebSelect(item)}
            >
              <Image source={item.image} style={styles.celebrityImage} />
              <View style={styles.celebrityInfo}>
                <Text style={styles.celebrityType}>{item.type}</Text>
                <Text style={styles.celebrityName}>{item.name}</Text>
              </View>
            </TouchableOpacity>
          ))}

          {/* 底部间距 */}
          <View style={styles.bottomPadding} />
        </ScrollView>
      )}

      {/* 底部导航栏 */}
      <View style={styles.bottomTabBar}>
        <TouchableOpacity
          style={styles.tabItem}
          onPress={() => navigation.navigate('MainTabs', { screen: 'Home' })}
        >
          <Image
            source={IMAGES.tabIcons.home}
            style={[styles.tabIcon, { tintColor: '#767676' }]}
          />
          <Text style={[styles.tabText, { color: '#767676' }]}>HOME</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.tabItem}
          onPress={() => navigation.navigate('MainTabs', { screen: 'Subscription' })}
        >
          <Image
            source={IMAGES.tabIcons.subscript}
            style={[styles.tabIcon, { tintColor: '#767676' }]}
          />
          <Text style={[styles.tabText, { color: '#767676' }]}>SUBSCRIPTION</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.tabItem}
          onPress={() => navigation.navigate('MainTabs', { screen: 'Inbox' })}
        >
          <Image
            source={IMAGES.tabIcons.inbox}
            style={[styles.tabIcon, { tintColor: '#767676' }]}
          />
          <Text style={[styles.tabText, { color: '#767676' }]}>INBOX</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.tabItem}
          onPress={() => navigation.navigate('MainTabs', { screen: 'Profile' })}
        >
          <Image
            source={IMAGES.tabIcons.my}
            style={[styles.tabIcon, { tintColor: '#767676' }]}
          />
          <Text style={[styles.tabText, { color: '#767676' }]}>MY</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000000',
  },
  headerRight: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  searchButton: {
    marginRight: 15,
  },
  menuButton: {},
  icon: {
    width: 24,
    height: 24,
    tintColor: '#FFFFFF',
  },
  categoriesContainer: {
    borderBottomWidth: 1,
    borderBottomColor: '#333333',
  },
  categoriesScrollContent: {
    paddingHorizontal: 10,
  },
  categoryTab: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 15,
    paddingHorizontal: 15,
  },
  selectedCategoryTab: {
    borderBottomWidth: 2,
    borderBottomColor: '#D9C091',
  },
  categoryIcon: {
    width: 20,
    height: 20,
    marginRight: 5,
    tintColor: '#767676',
  },
  selectedCategoryIcon: {
    tintColor: '#D9C091',
  },
  categoryText: {
    color: '#767676',
    fontSize: 14,
    fontFamily: FONTS.pretendard.light,
  },
  selectedCategoryText: {
    color: '#D9C091',
  },
  titleContainer: {
    paddingVertical: 20,
    paddingHorizontal: 20,
    alignItems: 'center',
  },
  titleText: {
    color: '#FFFFFF',
    fontSize: 20,
    fontFamily: FONTS.bebasNeue.regular,
    letterSpacing: 1,
    textAlign: 'center',
  },
  scrollView: {
    flex: 1,
    paddingHorizontal: 20,
  },
  celebrityItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#333333',
  },
  celebrityImage: {
    width: 80,
    height: 80,
    borderRadius: 5,
  },
  celebrityInfo: {
    marginLeft: 15,
    justifyContent: 'center',
  },
  celebrityType: {
    color: '#999999',
    fontFamily: FONTS.bebasNeue.regular,
    fontSize: 14,
    marginBottom: 5,
  },
  celebrityName: {
    color: '#FFFFFF',
    fontFamily: FONTS.bebasNeue.regular,
    fontSize: 22,
    letterSpacing: 1,
  },
  bottomPadding: {
    height: 80,
  },
  loadingContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 50,
  },
  bottomTabBar: {
    flexDirection: 'row',
    backgroundColor: '#000000',
    borderTopWidth: 0.01,
    borderTopColor: '#333333',
    height: 60,
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
  },
  tabItem: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  tabIcon: {
    width: 20,
    height: 20,
    marginBottom: 5,
  },
  tabText: {
    fontFamily: 'Pretendard-Light',
    fontSize: 12,
  },
});

export default SelectCelebScreen;
