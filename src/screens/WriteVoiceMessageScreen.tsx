import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Image,
  TouchableOpacity,
  StatusBar,
  Dimensions,
  TextInput,
  ScrollView,
  ActivityIndicator,
  Platform,
} from 'react-native';
import Carousel from 'react-native-reanimated-carousel';
import { FONTS } from '../assets/fonts';
import { IMAGES } from '../assets/images';
import { Header, SimpleFullTextModal } from '../components';
import { get, post } from '../services/api/apiService';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useNavigation, useRoute, RouteProp } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import LinearGradient from 'react-native-linear-gradient';
import { useTranslation } from 'react-i18next';
import * as RNIap from 'react-native-iap';
import { showSuccess, showError } from '../services/toast';

// 定义导航参数类型
type RootStackParamList = {
  Home: undefined;
  VoiceMessage: {
    id: number;
    name: string;
    image: any;
    nm?: string;
  };
  SelectVoiceMessage: {
    id: number;
    name: string;
    image: any;
    nm?: string;
  };
  WriteVoiceMessage: {
    id: number;
    name: string;
    image: any;
    nm?: string;
    categoryNm?: string;
  };
  VoiceMessageConfirm: {
    id?: string;
    source?: 'voiceMessage' | 'alarm';
  };
};

type WriteVoiceMessageRouteProp = RouteProp<
  { WriteVoiceMessage: { id: number; name: string; image: any; nm?: string; categoryNm?: string } },
  'WriteVoiceMessage'
>;

type WriteVoiceMessageScreenNavigationProp = StackNavigationProp<RootStackParamList, 'WriteVoiceMessage'>;

const { width } = Dimensions.get('window');

const WriteVoiceMessageScreen = () => {
  const navigation = useNavigation<WriteVoiceMessageScreenNavigationProp>();
  const route = useRoute<WriteVoiceMessageRouteProp>();
  const { t } = useTranslation();

  // 从路由参数中获取名人信息
  const { name, image, nm, categoryNm } = route.params || {
    name: 'ARINDA GRANDE',
    image: IMAGES.defaultAvatar,
    nm: '',
    categoryNm: '',
  };

  // 状态管理
  const [message, setMessage] = useState('');
  const [selectedDuration, setSelectedDuration] = useState('');
  const [selectedPrice, setSelectedPrice] = useState('');
  const [activeSlide, setActiveSlide] = useState(0);
  const [celebrityData, setCelebrityData] = useState<any>(null);
  const [packageData, setPackageData] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showConfirmModal, setShowConfirmModal] = useState(false);
  const [selectedBackgroundIndex, setSelectedBackgroundIndex] = useState<number | null>(null);
  const [alertVisible, setAlertVisible] = useState(false);
  const [categoryData, setCategoryData] = useState<any>(null);
  const [recommendedMessages, setRecommendedMessages] = useState<string[]>([]);
  const [currentRecommendIndex, setCurrentRecommendIndex] = useState(0);
  const [showMessageConfirmModal, setShowMessageConfirmModal] = useState(false);
  const [selectedMessageIndex, setSelectedMessageIndex] = useState<number | null>(null);
  const [policyModalVisible, setPolicyModalVisible] = useState(false);
  const [policyContent, setPolicyContent] = useState('');

  // 轮播引用
  const carouselRef = useRef<any>(null);

  // 用于双击检测
  const lastTapRef = useRef(0);

  // 背景图片数据
  const defaultBackgroundImages: any[] = [];
  const [backgroundImages, setBackgroundImages] = useState<any[]>(defaultBackgroundImages);

  // 初始化IAP连接
  useEffect(() => {
    // 连接到应用商店
    const initializeIAP = async () => {
      try {
        // 初始化连接
        await RNIap.initConnection();
        console.log('IAP连接初始化成功');

        // 记录初始化成功日志
        await post('/logMessage/paylogs', {
          type: 'init',
          status: 'success',
          message: 'IAP连接初始化成功',
          timestamp: new Date().toISOString(),
          device: Platform.OS,
        });
      } catch (err) {
        console.error('IAP连接初始化失败:', err);

        // 记录初始化失败日志
        await post('/logMessage/paylogs', {
          type: 'init',
          status: 'error',
          message: '初始化失败: ' + (err instanceof Error ? err.message : String(err)),
          timestamp: new Date().toISOString(),
          device: Platform.OS,
        });
      }
    };

    initializeIAP();

    // 组件卸载时关闭连接
    return () => {
      RNIap.endConnection();
    };
  }, []);

  // 初始化IAP连接
  useEffect(() => {
    // 连接到应用商店
    const initializeIAP = async () => {
      try {
        // 初始化连接
        await RNIap.initConnection();
        console.log('IAP连接初始化成功');

        // 记录初始化成功日志
        await post('/logMessage/paylogs', {
          type: 'init',
          status: 'success',
          message: 'IAP连接初始化成功',
          timestamp: new Date().toISOString(),
          device: Platform.OS,
        });
      } catch (err) {
        console.error('IAP连接初始化失败:', err);

        // 记录初始化失败日志
        await post('/logMessage/paylogs', {
          type: 'init',
          status: 'error',
          message: '初始化失败: ' + (err instanceof Error ? err.message : String(err)),
          timestamp: new Date().toISOString(),
          device: Platform.OS,
        });
      }
    };

    initializeIAP();

    // 组件卸载时关闭连接
    return () => {
      RNIap.endConnection();
    };
  }, []);

  // 在组件挂载时调用接口
  useEffect(() => {
    // 获取包信息
    const fetchPackageInfo = async () => {
      try {
        const response = await get('/packageInformationVoiceMessage/all');
        console.log('Package information response:', JSON.stringify(response.data));

        if (response.data && response.data.code === '200' && response.data.data) {
          // 保存包信息数据
          setPackageData(response.data.data);

          // 设置默认选中的时间和价格（选择第一个选项）
          if (response.data.data.length > 0) {
            const firstPackage = response.data.data[0];
            setSelectedDuration(firstPackage.generationTime);
            setSelectedPrice(firstPackage.surcharge);
          }
        }
      } catch (err) {
        console.error('Error fetching package information:', err);
      }
    };

    fetchPackageInfo();

    const fetchCelebrityDetail = async () => {
      try {
        setIsLoading(true);
        setError(null);

        // 使用 AsyncStorage 获取用户信息
        const userInfoStr = await AsyncStorage.getItem('@aivgen:user_info');
        let account = '';

        if (userInfoStr) {
          const userInfo = JSON.parse(userInfoStr);
          account = userInfo.nm || '';
        }

        // 如果没有nm参数，则不调用接口
        if (!nm) {
          setIsLoading(false);
          return;
        }

        // 调用接口
        const response = await get('/voicePerson/detail/', {
          nm: nm,
          account: account,
        });

        console.log('Celebrity detail response:', JSON.stringify(response.data));

        if (response.data && response.data.code === '200' && response.data.data) {
          // 保存接口返回的数据
          setCelebrityData(response.data.data);

          // 如果有图片数据，更新背景图片
          if (response.data.data.images && response.data.data.images.length > 0) {
            setBackgroundImages(response.data.data.images);
          }
        } else {
          setError(response.data.msg || t('voiceMessage.failedToGetData'));
        }
      } catch (err) {
        console.error('Error fetching celebrity detail:', err);
        setError(t('voiceMessage.errorFetchingData'));
      } finally {
        setIsLoading(false);
      }
    };

    fetchCelebrityDetail();
  }, [nm]);

  // 当 categoryNm 有值时调用接口
  useEffect(() => {
    const fetchCategoryDetail = async () => {
      // 如果没有 categoryNm 参数，则不调用接口
      if (!categoryNm) {
        return;
      }

      try {
        console.log('Fetching category detail for nm:', categoryNm);

        // 调用接口
        const response = await get('/voiceMessageSceneDetail/flush', {
          nm: categoryNm,
          num: 1,
        });

        console.log('Category detail response:', JSON.stringify(response.data));

        if (response.data && response.data.code === '200' && response.data.data) {
          // 保存接口返回的数据
          setCategoryData(response.data.data);

          // 如果有推荐消息数据，提取并保存
          if (response.data.data.list && response.data.data.list.length > 0) {
            const messages = response.data.data.list.map((item: any) => item.content || '');
            setRecommendedMessages(messages);

            // 设置默认消息为第一条推荐消息
            if (messages.length > 0) {
              setMessage(messages[0]);
            }
          }
        }
      } catch (err) {
        console.error('Error fetching category detail:', err);
      }
    };

    fetchCategoryDetail();
  }, [categoryNm]);

  // 处理搜索按钮点击
  const handleSearchPress = () => {
    console.log('Search pressed in WriteVoiceMessageScreen');
  };

  // 处理菜单按钮点击
  const handleMenuPress = () => {
    console.log('Menu pressed in WriteVoiceMessageScreen');
    navigation.navigate('Menu' as never);
  };

  // 处理产品政策点击
  const handlePolicyPress = async () => {
    try {
      console.log('Product Policy pressed');
      setIsLoading(true);

      // 调用接口获取产品政策内容
      const response = await post('/appSettting/info', {
        code: 'product_policy',
      });

      console.log('Product Policy response:', JSON.stringify(response.data));

      if (response.data && response.data.code === '200' && response.data.data) {
        // 保存接口返回的数据
        setPolicyContent(response.data.data.content || '');
        setPolicyModalVisible(true);
      } else {
        console.error('Failed to fetch product policy:', response.data);
        setPolicyContent(t('voiceMessage.failedToLoadPolicy'));
        setPolicyModalVisible(true);
      }
    } catch (err) {
      console.error('Error fetching product policy:', err);
      setPolicyContent(t('voiceMessage.errorLoadingPolicy'));
      setPolicyModalVisible(true);
    } finally {
      setIsLoading(false);
    }
  };

  // 处理创建语音消息
  const handleCreateVoiceMessage = async () => {
    // 检查消息是否为空
    if (!message || message.trim() === '') {
      // 使用AlertModal显示提示
      console.log('Message is empty, showing alert');
      setAlertVisible(true);
      return;
    }

    try {
      setIsLoading(true);

      // 获取用户信息
      const userInfoStr = await AsyncStorage.getItem('@aivgen:user_info');
      let createUser = '';
      let account = '';

      if (userInfoStr) {
        const userInfo = JSON.parse(userInfoStr);
        createUser = userInfo.nm || '';
        account = userInfo.nm || '';
      } else {
        showError(t('common.pleaseLoginFirst'));
        setIsLoading(false);
        return;
      }

      const _messages =  await get('sensitiveWords/check?word=' + message);
      console.log('_messages_messages_messages_messages',_messages)
      if(_messages && _messages.data.code  !== '200')  {
        return  showError(_messages.data.msg);
      }
      // 获取当前选中的包信息
      let packageInformationVoiceMessageNm = '';
      let selectedPackage;

      // 如果用户已经选择了时间和价格，则查找对应的套餐
      if (selectedDuration && selectedPrice) {
        selectedPackage = packageData.find(pkg =>
          pkg.generationTime === selectedDuration && pkg.surcharge === selectedPrice
        );
      }

      // 如果没有找到对应的套餐，则使用第一个套餐
      if (!selectedPackage && packageData.length > 0) {
        selectedPackage = packageData[0];
        setSelectedDuration(selectedPackage.generationTime);
        setSelectedPrice(selectedPackage.surcharge);
      }

      // 如果仍然没有套餐，则显示错误
      if (!selectedPackage) {
        showError(t('voiceMessage.pleaseSelectPackage'));
        setIsLoading(false);
        return;
      }

      console.log('选择的套餐:', selectedPackage);
      packageInformationVoiceMessageNm = selectedPackage.nm || '';

      // 获取内购产品ID
      const productId = selectedPackage.nm;

      if (!productId) {
        showError(t('voiceMessage.productIdCannotBeEmpty'));
        setIsLoading(false);
        return;
      }

      console.log('开始购买产品:', productId);

      // 获取当前选中的背景图片
      let voicePersonImages = '';

      // 如果用户双击选择了某张图片，则使用该图片
      if (selectedBackgroundIndex !== null && backgroundImages[selectedBackgroundIndex]) {
        const selectedImage = backgroundImages[selectedBackgroundIndex];
        if (typeof selectedImage === 'object' && selectedImage.resourceAddress) {
          voicePersonImages = selectedImage.resourceAddress;
        }
      }
      // 如果没有选择图片，则默认使用第一张
      else if (backgroundImages.length > 0) {
        const firstImage = backgroundImages[0];
        if (typeof firstImage === 'object' && firstImage.resourceAddress) {
          voicePersonImages = firstImage.resourceAddress;
        }
      }

      // 在购买前先检查用户是否已经购买过该商品
      try {
        console.log('检查用户是否已购买过该商品:', productId);

        // 获取用户的已有购买
        const availablePurchases = await RNIap.getAvailablePurchases();
        console.log('可用的购买记录:', availablePurchases);

        // 查找匹配的购买
        const matchingPurchase = availablePurchases.find(
          (purchase) => purchase.productId === productId
        );

        if (matchingPurchase) {
          console.log('找到匹配的购买:', matchingPurchase);

          // 用户已经购买过该商品，直接使用已有的购买继续处理
          // 记录已拥有商品的日志
          await post('/logMessage/paylogs', {
            type: 'purchase',
            status: 'info',
            message: '用户已拥有此商品，直接使用已有购买',
            productId: productId,
            timestamp: new Date().toISOString(),
            device: Platform.OS,
            userInfo: { account: account },
          });

          // 准备请求数据
          const requestData = {
            createUser: createUser,
            messageText: message,
            packageInformationVoiceMessageNm: packageInformationVoiceMessageNm,
            type: '0',
            voicePersonNm: nm || '',
            voicePersonImages: voicePersonImages,
            categoryNm: categoryNm || '',
            purchaseToken: matchingPurchase.purchaseToken || matchingPurchase.transactionId || '',
          };

          console.log('使用已有购买提交语音消息数据:', requestData);

          // 发送请求
          const response = await post('/voiceMessage/save', requestData);

          if (response.data && (response.data.code === '200' || response.data.code === 200)) {
            // 获取返回的数据
            const responseData = response.data.data;
            const messageId = responseData?.id || responseData?.nm || '';

            console.log('使用已有购买创建语音消息成功，ID:', messageId);

            // 显示成功消息
            showSuccess(t('voiceMessage.createSuccess'));

            // 创建成功后导航到确认页面
            navigation.navigate('VoiceMessageConfirm', {
              id: messageId,
              source: 'voiceMessage',
            });

            setIsLoading(false);
            return;
          } else {
            // 如果请求失败，显示错误信息
            showError(response.data?.msg || t('voiceMessage.saveFailed'));
            console.log('Failed to save voice message with existing purchase:', response.data);
            setIsLoading(false);
            return;
          }
        }

        // 如果没有找到匹配的购买，继续正常的购买流程
        console.log('未找到匹配的购买，继续正常购买流程');
      } catch (err) {
        // 如果检查已有购买时出错，记录错误但继续正常购买流程
        console.log('检查已有购买时出错:', err);

        await post('/logMessage/paylogs', {
          type: 'checkExistingPurchase',
          status: 'error',
          message: '检查已有购买时出错',
          productId: productId,
          error: err instanceof Error ? err.message : String(err),
          timestamp: new Date().toISOString(),
          device: Platform.OS,
          userInfo: { account: account },
        });
      }

      try {
        // 记录开始获取产品信息的日志
        await post('/logMessage/paylogs', {
          type: 'getProducts',
          status: 'start',
          message: '开始获取产品信息',
          productId: productId,
          timestamp: new Date().toISOString(),
          device: Platform.OS,
          userInfo: { account: account },
        });

        // 获取产品信息 - 使用 getProducts 获取一次性购买产品
        const productIds = [productId];
        console.log('获取产品信息',productIds)
        const products = await RNIap.getProducts({ skus: productIds });
        console.log('获取到的产品信息:', JSON.stringify(products));

        // 记录获取产品信息成功的日志
        await post('/logMessage/paylogs', {
          type: 'getProducts',
          status: 'success',
          message: '成功获取产品信息',
          productId: productId,
          productsCount: products.length,
          products: JSON.stringify(products),
          timestamp: new Date().toISOString(),
          device: Platform.OS,
          result: {
            success: true,
            data: products
          }
        });

        if (products.length === 0) {
          showError(t('voiceMessage.productNotFound'));

          // 记录未找到产品的日志
          await post('/logMessage/paylogs', {
            type: 'getProducts',
            status: 'error',
            message: '未找到对应的产品',
            productId: productId,
            timestamp: new Date().toISOString(),
            device: Platform.OS,
            result: {
              success: false,
              error: '未找到对应的产品'
            },
          });

          setIsLoading(false);
          return;
        }

        // 记录开始购买的日志
        await post('/logMessage/paylogs', {
          type: 'purchase',
          status: 'start',
          message: '开始购买产品',
          productId: productId,
          timestamp: new Date().toISOString(),
          device: Platform.OS,
          userInfo: { account: account },
        });

        // 请求购买 - 使用 requestPurchase 购买一次性产品
        const purchase = await RNIap.requestPurchase(
          Platform.OS === 'ios'
            ? {
                sku: productId,
                // 设置为 true 可以自动完成交易，简化流程
                // 设置为 false 则需要手动调用 finishTransaction
                andDangerouslyFinishTransactionAutomaticallyIOS: true,
              }
            : {
                skus: [productId], // Android 需要使用 skus 数组
                // 虽然参数名包含 iOS，但为了 API 一致性，Android 也可以包含此参数（会被忽略）
                andDangerouslyFinishTransactionAutomaticallyIOS: true,
              }
        );
        console.log('购买结果:', JSON.stringify(purchase));

        // 记录购买成功的日志
        await post('/logMessage/paylogs', {
          type: 'purchase',
          status: 'success',
          message: '产品购买成功',
          productId: productId,
          purchaseInfo: JSON.stringify(purchase),
          timestamp: new Date().toISOString(),
          device: Platform.OS,
          result: {
            success: true,
            data: purchase
          },
        });

        // 购买成功，调用API保存语音消息
        if (purchase) {
          // 获取购买凭证
          let purchaseToken = '';

          // 生成随机的purchaseToken作为备用
          const randomToken = `purchase_${Date.now()}_${Math.random().toString(36).substring(2, 10)}`;

          if (Platform.OS === 'android') {
            // Android 使用 purchaseToken
            if (typeof purchase === 'object' && 'purchaseToken' in purchase) {
              purchaseToken = purchase.purchaseToken as string || randomToken;
            } else if (typeof purchase === 'object' && 'transactionId' in purchase) {
              purchaseToken = purchase.transactionId as string || randomToken;
            } else {
              purchaseToken = randomToken;
            }
          } else {
            // iOS 使用 transactionReceipt
            if (typeof purchase === 'object' && 'transactionReceipt' in purchase) {
              purchaseToken = purchase.transactionReceipt as string || randomToken;
            } else if (typeof purchase === 'object' && 'transactionId' in purchase) {
              purchaseToken = purchase.transactionId as string || randomToken;
            } else {
              purchaseToken = randomToken;
            }
          }

          // 准备请求数据
          const requestData = {
            createUser: createUser,
            messageText: message,
            packageInformationVoiceMessageNm: packageInformationVoiceMessageNm,
            type: '0',
            voicePersonNm: nm || '',
            voicePersonImages: voicePersonImages,
            categoryNm: categoryNm || '', // 添加category的nm值
            purchaseToken: purchaseToken, // 添加购买凭证
          };

          console.log('Submitting voice message data:', requestData);

          // 记录开始保存语音消息的日志
          await post('/logMessage/paylogs', {
            type: 'saveVoiceMessage',
            status: 'start',
            message: '开始保存语音消息',
            productId: productId,
            packageNm: packageInformationVoiceMessageNm,
            timestamp: new Date().toISOString(),
            device: Platform.OS,
            userInfo: { account: account },
          });

          // 发送请求
          const response = await post('/voiceMessage/save', requestData);

          console.log('Voice message save response:', response.data);

          if (response.data && (response.data.code === '200' || response.data.code === 200)) {
            // 获取返回的数据
            const responseData = response.data.data;
            const messageId = responseData?.id || responseData?.nm || '';

            console.log('Voice message created with ID:', messageId);

            // 记录保存语音消息成功的日志
            await post('/logMessage/paylogs', {
              type: 'saveVoiceMessage',
              status: 'success',
              message: '语音消息保存成功',
              productId: productId,
              packageNm: packageInformationVoiceMessageNm,
              messageId: messageId,
              responseData: JSON.stringify(response.data),
              timestamp: new Date().toISOString(),
              device: Platform.OS,
              result: {
                success: true,
                data: response.data
              },
            });

            // 显示成功消息
            showSuccess(t('voiceMessage.createSuccess'));

            // 创建成功后导航到确认页面，并传递 id 参数和来源页面标识
            navigation.navigate('VoiceMessageConfirm', {
              id: messageId,
              source: 'voiceMessage', // 标识来源为语音消息页面
            });
          } else {
            // 记录保存语音消息失败的日志
            await post('/logMessage/paylogs', {
              type: 'saveVoiceMessage',
              status: 'error',
              message: '语音消息保存失败',
              productId: productId,
              packageNm: packageInformationVoiceMessageNm,
              errorMsg: response.data?.msg || '未知错误',
              responseData: JSON.stringify(response.data),
              timestamp: new Date().toISOString(),
              device: Platform.OS,
              result: {
                success: false,
                error: response.data?.msg || '未知错误',
                data: response.data
              },
            });

            // 如果请求失败，显示错误信息
            showError(response.data?.msg || t('voiceMessage.saveFailed'));
            console.error('Failed to save voice message:', response.data);
          }
        }
      } catch (iapError: any) {
        // console.error('IAP购买过程中出错:', iapError,iapError.code);

        // 记录IAP购买错误日志
        const errorCode = iapError && iapError.code ? iapError.code : '未知错误码';
        const errorMessage = iapError && typeof iapError === 'object' && 'message' in iapError
          ? iapError.message
          : '未知错误';

        await post('/logMessage/paylogs', {
          type: 'purchase',
          status: 'error',
          message: 'IAP购买过程中出错',
          productId: productId,
          errorCode: errorCode,
          errorMessage: errorMessage,
          timestamp: new Date().toISOString(),
          device: Platform.OS,
          userInfo: { account: account },
          result: {
            success: false,
            error: errorMessage,
            errorCode: errorCode,
            errorDetails: JSON.stringify(iapError)
          },
        });

        // 处理用户取消购买的情况
        if (iapError && iapError.code === 'E_USER_CANCELLED') {
          showError(t('voiceMessage.userCancelledPurchase'));
        }
        // 处理用户已拥有商品的情况
        else if (iapError && iapError.code === 'E_ALREADY_OWNED') {
          console.log('用户已拥有此商品，尝试继续处理...');

          // 记录已拥有商品的日志
          await post('/logMessage/paylogs', {
            type: 'purchase',
            status: 'warning',
            message: '用户已拥有此商品，尝试继续处理',
            productId: productId,
            timestamp: new Date().toISOString(),
            device: Platform.OS,
            userInfo: { account: account },
          });

          // 尝试获取用户的已有购买
          try {
            const availablePurchases = await RNIap.getAvailablePurchases();
            console.log('可用的购买:', availablePurchases);

            // 查找匹配的购买
            const matchingPurchase = availablePurchases.find(
              (purchase) => purchase.productId === productId
            );

            if (matchingPurchase) {
              console.log('找到匹配的购买:', matchingPurchase);

              // 使用已有的购买继续处理
              // 准备请求数据
              const requestData = {
                createUser: createUser,
                messageText: message,
                packageInformationVoiceMessageNm: packageInformationVoiceMessageNm,
                type: '0',
                voicePersonNm: nm || '',
                voicePersonImages: voicePersonImages,
                categoryNm: categoryNm || '',
                purchaseToken: matchingPurchase.purchaseToken || matchingPurchase.transactionId || '',
              };

              console.log('使用已有购买提交语音消息数据:', requestData);

              // 发送请求
              const response = await post('/voiceMessage/save', requestData);

              if (response.data && (response.data.code === '200' || response.data.code === 200)) {
                // 获取返回的数据
                const responseData = response.data.data;
                const messageId = responseData?.id || responseData?.nm || '';

                console.log('使用已有购买创建语音消息成功，ID:', messageId);

                // 显示成功消息
                showSuccess(t('voiceMessage.createSuccess'));

                // 创建成功后导航到确认页面
                navigation.navigate('VoiceMessageConfirm', {
                  id: messageId,
                  source: 'voiceMessage',
                });
              } else {
                // 如果请求失败，显示错误信息
                showError(response.data?.msg || t('voiceMessage.saveFailed'));
              }
            } else {
              // 没有找到匹配的购买，显示友好的错误信息
              showError(t('voiceMessage.alreadyOwnedButNotFound'));
            }
          } catch (err) {
            console.error('处理已有购买时出错:', err);
            showError(t('voiceMessage.alreadyOwnedProcessError'));
          }
        } else {
          showError(t('voiceMessage.purchaseError', { error: errorMessage }));
        }
      }
    } catch (err) {
      console.error('Error purchasing and creating voice message:', err);

      // 记录整体购买流程错误日志
      await post('/logMessage/paylogs', {
        type: 'purchaseProcess',
        status: 'error',
        message: '购买和创建语音消息流程出错',
        error: err instanceof Error ? err.message : String(err),
        timestamp: new Date().toISOString(),
        device: Platform.OS,
        result: {
          success: false,
          error: err instanceof Error ? err.message : String(err),
          errorDetails: JSON.stringify(err)
        },
      });

      showError(t('voiceMessage.generalPurchaseError'));
    } finally {
      setIsLoading(false);
    }
  };

  // 处理选择背景图片
  const handleSelectBackground = (index: number) => {
    console.log('Select background image at index:', index);
    // 单击时只滑动到指定位置
    if (carouselRef.current && typeof carouselRef.current.snapToItem === 'function') {
      carouselRef.current.snapToItem(index);
    }
  };

  // 处理双击背景图片
  const handleDoubleClickBackground = (index: number) => {
    console.log('Double click background image at index:', index);
    setSelectedBackgroundIndex(index);
    setShowConfirmModal(true);
  };

  // 处理确认应用背景
  const handleApplyBackground = () => {
    console.log('Apply background at index:', selectedBackgroundIndex);
    // 已经在双击时设置了 selectedBackgroundIndex，这里只需要关闭弹窗
    // 当用户提交表单时，会使用这个 selectedBackgroundIndex 来选择背景图片
    setShowConfirmModal(false);
  };

  // 处理取消应用背景
  const handleCancelApplyBackground = () => {
    console.log('Cancel apply background');
    // 重置选中的背景图片索引
    setSelectedBackgroundIndex(null);
    setShowConfirmModal(false);
  };

  // 处理切换到上一条推荐消息
  const handlePrevRecommendedMessage = () => {
    if (recommendedMessages.length === 0) {
      return;
    }

    const newIndex = currentRecommendIndex === 0
      ? recommendedMessages.length - 1
      : currentRecommendIndex - 1;

    setCurrentRecommendIndex(newIndex);
    setMessage(recommendedMessages[newIndex]);
  };

  // 处理切换到下一条推荐消息
  const handleNextRecommendedMessage = () => {
    if (recommendedMessages.length === 0) {
      return;
    }

    const newIndex = (currentRecommendIndex + 1) % recommendedMessages.length;

    setCurrentRecommendIndex(newIndex);
    setMessage(recommendedMessages[newIndex]);
  };

  // 处理随机选择推荐消息
  const handleRandomMessage = async () => {
    if (!categoryNm) {
      return;
    }

    try {
      console.log('Fetching random message for category:', categoryNm);
      setIsLoading(true);

      // 调用接口获取随机消息
      const response = await get('/voiceMessageSceneDetailAccount/flush', {
        nm: categoryNm,
      });

      console.log('Random message response:',response.data && response.data.code === '200', JSON.stringify(response.data));

      if (response.data && response.data.code === '200') {
        // 保存接口返回的数据
        setCategoryData(response.data.data);

        // 如果有推荐消息数据，提取并保存
        if (response.data.data && response.data.data.length > 0) {
          setRecommendedMessages(response.data.data);
          console.log('Recommended messages:', response.data.data);
          // 设置默认消息为第一条推荐消息
          if (response.data.data.length > 0) {
            setMessage(response.data.data[0]);
            setCurrentRecommendIndex(0);
          }
        }
      } else {
        console.error('Failed to fetch random message:', response.data);
      }
    } catch (err) {
      console.error('Error fetching random message:', err);
    } finally {
      setIsLoading(false);
    }
  };

  // 处理双击消息
  const handleDoubleClickMessage = (index: number) => {
    console.log('Double click message at index:', index);
    setSelectedMessageIndex(index);
    setShowMessageConfirmModal(true);
  };

  // 处理确认应用消息
  const handleApplyMessage = () => {
    console.log('Apply message at index:', selectedMessageIndex);
    if (selectedMessageIndex !== null && recommendedMessages[selectedMessageIndex]) {
      setMessage(recommendedMessages[selectedMessageIndex]);
      setCurrentRecommendIndex(selectedMessageIndex);
    }
    setShowMessageConfirmModal(false);
  };

  // 处理取消应用消息
  const handleCancelApplyMessage = () => {
    console.log('Cancel apply message');
    setSelectedMessageIndex(null);
    setShowMessageConfirmModal(false);
  };

  // 渲染轮播项
  const renderCarouselItem = ({ item, index }: { item: any; index: number }) => {
    // 创建双击处理逻辑
    let lastTap = 0;
    const handlePress = () => {
      const now = Date.now();
      const DOUBLE_PRESS_DELAY = 300;
      if (lastTap && (now - lastTap) < DOUBLE_PRESS_DELAY) {
        // 双击事件
        handleDoubleClickBackground(index);
      } else {
        // 单击事件
        handleSelectBackground(index);
        lastTap = now;
      }
    };

    return (
      <View style={styles.carouselItemWrapper}>
        <TouchableOpacity
          style={styles.backgroundImageContainer}
          onPress={handlePress}
          activeOpacity={0.9}
        >
          <Image
            source={typeof item === 'object' && item.resourceAddress ? { uri: item.resourceAddress } : item}
            style={styles.backgroundImage}
            resizeMode="cover"
          />
        </TouchableOpacity>
      </View>
    );
  };

  // 自定义右侧组件
  const headerRightComponent = (
    <View style={styles.headerRight}>
      <TouchableOpacity style={styles.searchButton} onPress={handleSearchPress}>
        <Image source={IMAGES.searchIcon} style={styles.icon} />
      </TouchableOpacity>
      <TouchableOpacity style={styles.menuButton} onPress={handleMenuPress}>
        <Image source={IMAGES.menuIcon} style={styles.icon} />
      </TouchableOpacity>
    </View>
  );

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="transparent" translucent={true} />

      {/* 使用全局 Header 组件 */}
      <Header title={t('voiceMessage.title')} rightComponent={headerRightComponent} />

      {/* 消息为空提示弹窗 */}
      {alertVisible && (
        <View style={styles.alertOverlay}>
          <View style={styles.alertContainer}>
            <Text style={styles.alertMessage}>{t('voiceMessage.enterTitleAtLeastOneLetter')}</Text>
            <View style={styles.alertDivider} />
            <TouchableOpacity
              style={styles.alertButton}
              onPress={() => setAlertVisible(false)}
            >
              <Text style={styles.alertButtonText}>{t('common.ok')}</Text>
            </TouchableOpacity>
          </View>
        </View>
      )}

      {/* 背景确认弹窗 */}
      {showConfirmModal && (
        <View style={styles.modalOverlay}>
          <View style={styles.modalContainer}>
            <View style={styles.modalHandle} />

            <Text style={styles.modalTitle}>{t('voiceMessage.apply')}</Text>

            <View style={styles.modalDivider} />

            <Text style={styles.modalText}>
              {t('voiceMessage.backgroundConfirmMessage')}
            </Text>

            <View style={styles.modalButtonsContainer}>
              <TouchableOpacity
                style={[styles.modalButton, styles.modalButtonNo]}
                onPress={handleCancelApplyBackground}
              >
                <Text style={styles.modalButtonText}>{t('common.no')}</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[styles.modalButton, styles.modalButtonYes]}
                onPress={handleApplyBackground}
              >
                <Text style={styles.modalButtonText}>{t('common.yes')}</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      )}

      {/* 消息确认弹窗 */}
      {showMessageConfirmModal && (
        <View style={styles.modalOverlay}>
          <View style={styles.modalContainer}>
            <View style={styles.modalHandle} />

            <Text style={styles.modalTitle}>{t('voiceMessage.apply')}</Text>

            <View style={styles.modalDivider} />

            <Text style={styles.modalText}>
              {t('voiceMessage.messageConfirmMessage')}
            </Text>

            <View style={styles.modalButtonsContainer}>
              <TouchableOpacity
                style={[styles.modalButton, styles.modalButtonNo]}
                onPress={handleCancelApplyMessage}
              >
                <Text style={styles.modalButtonText}>{t('common.no')}</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[styles.modalButton, styles.modalButtonYes]}
                onPress={handleApplyMessage}
              >
                <Text style={styles.modalButtonText}>{t('common.yes')}</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      )}

      {error ? (
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>{error}</Text>
        </View>
      ) : (
        <ScrollView style={styles.scrollView}>
          <View style={styles.content}>

          {/* 加载中遮罩层 */}
          {isLoading && (
            <View style={styles.loadingOverlay}>
              <ActivityIndicator size="large" color="#00FF47" />
            </View>
          )}
          {/* 名人头像 */}
          <View style={styles.profileContainer}>
            <LinearGradient
              colors={['#00C853', '#2980B9', '#8E44AD', '#2980B9']}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 1 }}
              style={styles.profileBorder}
            >
              <View style={styles.profileImageContainer}>
                <Image
                  source={celebrityData?.headImage ? { uri: celebrityData.headImage } : image}
                  style={styles.profileImage}
                  resizeMode="cover"
                />
              </View>
            </LinearGradient>
            <Text style={styles.nameText}>{celebrityData?.name || name}</Text>
          </View>

          {/* 消息部分 */}
          <View style={styles.messageSection}>
            <View style={styles.messageTitleContainer}>
              <Image
                source={require('../assets/images/home/<USER>')}
                style={styles.messageTitleIcon}
              />
              <Text style={styles.messageTitleText}>{t('voiceMessage.message')}</Text>
            </View>

            {categoryNm ? (
              // 当有 categoryNm 时，显示推荐消息轮播
              <View>
                <View style={styles.recommendedMessageHeader}>
                  <View style={styles.flexSpacer} />
                  <TouchableOpacity
                    style={styles.randomButton}
                    onPress={handleRandomMessage}
                  >
                    <Image
                      source={require('../assets/images/home/<USER>')}
                      style={styles.randomIcon}
                    />
                    <Text style={styles.randomButtonText}>{t('voiceMessage.random')}</Text>
                  </TouchableOpacity>
                </View>

                <View style={styles.recommendedMessageWrapper}>
                  <TouchableOpacity
                    style={styles.recommendNavButton}
                    onPress={handlePrevRecommendedMessage}
                  >
                    <Image
                      source={require('../assets/images/home/<USER>')}
                      style={styles.recommendNavIcon}
                    />
                  </TouchableOpacity>

                  <View style={styles.recommendedMessageContainer}>
                    <TouchableOpacity
                      style={styles.recommendedMessageContent}
                      onPress={() => {
                        // 创建双击处理逻辑
                        const now = Date.now();
                        const DOUBLE_PRESS_DELAY = 300;
                        if (lastTapRef.current && (now - lastTapRef.current) < DOUBLE_PRESS_DELAY) {
                          // 双击事件
                          handleDoubleClickMessage(currentRecommendIndex);
                        }
                        lastTapRef.current = now;
                      }}
                    >
                      <ScrollView
                        style={styles.recommendedMessageScrollView}
                        contentContainerStyle={styles.recommendedMessageScrollContent}
                        showsVerticalScrollIndicator={false}
                      >
                        <Text style={styles.recommendedMessageText}>{message}</Text>
                      </ScrollView>
                    </TouchableOpacity>
                  </View>

                  <TouchableOpacity
                    style={styles.recommendNavButton}
                    onPress={handleNextRecommendedMessage}
                  >
                    <Image
                      source={require('../assets/images/home/<USER>')}
                      style={styles.recommendNavIcon}
                    />
                  </TouchableOpacity>
                </View>

                <Text style={styles.recommendedMessageHint}>
                  {t('voiceMessage.clickMessageHint')}
                </Text>
              </View>
            ) : (
              // 当没有 categoryNm 时，显示普通输入框
              <View style={styles.messageInputContainer}>
                <TextInput
                  style={styles.messageInput}
                  placeholder={t('voiceMessage.enterMessagePlaceholder')}
                  placeholderTextColor="#666666"
                  multiline
                  value={message}
                  onChangeText={setMessage}
                  maxLength={500}
                />
                <Text style={styles.charCount}>{message.length}/500</Text>
              </View>
            )}
          </View>

          {/* 背景部分 */}
          <View style={styles.backgroundSection}>
            <View style={styles.backgroundTitleContainer}>
              <Image
                source={require('../assets/images/home/<USER>')}
                style={styles.backgroundTitleIcon}
              />
              <Text style={styles.backgroundTitleText}>{t('voiceMessage.background')}</Text>
            </View>

            <View>
              <Carousel
                ref={carouselRef}
                data={backgroundImages}
                renderItem={renderCarouselItem}
                width={width}
                height={250}
                onSnapToItem={(index) => setActiveSlide(index)}
                autoPlay={false}
                loop
                mode="parallax"
                modeConfig={{
                  parallaxScrollingScale: 0.9,
                  parallaxScrollingOffset: 50,
                }}
                style={styles.carousel}
              />

              {/* 轮播指示器 */}
              <View style={styles.paginationContainer}>
                {backgroundImages.map((_, index) => (
                  <TouchableOpacity
                    key={index}
                    style={[
                      styles.paginationDot,
                      activeSlide === index && styles.paginationDotActive,
                    ]}
                    onPress={() => handleSelectBackground(index)}
                  />
                ))}
              </View>

              {/* 提示文本 */}
              <Text style={styles.backgroundHintText}>
                {t('voiceMessage.clickPhotoHint')}
              </Text>
            </View>
          </View>

          {/* 时间类型部分 */}
          <View style={styles.timeTypeSection}>
            <View style={styles.timeTypeTitleContainer}>
              <Image
                source={require('../assets/images/home/<USER>')}
                style={styles.timeTypeTitleIcon}
              />
              <Text style={styles.timeTypeTitleText}>{t('voiceMessage.timeTypePrice')}</Text>
            </View>

            <View style={styles.timeTypeOptions}>
              {packageData.length > 0 ? (
                packageData.map((packageItem, index) => (
                  <TouchableOpacity
                    key={packageItem.id || index}
                    style={styles.timeTypeOption}
                    onPress={() => {
                      console.log('选择时间类型:', packageItem.generationTime, packageItem.surcharge);
                      setSelectedDuration(packageItem.generationTime);
                      setSelectedPrice(packageItem.surcharge);
                    }}
                  >
                    <View style={styles.timeTypeRow}>
                      <View style={styles.radioContainer}>
                        <View
                          style={[
                            styles.radioOuter,
                            selectedDuration === packageItem.generationTime &&
                            selectedPrice === packageItem.surcharge &&
                            styles.radioOuterSelected,
                          ]}
                        >
                          {selectedDuration === packageItem.generationTime &&
                           selectedPrice === packageItem.surcharge && (
                            <View style={styles.radioInner} />
                          )}
                        </View>
                      </View>
                      <View style={styles.timeTypeTextContainer}>
                        <Text style={styles.timeTypeText}>{packageItem.generationTime}</Text>
                        <Text style={styles.priceText}>(+ {packageItem.surcharge})</Text>
                      </View>
                    </View>
                  </TouchableOpacity>
                ))
              ) : (
                <Text style={styles.noDataText}>{t('voiceMessage.noTimeTypeData')}</Text>
              )}
            </View>
          </View>

          {/* 创建按钮 */}
          <View style={styles.createButtonContainer}>
            <TouchableOpacity
              style={styles.createButton}
              onPress={handleCreateVoiceMessage}
            >
              <Image
                source={require('../assets/images/home/<USER>')}
                style={styles.createButtonIcon}
              />
              <Text style={styles.createButtonText}>{selectedPrice || t('voiceMessage.selectPrice')}</Text>
            </TouchableOpacity>
          </View>

          {/* 产品政策链接 */}
          <TouchableOpacity
            style={styles.policyLinkContainer}
            onPress={handlePolicyPress}
          >
            <Text style={styles.policyLinkText}>{t('voiceMessage.productPolicy')}</Text>
          </TouchableOpacity>

          {/* 底部空间 */}
          <View style={styles.bottomSpace} />
        </View>
      </ScrollView>
      )}

      {/* 产品政策弹窗 */}
      <SimpleFullTextModal
        visible={policyModalVisible}
        onClose={() => setPolicyModalVisible(false)}
        title={t('voiceMessage.productPolicy')}
        content={policyContent}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000000',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1000,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  errorText: {
    color: '#FF4D4D',
    fontFamily: FONTS.pretendard.light,
    fontSize: 16,
    textAlign: 'center',
  },
  headerRight: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  searchButton: {
    marginRight: 15,
  },
  menuButton: {
    marginRight: 5,
  },
  icon: {
    width: 24,
    height: 24,
    tintColor: '#FFFFFF',
  },
  scrollView: {
    flex: 1,
  },
  content: {
    paddingBottom: 20,
  },
  profileContainer: {
    alignItems: 'center',
    marginTop: 20,
    marginBottom: 20,
  },
  profileBorder: {
    width: 100,
    height: 100,
    borderRadius: 50,
    alignItems: 'center',
    justifyContent: 'center',
  },
  profileImageContainer: {
    width: 90,
    height: 90,
    borderRadius: 45,
    overflow: 'hidden',
    backgroundColor: '#000000',
  },
  profileImage: {
    width: '100%',
    height: '100%',
  },
  nameText: {
    color: '#FFFFFF',
    fontFamily: FONTS.bebasNeue.regular,
    fontSize: 24,
    letterSpacing: 1,
    marginTop: 10,
  },
  messageSection: {
    marginBottom: 20,
  },
  messageTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 15,
    paddingHorizontal: 20,
  },
  messageTitleIcon: {
    width: 20,
    height: 20,
    tintColor: '#D9C091',
    marginRight: 8,
    resizeMode: 'contain',
  },
  messageTitleText: {
    color: '#D9C091',
    fontFamily: FONTS.bebasNeue.regular,
    fontSize: 20,
    letterSpacing: 1,
  },
  messageInputContainer: {
    marginHorizontal: 20,
    borderWidth: 1,
    borderColor: '#333333',
    borderRadius: 5,
    padding: 10,
    position: 'relative',
  },
  messageInput: {
    color: '#FFFFFF',
    fontFamily: FONTS.pretendard.light,
    fontSize: 16,
    minHeight: 80,
    textAlignVertical: 'top',
  },
  charCount: {
    color: '#666666',
    fontFamily: FONTS.pretendard.light,
    fontSize: 12,
    alignSelf: 'flex-end',
    marginTop: 5,
  },
  backgroundSection: {
    marginBottom: 20,
  },
  backgroundTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 15,
  },
  backgroundTitleIcon: {
    width: 20,
    height: 20,
    resizeMode: 'contain',
    tintColor: '#D9C091',
    marginRight: 8,
  },
  backgroundTitleText: {
    color: '#D9C091',
    fontFamily: FONTS.bebasNeue.regular,
    fontSize: 20,
    letterSpacing: 1,
  },
  carousel: {
    width: '100%',
    alignSelf: 'center',
  },
  backgroundImageContainer: {
    height: 250,
    borderRadius: 5,
    overflow: 'hidden',
    position: 'relative',
    width: '100%',
  },
  backgroundImage: {
    width: '100%',
    height: '100%',
  },

  backgroundHintText: {
    color: '#CCCCCC',
    fontFamily: FONTS.pretendard.light,
    fontSize: 12,
    textAlign: 'center',
    marginTop: 10,
    marginHorizontal: 20,
  },
  timeTypeSection: {
    marginBottom: 20,
  },
  timeTypeTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 15,
  },
  timeTypeTitleIcon: {
    width: 20,
    height: 20,
    tintColor: '#D9C091',
    marginRight: 8,
  },
  timeTypeTitleText: {
    color: '#D9C091',
    fontFamily: FONTS.bebasNeue.regular,
    fontSize: 20,
    letterSpacing: 1,
  },
  timeTypeOptions: {
    marginHorizontal: 20,
  },
  timeTypeOption: {
    marginBottom: 15,
  },
  timeTypeRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  radioContainer: {
    marginRight: 10,
  },
  radioOuter: {
    width: 20,
    height: 20,
    borderRadius: 10,
    borderWidth: 1,
    borderColor: '#FFFFFF',
    alignItems: 'center',
    justifyContent: 'center',
  },
  radioOuterSelected: {
    borderColor: '#6A4BFF',
  },
  radioInner: {
    width: 10,
    height: 10,
    borderRadius: 5,
    backgroundColor: '#6A4BFF',
  },
  timeTypeTextContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  timeTypeText: {
    color: '#FFFFFF',
    fontFamily: FONTS.pretendard.light,
    fontSize: 16,
    marginRight: 5,
  },
  priceText: {
    color: '#FFFFFF',
    fontFamily: FONTS.pretendard.light,
    fontSize: 16,
    marginBottom: 5,
  },
  createButtonContainer: {
    alignItems: 'center',
    marginBottom: 15,
  },
  createButton: {
    backgroundColor: '#6A4BFF',
    borderRadius: 25,
    paddingVertical: 12,
    paddingHorizontal: 30,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    width: 200,
  },
  createButtonIcon: {
    width: 30,
    height: 20,
    tintColor: '#FFFFFF',
    marginRight: 8,
  },
  createButtonText: {
    color: '#FFFFFF',
    fontFamily: FONTS.bebasNeue.regular,
    fontSize: 18,
    letterSpacing: 1,
  },
  policyLinkContainer: {
    alignItems: 'center',
    marginBottom: 20,
  },
  policyLinkText: {
    color: '#CCCCCC',
    fontFamily: FONTS.pretendard.light,
    fontSize: 14,
    textDecorationLine: 'underline',
  },
  noDataText: {
    color: '#CCCCCC',
    fontFamily: FONTS.pretendard.light,
    fontSize: 14,
    textAlign: 'center',
    marginVertical: 10,
  },
  paginationContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 10,
  },
  paginationDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#555555',
    marginHorizontal: 4,
  },
  paginationDotActive: {
    backgroundColor: '#D9C091',
    width: 10,
    height: 10,
    borderRadius: 5,
  },
  bottomSpace: {
    height: 40,
  },
  carouselItemWrapper: {
    paddingHorizontal: 10,
  },
  // 弹窗样式
  alertOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 9999,
    elevation: 10,
  },
  alertContainer: {
    width: width * 0.85,
    backgroundColor: '#222222',
    borderRadius: 10,
    overflow: 'hidden',
  },
  alertMessage: {
    color: '#FF0066',
    fontSize: 16,
    fontFamily: FONTS.pretendard.light,
    textAlign: 'center',
    paddingVertical: 30,
    paddingHorizontal: 20,
  },
  alertDivider: {
    height: 1,
    backgroundColor: '#444444',
    marginHorizontal: 0,
  },
  alertButton: {
    backgroundColor: '#007AFF',
    paddingVertical: 15,
    alignItems: 'center',
    margin: 15,
    borderRadius: 10,
  },
  alertButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontFamily: FONTS.pretendard.light,
    fontWeight: '500',
  },
  // 弹窗样式
  modalOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1000,
  },
  modalContainer: {
    width: '90%',
    backgroundColor: '#1A1A1A',
    borderRadius: 10,
    padding: 20,
    alignItems: 'center',
    maxWidth: 400,
  },
  modalHandle: {
    width: 40,
    height: 4,
    backgroundColor: '#666666',
    borderRadius: 2,
    marginBottom: 20,
  },
  modalTitle: {
    color: '#D9C091',
    fontFamily: FONTS.bebasNeue.regular,
    fontSize: 28,
    letterSpacing: 2,
    marginBottom: 15,
  },
  modalDivider: {
    width: '100%',
    height: 1,
    backgroundColor: '#333333',
    marginBottom: 20,
  },
  modalText: {
    color: '#FFFFFF',
    fontFamily: FONTS.pretendard.light,
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 30,
    lineHeight: 24,
  },
  modalButtonsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
  },
  modalButton: {
    borderRadius: 10, // 修改圆角半径为10
    paddingVertical: 12,
    paddingHorizontal: 20,
    width: '48%',
    alignItems: 'center',
  },
  modalButtonNo: {
    backgroundColor: '#FF4D4D',
  },
  modalButtonYes: {
    backgroundColor: '#007AFF',
  },
  modalButtonText: {
    color: '#FFFFFF',
    fontFamily: FONTS.bebasNeue.regular,
    fontSize: 18,
    letterSpacing: 1,
  },
  // 推荐消息样式
  recommendedMessageWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  recommendedMessageContainer: {
    flex: 1,
    borderWidth: 1,
    borderColor: '#333333',
    borderRadius: 5,
    padding: 10,
  },
  recommendNavButton: {
    width: 20,
    height: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  recommendNavIcon: {
    width: 6,
    height: 20,
    resizeMode: 'contain',
    tintColor: '#FFFFFF',
  },
  recommendedMessageContent: {
    flex: 1,
    paddingHorizontal: 10,
    height: 100, // 固定高度
  },
  recommendedMessageScrollView: {
    height: 100, // 固定高度
  },
  recommendedMessageScrollContent: {
    flexGrow: 1,
  },
  recommendedMessageText: {
    color: '#FFFFFF',
    fontFamily: FONTS.pretendard.light,
    fontSize: 16,
    textAlign: 'left',
    marginBottom: 5,
  },
  recommendedMessageCounter: {
    color: '#666666',
    fontFamily: FONTS.pretendard.light,
    fontSize: 12,
    textAlign: 'center',
  },
  recommendedMessageName: {
    color: '#D9C091',
    fontFamily: FONTS.pretendard.light,
    fontSize: 14,
    textAlign: 'center',
    marginTop: 5,
  },
  recommendedMessageHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginHorizontal: 20,
    marginBottom: 5,
  },
  flexSpacer: {
    flex: 1,
  },
  recommendedMessageHint: {
    color: '#666666',
    fontFamily: FONTS.pretendard.light,
    fontSize: 12,
    textAlign: 'center',
    marginTop: 10,
    marginBottom: 10,
    marginHorizontal: 20,
  },
  randomButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'transparent',
    paddingHorizontal: 8,
    paddingVertical: 4,
  },
  randomIcon: {
    width: 12,
    height: 12,
    marginRight: 4,
    tintColor: '#D9C091',
  },
  randomButtonText: {
    color: '#D9C091',
    fontFamily: FONTS.pretendard.light,
    fontSize: 12,
  },
});

export default WriteVoiceMessageScreen;
