import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Image,
  TouchableOpacity,
  Dimensions,
  StatusBar,
  ActivityIndicator,
} from 'react-native';
import { FONTS } from '../assets/fonts';
import { IMAGES } from '../assets/images';
import { Header } from '../components';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import LinearGradient from 'react-native-linear-gradient';
import { useTranslation } from 'react-i18next';
import Carousel from 'react-native-reanimated-carousel';

// 导入首页相关服务
import {
  getCarouselDataSimple,
  getTrendData,
  getCelebrityData,
} from '../services/api/homeService';

// 定义导航参数类型
type RootStackParamList = {
  Home: undefined;
  Celebrity: {
    selectedCategory?: string;
  };
  Subscription: undefined;
  CelebrityDetail: {
    id: number;
    name: string;
    type: string;
    image: any;
    nm?: string; // 添加nm字段，可选
  };
};

type HomeScreenNavigationProp = StackNavigationProp<RootStackParamList, 'Home'>;

// 获取屏幕宽度
const { width } = Dimensions.get('window');

// 默认轮播图数据
const defaultCarouselData = [
  { id: 1, tag: 'MUSICIAN', title: 'ARINDA GRANDE', image: IMAGES.banner, name: 'ARINDA GRANDE', headImage: '', category: 'MUSICIAN', collectStatus: false, subscriptStatus: false, nm: '' },
  { id: 2, tag: 'ACTOR', title: 'JOHN SMITH', image: IMAGES.banner, name: 'JOHN SMITH', headImage: '', category: 'ACTOR', collectStatus: false, subscriptStatus: false, nm: '' },
  { id: 3, tag: 'DANCER', title: 'LISA JOHNSON', image: IMAGES.banner, name: 'LISA JOHNSON', headImage: '', category: 'DANCER', collectStatus: false, subscriptStatus: false, nm: '' },
  { id: 4, tag: 'ARTIST', title: 'MICHAEL BROWN', image: IMAGES.banner, name: 'MICHAEL BROWN', headImage: '', category: 'ARTIST', collectStatus: false, subscriptStatus: false, nm: '' },
  { id: 5, tag: 'DESIGNER', title: 'EMMA WILSON', image: IMAGES.banner, name: 'EMMA WILSON', headImage: '', category: 'DESIGNER', collectStatus: false, subscriptStatus: false, nm: '' },
];

// 首页组件
const HomeScreen = () => {
  // 获取导航对象
  const navigation = useNavigation<HomeScreenNavigationProp>();
  // 获取翻译函数
  const { t } = useTranslation();

  // 轮播图数据
  const [carouselData, setCarouselData] = useState(defaultCarouselData);
  // 轮播图加载状态
  const [isCarouselLoading, setIsCarouselLoading] = useState(false);

  // 趋势数据
  const [trendData, setTrendData] = useState<any[]>([]);
  // 趋势加载状态
  const [isTrendLoading, setIsTrendLoading] = useState(false);

  // 名人数据
  const [celebrityData, setCelebrityData] = useState<any[]>([]);
  // 名人加载状态
  const [isCelebrityLoading, setIsCelebrityLoading] = useState(false);
  // 轮播图当前索引
  const [activeIndex, setActiveIndex] = useState(0);

  // 获取首页数据
  useEffect(() => {
    // 获取轮播图数据
    const fetchCarouselData = async () => {
      try {
        setIsCarouselLoading(true);
        // 获取轮播图数据
        const data = await getCarouselDataSimple();
        console.log('Carousel data:', JSON.stringify(data));

        if (data && data.length > 0) {
          // 如果成功获取数据，更新轮播图数据
          setCarouselData(data.map((item, index) => ({
            id: index + 1, // 生成唯一ID
            tag: item.category || 'UNKNOWN',
            title: item.name || 'Unknown',
            image: item.headImage ? { uri: item.headImage } : IMAGES.banner,
            name: item.name || 'Unknown',
            headImage: item.headImage || '',
            category: item.category || 'UNKNOWN',
            collectStatus: item.collectStatus || false,
            subscriptStatus: item.subscriptStatus || false,
            nm: item.nm || '', // 添加nm字段
          })));
        }
      } catch (error) {
        console.error('Failed to fetch carousel data:', error);
      } finally {
        setIsCarouselLoading(false);
        // 初始化时设置为第一张
        setActiveIndex(0);
      }
    };

    // 获取趋势数据
    const fetchTrendData = async () => {
      try {
        setIsTrendLoading(true);
        // 获取趋势数据
        const data = await getTrendData();
        console.log('Trend data:', JSON.stringify(data));

        if (data && data.length > 0) {
          // 如果成功获取数据，更新趋势数据
          setTrendData(data.map((item, index) => ({
            id: item.id || index + 1, // 使用服务器返回的ID，如果没有则生成唯一ID
            name: item.name || item.categoryExplain || 'Unknown',
            image: item.headImage ? { uri: item.headImage } : IMAGES.defaultAvatar,
            headImage: item.headImage || '',
            category: item.category || 'UNKNOWN',
            collectStatus: false, // 默认为false
            subscriptStatus: false, // 默认为false
            // 其他字段
            categoryExplain: item.categoryExplain || '',
            reserve3: item.reserve3 || '', // 可能是灰色图标
            reserve4: item.reserve4 || '', // 可能是橙色图标
            nm: item.nm || '', // 添加nm字段
          })));
        }
      } catch (error) {
        console.error('Failed to fetch trend data:', error);
      } finally {
        setIsTrendLoading(false);
      }
    };

    // 获取名人数据
    const fetchCelebrityData = async () => {
      try {
        setIsCelebrityLoading(true);
        // 获取名人数据
        const data = await getCelebrityData();
        console.log('datadata', data);
        if (data && data.length > 0) {
          // 如果成功获取数据，更新名人数据
          setCelebrityData(data.map((item, index) => ({
            id: item.id || index + 1, // 使用服务器返回的ID，如果没有则生成唯一ID
            name: item.name || item.categoryExplain || item.category || 'Unknown',
            image: item.headImage ? { uri: item.headImage } : IMAGES.defaultAvatar,
            headImage: item.headImage || '',
            category: item.category || 'UNKNOWN',
            collectStatus: false, // 默认为false
            subscriptStatus: false, // 默认为false
            // 其他字段
            categoryExplain: item.categoryExplain || '',
            reserve3: item.reserve3 || '', // 可能是灰色图标
            reserve4: item.reserve4 || '', // 可能是橙色图标
            nm: item.nm || '', // 添加nm字段
          })));
        }
      } catch (error) {
        console.error('Failed to fetch celebrity data:', error);
      } finally {
        setIsCelebrityLoading(false);
      }
    };

    // 执行所有数据获取函数
    fetchCarouselData();
    fetchTrendData();
    fetchCelebrityData();
  }, []);

  // 处理轮播图导航
  const handlePrevSlide = () => {
    const carouselInstance = (global as any).carouselRef;
    if (carouselInstance) {
      carouselInstance.prev();
    }
  };

  const handleNextSlide = () => {
    const carouselInstance = (global as any).carouselRef;
    if (carouselInstance) {
      carouselInstance.next();
    }
  };
  // 处理搜索按钮点击
  const handleSearchPress = () => {
    console.log('Search pressed in HomeScreen');
    // 这里添加搜索功能的逻辑
  };

  // 处理菜单按钮点击
  const handleMenuPress = () => {
    console.log('Menu pressed in HomeScreen');
    // 导航到菜单页面
    navigation.navigate('Menu' as never);
  };

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="transparent" translucent={true} />

      {/* 使用全局 Header 组件 */}
      <Header
        showBack={false}
        rightComponentType="menuAndSearch"
        onSearchPress={handleSearchPress}
        onMenuPress={handleMenuPress}
      />

      <ScrollView style={styles.scrollView}>
        {/* 主轮播图 */}
        <View style={styles.mainSlider}>
          {isCarouselLoading ? (
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="large" color="#00FF66" />
            </View>
          ) : (
            <Carousel
              ref={(c) => {
                // 保存一个引用，以便可以通过导航按钮控制轮播图
                (global as any).carouselRef = c;
              }}
              loop
              width={width}
              height={width * 0.8}
              autoPlay={true}
              data={carouselData}
              scrollAnimationDuration={500}
              autoPlayInterval={5000}
              onProgressChange={(_, absoluteProgress) => {
                // 根据滚动进度更新指示器
                const index = Math.round(absoluteProgress);
                if (index !== activeIndex && index >= 0 && index < carouselData.length) {
                  setActiveIndex(index);
                }
              }}
              onSnapToItem={(index) => setActiveIndex(index)}
              renderItem={({ item }) => (
                <TouchableOpacity
                  style={styles.carouselItem}
                  onPress={() => {
                    navigation.navigate('CelebrityDetail', {
                      id: item.id,
                      name: item.name,
                      type: item.category,
                      image: item.image,
                      nm: item.nm || '',
                    });
                  }}
                  activeOpacity={0.8}
                  pressRetentionOffset={{ top: 20, left: 20, right: 20, bottom: 20 }}
                >
                  <Image
                    source={item.image}
                    style={styles.carouselImage}
                    resizeMode="cover"
                  />
                  <View style={styles.bannerContent}>
                    <LinearGradient
                      colors={['#00FF47', '#AD00FF']}
                      start={{ x: 0, y: 0 }}
                      end={{ x: 1, y: 0 }}
                      style={styles.bannerTag}
                    >
                      <Text style={styles.bannerTagText}>{item.category}</Text>
                    </LinearGradient>
                    <Text style={styles.bannerTitle}>{item.name}</Text>
                  </View>
                </TouchableOpacity>
              )}
            />
          )}


          {/* 轮播图指示器 */}
          <View style={styles.paginationContainer}>
            {carouselData.map((_, index) => (
              <TouchableOpacity
                key={index}
                onPress={() => {
                  const carouselInstance = (global as any).carouselRef;
                  if (carouselInstance) {
                    carouselInstance.scrollTo({ index, animated: true });
                  }
                }}
              >
                <View
                  style={[
                    styles.paginationDot,
                    index === activeIndex && styles.paginationDotActive,
                  ]}
                />
              </TouchableOpacity>
            ))}
          </View>

          {/* 导航按钮 */}
          <View style={styles.navButtonsContainer}>
            <TouchableOpacity
              style={[styles.navButton, styles.navButtonLeft]}
              onPress={handlePrevSlide}
              hitSlop={{top: 20, bottom: 20, left: 20, right: 20}}
              activeOpacity={0.6}
            >
              <View style={styles.navButtonInner}>
                <Image
                  source={require('../assets/images/home/<USER>')}
                  style={styles.navButtonIcon}
                  resizeMode="contain"
                />
              </View>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.navButton, styles.navButtonRight]}
              onPress={handleNextSlide}
              hitSlop={{top: 20, bottom: 20, left: 20, right: 20}}
              activeOpacity={0.6}
            >
              <View style={styles.navButtonInner}>
                <Image
                  source={require('../assets/images/home/<USER>')}
                  style={styles.navButtonIcon}
                  resizeMode="contain"
                />
              </View>
            </TouchableOpacity>
          </View>
        </View>

        {/* 音乐节广告 */}
        <View style={styles.musicFestival}>
          <Image
            source={require('../assets/images/home/<USER>')}
            style={styles.festivalImage}
            resizeMode="cover"
          />
        </View>

        {/* 趋势部分 */}
        <View style={styles.sectionContainer}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>{t('home.trend')}</Text>
          </View>

          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            style={styles.trendScroll}
            decelerationRate="fast"
            snapToInterval={95} // 图片宽度 + 右边距
            snapToAlignment="start"
          >
            {isTrendLoading ? (
              <View style={styles.smallLoadingContainer}>
                <ActivityIndicator size="small" color="#00FF66" />
              </View>
            ) : trendData.length > 0 ? (
              trendData.map((item) => (
                <TouchableOpacity
                  key={item.id}
                  style={styles.trendItem}
                  onPress={() => navigation.navigate('CelebrityDetail', {
                    id: item.id,
                    name: item.name,
                    type: item.category,
                    image: item.image,
                    nm: item.nm || '',
                  })}
                >
                  <Image source={item.image} style={styles.trendImage} />
                  <Text style={styles.trendName}>{item.categoryExplain || item.name}</Text>
                </TouchableOpacity>
              ))
            ) : (
              // 没有数据时显示空状态
              <View style={styles.smallLoadingContainer}>
                <Text style={styles.trendName}>{t('common.noData')}</Text>
              </View>
            )
            }
          </ScrollView>
        </View>

        {/* 名人部分 */}
        <View style={styles.sectionContainer}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>{t('home.celebrity')}</Text>
            <TouchableOpacity
              style={styles.sectionMore}
              onPress={() => navigation.navigate('Celebrity', { selectedCategory: 'total' })}
            >
              <Text style={styles.sectionMoreIcon}>+</Text>
            </TouchableOpacity>
          </View>

          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            style={styles.celebrityScroll}
            decelerationRate="fast"
            snapToInterval={95} // 图片宽度 + 右边距
            snapToAlignment="start"
          >
            {isCelebrityLoading ? (
              <View style={styles.smallLoadingContainer}>
                <ActivityIndicator size="small" color="#00FF66" />
              </View>
            ) : celebrityData.length > 0 ? (
              celebrityData.map((item) => (
                <TouchableOpacity
                  key={item.id}
                  style={styles.celebrityItem}
                  onPress={() => {
                    navigation.navigate('Celebrity', {
                      selectedCategory: (item.category || 'musician').toLowerCase(),
                    });
                  }}
                >
                  <LinearGradient
                    colors={['#00FF75', '#4C0099']}
                    start={{ x: 0, y: 0 }}
                    end={{ x: 0, y: 1 }}
                    style={styles.celebrityImageGradientBorder}
                  >
                    <View style={styles.celebrityImageContainer}>
                      <Image source={item.image} style={styles.celebrityImage} />
                    </View>
                  </LinearGradient>
                  <Text style={styles.celebrityName}>{item.categoryExplain || item.name}</Text>
                </TouchableOpacity>
              ))
            ) : (
              // 没有数据时显示空状态
              <View style={styles.smallLoadingContainer}>
                <Text style={styles.celebrityName}>{t('common.noData')}</Text>
              </View>
            )
            }
          </ScrollView>
        </View>

        {/* 底部间距，为React Navigation的TabBar留出空间 */}
        <View style={styles.bottomPadding} />
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000000',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    height: width * 0.8, // 与轮播图高度一致
  },
  smallLoadingContainer: {
    width: 80,
    height: 80,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 15,
  },

  logo: {
    width: 120,
    height: 30,
    resizeMode: 'contain',
  },
  headerRight: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  searchButton: {
    marginRight: 15,
  },
  menuButton: {},
  icon: {
    width: 24,
    height: 24,
    tintColor: '#FFFFFF',
  },
  scrollView: {
    flex: 1,
  },
  mainSlider: {
    width: '100%',
    height: width * 0.8, // 保持宽高比
    position: 'relative',
  },
  carouselItem: {
    width: '100%',
    height: '100%',
    justifyContent: 'flex-end',
    backgroundColor: '#1A3B39',
    overflow: 'hidden',
    zIndex: 2,
  },
  carouselImage: {
    width: '100%',
    height: '100%',
    position: 'absolute',
    zIndex: 1,
  },
  bannerContent: {
    padding: 20,
    paddingBottom: 30,
    zIndex: 3,
  },
  bannerTag: {
    borderRadius: 20,
    alignSelf: 'flex-start',
    marginBottom: 10,
  },
  bannerTagText: {
    color: '#FFFFFF',
    fontFamily: FONTS.bebasNeue.regular,
    fontSize: 14,
    fontWeight: 'bold',
    paddingHorizontal: 10,
    paddingVertical: 5,
  },
  bannerTitle: {
    color: '#FFFFFF',
    fontFamily: FONTS.bebasNeue.regular,
    fontSize: 28,
  },
  musicFestival: {
    width: '100%',
    height: 100,
    marginVertical: 15,
  },
  festivalImage: {
    width: '100%',
    height: '100%',
  },
  sectionContainer: {
    marginBottom: 20,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 15,
    marginBottom: 15,
  },
  sectionTitle: {
    color: '#FFFFFF',
    fontFamily: FONTS.bebasNeue.regular,
    fontSize: 20,
    letterSpacing: 1,
  },
  sectionMore: {
    width: 24,
    height: 24,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  sectionMoreIcon: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
    lineHeight: 22,
  },
  trendScroll: {
    paddingLeft: 15,
  },
  trendItem: {
    marginRight: 15,
    alignItems: 'center',
  },
  trendImage: {
    width: 100,
    height: 100,
    borderRadius: 100,
    marginBottom: 8,
    borderWidth: 1,
    borderColor: '#D9C091',
  },
  trendName: {
    color: '#FFFFFF',
    fontFamily: FONTS.pretendard.light,
    fontSize: 12,
    textAlign: 'center',
  },
  celebrityScroll: {
    paddingLeft: 15,
  },
  celebrityItem: {
    marginRight: 15,
    alignItems: 'center',
  },
  celebrityImageGradientBorder: {
    width: 102, // 比图片大一点，形成边框效果
    height: 102,
    borderRadius: 51,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  celebrityImageContainer: {
    width: 98,
    height: 98,
    borderRadius: 49,
    overflow: 'hidden',
    backgroundColor: '#000',
  },
  celebrityImage: {
    width: 98,
    height: 98,
    borderRadius: 49,
  },
  celebrityName: {
    color: '#FFFFFF',
    fontFamily: FONTS.pretendard.light,
    fontSize: 12,
    textAlign: 'center',
  },
  bottomPadding: {
    height: 60, // 为React Navigation的TabBar留出空间
  },
  paginationContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    position: 'absolute',
    bottom: 20,
    width: '100%',
    zIndex: 10,
  },
  paginationDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: 'rgba(255, 255, 255, 0.5)',
    marginHorizontal: 5,
    transform: [{ scale: 1 }],
  },
  paginationDotActive: {
    backgroundColor: '#00FF66',
    width: 10,
    height: 10,
    borderRadius: 5,
    transform: [{ scale: 1.2 }],
  },
  navButtonsContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 15,
    zIndex: 10,
  },
  navButton: {
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 20,
  },
  navButtonLeft: {
    left: 15,
    position: 'absolute',
    top: '50%',
    transform: [{ translateY: -20 }],
  },
  navButtonRight: {
    right: 15,
    position: 'absolute',
    top: '50%',
    transform: [{ translateY: -20 }],
  },
  navButtonInner: {
    width: '100%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  navButtonIcon: {
    width: 15,
    height: 15,
    resizeMode: 'contain',
    tintColor: '#FFFFFF',
  },
});

export default HomeScreen;
