import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Image,
  TouchableOpacity,
  ScrollView,
  StatusBar,
  Dimensions,
  ActivityIndicator,
} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { FONTS } from '../assets/fonts';
import { IMAGES } from '../assets/images';
import { Header } from '../components';
import { get, post } from '../services/api/apiService';
import { useNavigation, useRoute, RouteProp } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { useTranslation } from 'react-i18next';

// 定义导航参数类型
type RootStackParamList = {
  Home: undefined;
  Celebrity: undefined;
  Subscription: undefined;
  CelebrityDetail: {
    id: number;
    name: string;
    type: string;
    image: any;
  };
  Alarm: {
    id: number;
    name: string;
    type: string;
    image: any;
    nm?: string; // 添加nm参数
  };
  VoiceMessage: {
    id: number;
    name: string;
    image: any;
    nm?: string; // 添加nm参数
  };
  MainTabs: {
    screen?: string;
    params?: any;
  };
  Profile: undefined;
  Inbox: undefined;
};

const { width: screenWidth } = Dimensions.get('window');

// 定义路由参数类型
type CelebrityDetailRouteParams = {
  id: number;
  name: string;
  type: string;
  image: any;
  nm?: string; // 添加 nm 参数
};

type CelebrityDetailRouteProp = RouteProp<
  { CelebrityDetail: CelebrityDetailRouteParams },
  'CelebrityDetail'
>;

type CelebrityDetailScreenNavigationProp = StackNavigationProp<RootStackParamList, 'CelebrityDetail'>;

const CelebrityDetailScreen = () => {
  const navigation = useNavigation<CelebrityDetailScreenNavigationProp>();
  const route = useRoute<CelebrityDetailRouteProp>();
  const { t } = useTranslation();
  const [isSubscribed, setIsSubscribed] = useState(false);

  // 添加状态来存储接口返回的数据
  const [celebrityData, setCelebrityData] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // 从路由参数中获取名人信息
  const { id, name, type, image, nm } = route.params || {
    id: 1,
    name: 'ARINDA GRANDE',
    type: 'MUSICIAN',
    image: IMAGES.defaultAvatar,
    nm: '',
  };

  // 在组件挂载时调用接口
  useEffect(() => {
    const fetchCelebrityDetail = async () => {
      try {
        setIsLoading(true);
        setError(null);

        // 使用 AsyncStorage 获取用户信息
        const userInfoStr = await AsyncStorage.getItem('@aivgen:user_info');
        let account = '';

        if (userInfoStr) {
          const userInfo = JSON.parse(userInfoStr);
          account = userInfo.nm || '';
        }

        // 调用接口
        const response = await get('/voicePerson/detail/', {
          nm: nm,
          account: account,
        });

        console.log('Celebrity detail respons1111e:', response.data);

        if (response.data && response.data.code === '200' && response.data.data) {
          // 保存接口返回的数据
          setCelebrityData(response.data.data);

          // 更新点赞和订阅状态
          setIsLiked(response.data.data.collectStatus || false);
          setIsSubscribed(response.data.data.subscriptStatus || false);

          // 更新点赞数量
          if (response.data.data.collectionNumber !== undefined) {
            setLikeCount(response.data.data.collectionNumber);
          }
        } else {
          setError(response.data.msg || t('errors.failedToFetchData'));
        }
      } catch (err) {
        console.error('Error fetching celebrity detail:', err);
        setError(t('errors.errorFetchingData'));
      } finally {
        setIsLoading(false);
      }
    };

    if (nm) {
      fetchCelebrityDetail();
    }
  }, [nm]);

  // 点赞计数
  const [likeCount, setLikeCount] = useState(135);
  const [isLiked, setIsLiked] = useState(false);

  // 处理点赞
  const handleLike = async () => {
    try {
      // 使用 AsyncStorage 获取用户信息
      const userInfoStr = await AsyncStorage.getItem('@aivgen:user_info');
      let account = '';

      if (userInfoStr) {
        const userInfo = JSON.parse(userInfoStr);
        account = userInfo.nm || '';
      }

      // 调用点赞接口
      const response = await post('/favoriteInformation/collectAndCancel', {
        peopleId: celebrityData?.nm || nm,
        account: account,
      });

      console.log('Like response:', JSON.stringify(response.data));

      // 更新UI
      if (isLiked) {
        setLikeCount(likeCount - 1);
      } else {
        setLikeCount(likeCount + 1);
      }
      setIsLiked(!isLiked);
    } catch (err) {
      console.error('Error handling like:', err);
    }
  };

  // 处理闹钟功能
  const handleAlarm = () => {
    console.log('Celeb Voice Alarm clicked');
    // 导航到闹钟设置页面，并传递名人信息
    navigation.navigate('Alarm', {
      id,
      name,
      type,
      image,
      nm, // 传递nm参数
    });
  };

  // 处理消息功能
  const handleMessage = () => {
    console.log('Celeb Voice Message clicked');
    // 导航到语音消息页面，并传递名人信息
    navigation.navigate('VoiceMessage', {
      id,
      name,
      image,
      nm, // 传递nm参数
    });
  };

  // 处理搜索按钮点击
  const handleSearchPress = () => {
    console.log('Search pressed in CelebrityDetailScreen');
    // 这里添加搜索功能的逻辑
  };

  // 处理菜单按钮点击
  const handleMenuPress = () => {
    console.log('Menu pressed in CelebrityDetailScreen');
    // 这里添加菜单功能的逻辑
  };

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="transparent" translucent={true} />

      {/* 使用全局 Header 组件 */}
      <Header
        title={name}
        rightComponentType="searchAndMenu"
        onSearchPress={handleSearchPress}
        onMenuPress={handleMenuPress}
      />

      {isLoading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#00FF47" />
        </View>
      ) : error ? (
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>{error}</Text>
        </View>
      ) : (
        <ScrollView style={styles.scrollView}>
        {/* 名人头像和背景 */}
        <View style={styles.profileHeader}>
          <Image
            source={celebrityData?.headImage ? { uri: celebrityData.headImage } : image}
            style={styles.profileImage}
            resizeMode="cover"
          />

          {/* 名人信息 - 放在图片内部 */}
          <View style={styles.profileInfo}>
            <LinearGradient
              colors={['#00FF47', '#AD00FF']}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 0 }}
              style={styles.typeContainer}
            >
              <Text style={styles.typeText}>{type}</Text>
            </LinearGradient>
            <Text style={styles.nameText}>{name}</Text>
          </View>

          {/* 点赞和订阅按钮 - 恢复到图片内部左下方 */}
          <View style={styles.actionButtonsOverlay}>
            <View style={styles.actionButtons}>
              <TouchableOpacity
                style={styles.likeButton}
                onPress={handleLike}
              >
                <Image
                  source={require('../assets/images/home/<USER>')}
                  style={[styles.likeIcon, isLiked && styles.likedIcon]}
                />
                <Text style={[styles.likeCount, isLiked && styles.likedText]}>{likeCount}</Text>
              </TouchableOpacity>

              <View style={styles.subscribeButton}>
                <Image
                  source={require('../assets/images/home/<USER>')}
                  style={[styles.subscribeIcon, isSubscribed && styles.likedIcon]}
                />
                <Text style={[styles.subscribeText, isSubscribed && styles.likedText]}>
                  {isSubscribed ? t('celebrity.subscribed') : t('celebrity.subscript')}
                </Text>
              </View>
            </View>
          </View>
        </View>

        {/* 个人简介 */}
        <View style={styles.bioSection}>
         
          <Text style={styles.bioText}>
            {celebrityData?.state || t('celebrity.defaultBio')}
          </Text>

        
        </View>

        {/* 功能按钮 */}
        <View style={styles.functionButtons}>
          <TouchableOpacity
            style={styles.alarmButton}
            onPress={handleAlarm}
          >
            <Image source={require('../assets/images/home/<USER>')} style={styles.buttonIcon} resizeMode="contain" />
            <Text style={styles.buttonText}>{t('celebrity.voiceAlarm')}</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.messageButton}
            onPress={handleMessage}
          >
            <Image source={require('../assets/images/home/<USER>')} style={styles.buttonIcon} resizeMode="contain" />
            <Text style={styles.buttonText}>{t('celebrity.voiceMessage')}</Text>
          </TouchableOpacity>
        </View>

        {/* 电子音乐测试广告 */}
        <View style={styles.adBanner}>
          <Image
            source={require('../assets/images/home/<USER>')}
            style={styles.adImage}
          />
        </View>
      </ScrollView>
      )}

      {/* 底部导航栏 */}
      <View style={styles.bottomTabBar}>
        <TouchableOpacity
          style={styles.tabItem}
          onPress={() => navigation.navigate('MainTabs', { screen: 'Home' })}
        >
          <Image
            source={IMAGES.tabIcons.home}
            style={[styles.tabIcon, { tintColor: '#767676' }]}
          />
          <Text style={[styles.tabText, { color: '#767676' }]}>{t('navigation.home')}</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.tabItem}
          onPress={() => navigation.navigate('MainTabs', { screen: 'Subscription' })}
        >
          <Image
            source={IMAGES.tabIcons.subscript}
            style={[styles.tabIcon, { tintColor: '#767676' }]}
          />
          <Text style={[styles.tabText, { color: '#767676' }]}>{t('navigation.subscription')}</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.tabItem}
          onPress={() => navigation.navigate('MainTabs', { screen: 'Inbox' })}
        >
          <Image
            source={IMAGES.tabIcons.inbox}
            style={[styles.tabIcon, { tintColor: '#767676' }]}
          />
          <Text style={[styles.tabText, { color: '#767676' }]}>{t('navigation.inbox')}</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.tabItem}
          onPress={() => navigation.navigate('MainTabs', { screen: 'Profile' })}
        >
          <Image
            source={IMAGES.tabIcons.my}
            style={[styles.tabIcon, { tintColor: '#767676' }]}
          />
          <Text style={[styles.tabText, { color: '#767676' }]}>{t('navigation.my')}</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000000',
  },
  headerRight: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  searchButton: {
    marginRight: 15,
  },
  menuButton: {},
  icon: {
    width: 24,
    height: 24,
    tintColor: '#FFFFFF',
  },
  scrollView: {
    flex: 1,
    paddingBottom: 0, // 不需要额外的padding，因为我们已经在广告组件上添加了足够的边距
  },
  profileHeader: {
    width: '100%',
    height: screenWidth * 0.8,
    backgroundColor: '#333',
    position: 'relative',
  },
  profileImage: {
    width: '100%',
    height: '100%',
  },
  profileInfo: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    padding: 20,
    paddingBottom: 25,
    width: '60%', // 限制宽度，避免与按钮重叠
    zIndex: 998, // 设置比 actionButtonsOverlay 稍低的 zIndex
    elevation: 998, // Android 需要 elevation
  },
  actionButtonsOverlay: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    left: 0,
    paddingHorizontal: 20,
    paddingVertical: 8,
    backgroundColor: 'rgba(0, 0, 0, 0.5)', // 半透明背景，只给按钮添加
    zIndex: 999, // 大幅提高 zIndex 确保覆盖层在轮播图上方
    elevation: 999, // Android 需要 elevation，设置为很高的值
  },
  typeContainer: {
    borderRadius: 20,
    alignSelf: 'flex-start',
    marginBottom: 10,
  },
  typeText: {
    color: '#FFFFFF',
    fontFamily: FONTS.bebasNeue.regular,
    fontSize: 14,
    paddingHorizontal: 15,
    paddingVertical: 5,
  },
  nameText: {
    color: '#FFFFFF',
    fontFamily: FONTS.bebasNeue.regular,
    fontSize: 28,
    marginBottom: 15,
  },

  actionButtons: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-start',
  },
  likeButton: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 20,
  },
  likeIcon: {
    width: 20,
    height: 20,
    resizeMode: 'contain',
    tintColor: '#FFFFFF',
    marginRight: 5,
  },
  likedIcon: {
    tintColor: '#FF0000',
  },
  likeCount: {
    color: '#FFFFFF',
    fontFamily: FONTS.pretendard.light,
    fontSize: 16,
  },
  likedText: {
    color: '#FF0000',
  },
  subscribeButton: {
    flexDirection: 'row',
    alignItems: 'center',
    marginLeft: 20,
  },
  subscribeIcon: {
    width: 20,
    height: 20,
    resizeMode: 'contain',
    tintColor: '#FFFFFF',
    marginRight: 5,
  },
  subscribeText: {
    color: '#FFFFFF',
    fontFamily: FONTS.bebasNeue.regular,
    fontSize: 16,
    letterSpacing: 1,
  },
  bioSection: {
    padding: 20,
  },
  bioTitle: {
    color: '#FFFFFF',
    fontFamily: FONTS.pretendard.light,
    fontSize: 18,
    marginBottom: 5,
  },
  bioSubtitle: {
    color: '#CCCCCC',
    fontFamily: FONTS.pretendard.light,
    fontSize: 16,
    marginBottom: 20,
  },
  bioText: {
    color: '#FFFFFF',
    fontFamily: FONTS.pretendard.light,
    fontSize: 16,
    lineHeight: 24,
    marginBottom: 10,
  },
  functionButtons: {
    padding: 10,
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  alarmButton: {
    backgroundColor: '#00C853',
    borderRadius: 25,
    paddingVertical: 12,
    paddingHorizontal: 20,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    flex: 1,
    marginRight: 10,
  },
  messageButton: {
    backgroundColor: '#7E57C2',
    borderRadius: 25,
    paddingVertical: 12,
    paddingHorizontal: 20,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    flex: 1,
    marginLeft: 10,
  },
  buttonIcon: {
    height: 20, // 固定高度
    width: 20, // 设置固定宽度
    tintColor: '#FFFFFF',
    marginRight: 8,
  },
  buttonText: {
    color: '#FFFFFF',
    fontFamily: FONTS.bebasNeue.regular,
    fontSize: 14,
    letterSpacing: 1,
  },
  adBanner: {
    width: '100%',
    height: 120,
    position: 'relative',
    marginTop: 20,
    marginBottom: 70, // 增加底部边距，确保广告不会被底部导航栏遮挡
  },
  adImage: {
    width: '100%',
    height: '100%',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorContainer: {
    padding: 20,
    backgroundColor: 'rgba(255, 0, 0, 0.1)',
    borderRadius: 10,
    margin: 20,
  },
  errorText: {
    color: '#FF0000',
    fontFamily: FONTS.pretendard.light,
    fontSize: 16,
    textAlign: 'center',
  },
  bottomTabBar: {
    flexDirection: 'row',
    backgroundColor: '#000000',
    borderTopWidth: 0.01,
    borderTopColor: '#333333',
    height: 60,
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
  },
  tabItem: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  tabIcon: {
    width: 20,
    height: 20,
    marginBottom: 5,
  },
  tabText: {
    fontFamily: 'Pretendard-Light',
    fontSize: 12,
  },
});

export default CelebrityDetailScreen;
