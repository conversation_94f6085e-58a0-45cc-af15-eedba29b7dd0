import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  TextInput,
  Image,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';

// 导入图片资源
import { IMAGES } from '../assets/images';
import { FONTS } from '../assets/fonts';
import { Header } from '../components';
import {  showError } from '../services/toast';
// 导入上下文
import { useAlert } from '../context/AlertContext';
import { useTranslation } from 'react-i18next';

// 定义导航类型
type AuthStackParamList = {
  Login: undefined;
  Agreement: undefined;
  SignUpForm: undefined;
  SignUpProfile: {
    email: string;
    password: string;
    confirmPassword: string;
  };
};

type SignUpFormScreenNavigationProp = StackNavigationProp<AuthStackParamList, 'SignUpForm'>;

const SignUpFormScreen = () => {
  const navigation = useNavigation<SignUpFormScreenNavigationProp>();
  const { showAlert } = useAlert();
  const { t } = useTranslation();

  // 表单状态
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  // 验证表单
  const validateForm = () => {
    if (!email || !email.includes('@')) {
      showAlert(t('signup.enterCorrectEmail'));
      return false;
    }

    if (!password || password.length < 8) {
      showAlert(t('signup.passwordMinLength'));
      return false;
    }

    if (password !== confirmPassword) {
      showAlert(t('signup.passwordsDoNotMatch'));
      return false;
    }

     // 验证密码长度
        if (password.length < 8 || password.length > 24) {
          showError(t('password.lengthRequirement'));
          return false;
        }
    
        // 验证密码包含小写字母和数字
        const hasLowercase = /[a-z]/.test(confirmPassword);
        const hasNumber = /[0-9]/.test(confirmPassword);
    
        if (!hasLowercase || !hasNumber) {
          showError(t('password.requireLowercaseAndNumbers'));
          return false;
        }

    return true;
  };

  // 处理下一步
  const handleNext = () => {
    if (validateForm()) {
      console.log('Sign up with:', { email, password, confirmPassword });
      // 导航到个人资料页面，并传递表单数据
      navigation.navigate('SignUpProfile', {
        email,
        password,
        confirmPassword,
      });
    }
  };

  // 返回上一页的逻辑已由Header组件处理

  return (
    <View style={styles.container}>
        {/* 背景图片 */}
        <Image
          source={IMAGES.signupBackground}
          style={styles.backgroundImage}
          resizeMode="contain"
        />

        {/* 顶部导航 */}
        <Header showLogo={true} />

        {/* 注册标题 */}
        <View style={styles.titleContainer}>
          <View style={styles.titleIndicator} />
          <Text style={styles.title}>{t('signup.title')}</Text>
        </View>

        {/* 邮箱输入 */}
        <View style={styles.inputSection}>
          <Text style={styles.inputLabel}>{t('signup.enterEmail')}</Text>
          <TextInput
            style={styles.textInput}
            placeholder=""
            placeholderTextColor="#666"
            value={email}
            onChangeText={setEmail}
            keyboardType="email-address"
            autoCapitalize="none"
          />

          {/* 邮箱格式提示 */}
          <View style={styles.warningContainer}>
            <Image source={IMAGES.warningIcon} style={styles.warningIcon} />
          </View>

          <View style={styles.requirementsList}>
            <View style={styles.requirementItem}>
              <Text style={styles.bulletPoint}>•</Text>
              <View style={styles.requirementTextContainer}>
                <Text>
                  <Text style={styles.requirementLabel}>{t('signup.configuration')} : </Text>
                  <Text style={styles.requirementText}>{t('signup.configurationValue')}</Text>
                </Text>
              </View>
            </View>
            <View style={styles.requirementItem}>
              <Text style={styles.bulletPoint}>•</Text>
              <View style={styles.requirementTextContainer}>
                <Text>
                  <Text style={styles.requirementLabel}>{t('signup.length')} : </Text>
                  <Text style={styles.requirementText}>{t('signup.lengthValueEmail')}</Text>
                </Text>
              </View>
            </View>
          </View>
        </View>

        {/* 密码输入 */}
        <View style={styles.inputSection}>
          <Text style={styles.inputLabel}>{t('signup.enterPassword')}</Text>

          {/* 第一个密码输入框 */}
          <View style={styles.passwordInputContainer}>
            <TextInput
              style={styles.passwordInput}
              placeholder=""
              placeholderTextColor="#666"
              value={password}
              onChangeText={setPassword}
              secureTextEntry={!showPassword}
              autoCapitalize="none"
            />
            <TouchableOpacity
              style={styles.eyeButton}
              onPress={() => setShowPassword(!showPassword)}
            >
              <Image
                source={showPassword ? IMAGES.eyeOpen : IMAGES.eyeClosed}
                style={showPassword ? styles.eyeIcon : styles.eyeIconClosed}
              />
            </TouchableOpacity>
          </View>

          {/* 确认密码输入框 */}
          <View style={[styles.passwordInputContainer, styles.confirmPasswordContainer]}>
            <TextInput
              style={styles.passwordInput}
              placeholder={t('signup.confirmPasswordPlaceholder')}
              placeholderTextColor="#666"
              value={confirmPassword}
              onChangeText={setConfirmPassword}
              secureTextEntry={!showConfirmPassword}
              autoCapitalize="none"
            />
            <TouchableOpacity
              style={styles.eyeButton}
              onPress={() => setShowConfirmPassword(!showConfirmPassword)}
            >
              <Image
                source={showConfirmPassword ? IMAGES.eyeOpen : IMAGES.eyeClosed}
                style={showConfirmPassword ? styles.eyeIcon : styles.eyeIconClosed}
              />
            </TouchableOpacity>
          </View>

          {/* 密码匹配提示 */}
          <View style={styles.passwordMatchContainer}>
            {confirmPassword.length > 0 && (
              <Text style={[styles.passwordMatchText, password === confirmPassword ? styles.passwordMatchSuccess : styles.passwordMatchError]}>
                {password === confirmPassword ? t('signup.passwordsMatch') : t('signup.passwordsDoNotMatch')}
              </Text>
            )}
          </View>

          {/* 密码格式提示 */}
          <View style={styles.warningContainer}>
            <Image source={IMAGES.warningIcon} style={styles.warningIcon} />
          </View>

          <View style={styles.requirementsList}>
            <View style={styles.requirementItem}>
              <Text style={styles.bulletPoint}>•</Text>
              <View style={styles.requirementTextContainer}>
                <Text>
                  <Text style={styles.requirementLabel}>{t('signup.length')} : </Text>
                  <Text style={styles.requirementText}>{t('signup.lengthValuePassword')}</Text>
                </Text>
              </View>
            </View>
            <View style={styles.requirementItem}>
              <Text style={styles.bulletPoint}>•</Text>
              <View style={styles.requirementTextContainer}>
                <Text>
                  <Text style={styles.requirementLabel}>{t('signup.requiredCondition')} : </Text>
                  <Text style={styles.requirementText}>{t('signup.requiredConditionValue')}</Text>
                </Text>
              </View>
            </View>
            <View style={styles.requirementItem}>
              <Text style={styles.bulletPoint}>•</Text>
              <View style={styles.requirementTextContainer}>
                <Text>
                  <Text style={styles.requirementLabel}>{t('signup.optionalCondition')} : </Text>
                  <Text style={styles.requirementText}>{t('signup.optionalConditionValue')}</Text>
                </Text>
              </View>
            </View>
          </View>
        </View>

        {/* 下一步按钮 */}
        <View style={styles.bottomContainer}>
          <TouchableOpacity
            style={styles.nextButton}
            onPress={handleNext}
          >
            <Text style={styles.nextButtonText}>{t('common.next')}</Text>
            <Image source={IMAGES.arrowRight} style={styles.arrowIcon} />
          </TouchableOpacity>
        </View>
    </View>
  );
};

// 使用Dimensions获取屏幕尺寸

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000',
    position: 'relative',
  },
  backgroundImage: {
    position: 'absolute',
    top: '50%',
    transform: [{ translateY: -150 }],
    right: -30,
    width: 300,
    height: 300,
    zIndex: 0,
  },

  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    marginTop: 20,
    marginBottom: 30,
    zIndex: 1,
  },
  titleIndicator: {
    width: 4,
    height: 24,
    backgroundColor: '#FF0066',
    marginRight: 10,
  },
  title: {
    color: '#FFFFFF',
    fontSize: 24,
    fontFamily: FONTS.bebasNeue.regular,
    letterSpacing: 1,
  },
  inputSection: {
    paddingHorizontal: 20,
    marginBottom: 20,
    zIndex: 1,
  },
  inputLabel: {
    color: '#FFFFFF',
    fontSize: 16,
    fontFamily: FONTS.bebasNeue.regular,
    letterSpacing: 1,
    marginBottom: 10,
  },
  textInput: {
    borderBottomWidth: 1,
    borderBottomColor: '#FFFFFF',
    color: '#FFFFFF',
    fontSize: 16,
    fontFamily: FONTS.pretendard.light,
    paddingVertical: 10,
  },
  passwordInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderBottomWidth: 1,
    borderBottomColor: '#FFFFFF',
    height: 50,
  },
  passwordInput: {
    flex: 1,
    color: '#FFFFFF',
    fontSize: 16,
    fontFamily: FONTS.pretendard.light,
    paddingVertical: 10,
  },
  eyeButton: {
    padding: 10,

  },
  eyeIcon: {
    width: 20,
    height: 15,
    tintColor: '#FFFFFF',
  },
  eyeIconClosed: {
    width: 24,
    height: 24,
    tintColor: '#FFFFFF',
  },
  passwordStrength: {
    flexDirection: 'row',
    marginTop: 10,
    marginBottom: 20,
  },
  strengthDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#444444',
    marginRight: 5,
  },
  strengthDotActive: {
    backgroundColor: '#FFFFFF',
  },
  warningContainer: {
    alignItems: 'center',
    marginVertical: 10,
  },
  warningIcon: {
    width: 24,
    height: 24,
    tintColor: '#FFFFFF',
    resizeMode: 'contain'
  },
  requirementsList: {
    marginTop: 10,
  },
  requirementItem: {
    flexDirection: 'row',
    marginBottom: 10,
    alignItems: 'flex-start',
  },
  requirementCard: {
    flexDirection: 'row',
  },
  requirementTextContainer: {
    flex: 1,
  },
  bulletPoint: {
    color: '#FF9800',
    fontSize: 16,
    marginRight: 5,
    marginTop: -2,
  },
  requirementLabel: {
    color: '#FF9800',
    fontSize: 14,
    fontFamily: FONTS.pretendard.light,
  },
  requirementText: {
    color: '#BBBBBB',
    fontSize: 14,
    fontFamily: FONTS.pretendard.light,
  },
  bottomContainer: {
    flex: 1,
    justifyContent: 'flex-end',
    alignItems: 'center',
    paddingBottom: 40,
    zIndex: 1,
  },
  nextButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 5,
    paddingVertical: 12,
    paddingHorizontal: 30,
  },
  nextButtonText: {
    color: '#FF0066',
    fontSize: 16,
    fontFamily: FONTS.bebasNeue.regular,
    letterSpacing: 1,
    marginRight: 10,
  },
  arrowIcon: {
    width: 16,
    height: 16,
    tintColor: '#FF0066',
  },
  passwordMatchContainer: {
    alignItems: 'center',
    marginTop: 10,
  },
  confirmPasswordContainer: {
    marginTop: 15,
  },
  passwordMatchText: {
    fontSize: 14,
    fontFamily: FONTS.pretendard.light,
  },
  passwordMatchSuccess: {
    color: '#4CAF50',
  },
  passwordMatchError: {
    color: '#F44336',
  },
});

export default SignUpFormScreen;
