import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  StatusBar,
  ScrollView,
  ActivityIndicator,
} from 'react-native';
import { FONTS } from '../assets/fonts';
import { Header, NoticeDetailModal } from '../components';
import { get } from '../services/api/apiService';
import { showError } from '../services/toast';
import { useTranslation } from 'react-i18next';





const NoticeScreen = () => {
  const { t } = useTranslation();

  // 状态管理
  const [notices, setNotices] = useState<any[]>([]);
  const [selectedNotice, setSelectedNotice] = useState<any>(null);
  const [modalVisible, setModalVisible] = useState(false);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // 获取通知数据
  const fetchNotices = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      const response = await get('/notice/all');
      console.log('response',response)
      if (response && response.data && response.data.code === '200'  ) {
        // 处理API响应数据
        const noticeData = response.data.data.map((item: any) => ({
          id: item.id || '',
          title: item.title || 'Notice',
          date: formatDate(item.createDate),
          writer: item.author || 'Admin',
          content: item.content || '',
          highlighted: false, // 可以根据需要设置高亮项
        }));

        setNotices(noticeData);
      } else {
        setError(t('notice.failedToLoad'));
      }
    } catch (err) {
      console.error('Error fetching notices:', err);
      setError(t('notice.failedToLoad'));
      showError(t('notice.failedToLoad'));
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchNotices();
  }, [fetchNotices]);



  // 格式化日期
  const formatDate = (dateString: string) => {
    try {
      const date = new Date(dateString);
      const year = date.getFullYear().toString().slice(2, 4); // 取年份的后两位
      const month = (date.getMonth() + 1).toString().padStart(2, '0');
      const day = date.getDate().toString().padStart(2, '0');
      return `${year}-${month}-${day}`;
    } catch (e) {
      return dateString;
    }
  };

  // 处理通知项点击
  const handleNoticePress = (notice: any) => {
    console.log(`Notice ${notice.id} pressed`);
    setSelectedNotice(notice);
    setModalVisible(true);
  };

  // 刷新数据
  const handleRefresh = () => {
    fetchNotices();
  };

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="transparent" translucent={true} />

      {/* 使用全局 Header 组件 */}
      <Header title={t('menu.notice')} />

      {loading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#D9C091" />
        </View>
      ) : error ? (
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>{error}</Text>
          <TouchableOpacity style={styles.refreshButton} onPress={handleRefresh}>
            <Text style={styles.refreshButtonText}>{t('common.retry')}</Text>
          </TouchableOpacity>
        </View>
      ) : (
        <ScrollView style={styles.scrollView}>
          {/* 通知列表 */}
          {notices.length > 0 ? (
            notices.map((notice, index) => (
              <TouchableOpacity
                key={notice.id || index}
                style={[
                  styles.noticeItem,
                  notice.highlighted && styles.highlightedNoticeItem,
                ]}
                onPress={() => handleNoticePress(notice)}
              >
                <View style={styles.noticeContent}>
                  <Text style={styles.noticeNumber}>[{index + 1}]</Text>
                  <Text style={styles.noticeTitle} numberOfLines={1} ellipsizeMode="tail">[{notice.title}]</Text>
                </View>
                <Text style={styles.noticeDate}>{notice.date}</Text>
              </TouchableOpacity>
            ))
          ) : (
            <View style={styles.emptyContainer}>
              <Text style={styles.emptyText}>{t('notice.noNotices')}</Text>
            </View>
          )}
        </ScrollView>
      )}

      {/* 通知详情弹窗 */}
      {selectedNotice && (
        <NoticeDetailModal
          visible={modalVisible}
          onClose={() => setModalVisible(false)}
          notice={selectedNotice}
        />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000000',
  },
  scrollView: {
    flex: 1,
    paddingHorizontal: 20,
    paddingTop: 20,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingTop: 50,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingTop: 50,
  },
  errorText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontFamily: FONTS.pretendard.light,
    marginBottom: 20,
  },
  refreshButton: {
    backgroundColor: '#D9C091',
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 5,
  },
  refreshButtonText: {
    color: '#000000',
    fontSize: 16,
    fontFamily: FONTS.pretendard.light,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingTop: 50,
  },
  emptyText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontFamily: FONTS.pretendard.light,
  },
  noticeItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 8,
    paddingHorizontal: 20,
    marginBottom: 15,
    backgroundColor: '#222222',
    borderRadius: 10,
  },
  highlightedNoticeItem: {
    borderWidth: 1,
    borderColor: '#9932CC', // 紫色边框
    backgroundColor: '#1A1A1A', // 稍深一点的背景色
  },
  noticeContent: {
    flexDirection: 'column',
    justifyContent: 'center',
    flex: 1, // 添加flex属性以确保有足够的空间
    marginRight: 10, // 与日期保持一定距离
  },
  noticeNumber: {
    color: '#FFFFFF',
    fontSize: 14,
    fontFamily: FONTS.pretendard.light,
  },
  noticeTitle: {
    color: '#D9C091', // 金色文字
    fontSize: 14,
    fontFamily: FONTS.pretendard.light,
    marginTop: 5,
    width: '90%', // 限制宽度以确保省略号正常显示
  },
  noticeDate: {
    color: '#FFFFFF',
    fontSize: 14,
    fontFamily: FONTS.pretendard.light,
  },
});

export default NoticeScreen;
