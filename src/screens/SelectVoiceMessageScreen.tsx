import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Image,
  TouchableOpacity,
  StatusBar,
  Dimensions,
  ImageBackground,
  ActivityIndicator,
} from 'react-native';
import Carousel from 'react-native-reanimated-carousel';
import { FONTS } from '../assets/fonts';
import { IMAGES } from '../assets/images';
import { Header } from '../components';
import { get } from '../services/api/apiService';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useNavigation, useRoute, RouteProp } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { useTranslation } from 'react-i18next';

// 定义导航参数类型
type RootStackParamList = {
  Home: undefined;
  MainTabs: {
    screen?: string;
  };
  VoiceMessage: {
    id: number;
    name: string;
    image: any;
    nm?: string;
  };
  SelectVoiceMessage: {
    id: number;
    name: string;
    image: any;
    nm?: string;
  };
  WriteVoiceMessage: {
    id: number;
    name: string;
    image: any;
    nm?: string;
  };
  RecommendVoiceMessage: {
    id: number;
    name: string;
    image: any;
    nm?: string;
  };
  Menu: undefined;
  Subscription: undefined;
  Inbox: undefined;
  Profile: undefined;
};

type SelectVoiceMessageRouteProp = RouteProp<
  { SelectVoiceMessage: { id: number; name: string; image: any; nm?: string } },
  'SelectVoiceMessage'
>;

type SelectVoiceMessageScreenNavigationProp = StackNavigationProp<RootStackParamList, 'SelectVoiceMessage'>;

const { width } = Dimensions.get('window');

const SelectVoiceMessageScreen = () => {
  const navigation = useNavigation<SelectVoiceMessageScreenNavigationProp>();
  const route = useRoute<SelectVoiceMessageRouteProp>();
  const { t } = useTranslation();

  // 添加状态管理
  const [celebrityData, setCelebrityData] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const carouselRef = useRef(null);

  // 从路由参数中获取名人信息
  const { id, name, image, nm } = route.params || {
    id: 1,
    name: 'ARINDA GRANDE',
    image: IMAGES.defaultAvatar,
    nm: '',
  };

  // 处理搜索按钮点击
  const handleSearchPress = () => {
    console.log('Search pressed in SelectVoiceMessageScreen');
  };

  // 处理菜单按钮点击
  const handleMenuPress = () => {
    console.log('Menu pressed in SelectVoiceMessageScreen');
    navigation.navigate('Menu' as never);
  };

  // 在组件挂载时调用接口
  useEffect(() => {
    const fetchCelebrityDetail = async () => {
      try {
        setIsLoading(true);
        setError(null);

        // 使用 AsyncStorage 获取用户信息
        const userInfoStr = await AsyncStorage.getItem('@aivgen:user_info');
        let account = '';

        if (userInfoStr) {
          const userInfo = JSON.parse(userInfoStr);
          account = userInfo.nm || '';
        }

        // 如果没有nm参数，则不调用接口
        if (!nm) {
          setIsLoading(false);
          return;
        }

        // 调用接口
        const response = await get('/voicePerson/detail/', {
          nm: nm,
          account: account,
        });

        console.log('Celebrity detail response:', JSON.stringify(response.data));

        if (response.data && response.data.code === '200' && response.data.data) {
          // 保存接口返回的数据
          setCelebrityData(response.data.data);
        } else {
          setError(response.data.msg || t('voiceMessage.failedToGetData'));
        }
      } catch (err) {
        console.error('Error fetching celebrity detail:', err);
        setError(t('voiceMessage.errorFetchingData'));
      } finally {
        setIsLoading(false);
      }
    };

    fetchCelebrityDetail();
  }, [nm]);

  // 处理自己写消息
  const handleWriteYourself = () => {
    console.log('Write it yourself pressed');
    // 导航到写消息页面
    navigation.navigate('WriteVoiceMessage', {
      id,
      name: celebrityData?.name || name,
      image: celebrityData?.headImage ? { uri: celebrityData.headImage } : image,
      nm,
    });
  };

  // 处理推荐消息
  const handleRecommendMessage = () => {
    console.log('Make Recommend Message pressed');
    // 导航到推荐消息页面
    navigation.navigate('RecommendVoiceMessage', {
      id,
      name: celebrityData?.name || name,
      image: celebrityData?.headImage ? { uri: celebrityData.headImage } : image,
      nm,
    });
  };

  // 自定义右侧组件
  const headerRightComponent = (
    <View style={styles.headerRight}>
      <TouchableOpacity style={styles.searchButton} onPress={handleSearchPress}>
        <Image source={IMAGES.searchIcon} style={styles.icon} />
      </TouchableOpacity>
      <TouchableOpacity style={styles.menuButton} onPress={handleMenuPress}>
        <Image source={IMAGES.menuIcon} style={styles.icon} />
      </TouchableOpacity>
    </View>
  );

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="transparent" translucent={true} />

      {/* 使用全局 Header 组件 */}
      <Header title={t('voiceMessage.title')}   rightComponentType="searchAndMenu" />

      {isLoading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#00FF47" />
        </View>
      ) : error ? (
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>{error}</Text>
        </View>
      ) : (
        <View style={styles.contentContainer}>
          {/* 名人视频背景 - 轮播图 */}
          <View style={styles.videoContainer}>
            {celebrityData?.images && celebrityData.images.length > 0 ? (
              <>
                <Carousel
                  ref={carouselRef}
                  width={width}
                  height={width * 0.8}
                  data={celebrityData.images}
                  scrollAnimationDuration={1000}
                  loop={true}
                  autoPlay={true}
                  autoPlayInterval={3000}
                  onProgressChange={(offsetProgress, absoluteProgress) => {
                    if (absoluteProgress !== undefined) {
                      setCurrentImageIndex(Math.round(absoluteProgress) % celebrityData.images.length);
                    }
                  }}
                  renderItem={({ item, index }) => (
                    <View key={index} style={styles.carouselItemContainer}>
                      <Image
                        source={{ uri: item.resourceAddress }}
                        style={styles.videoBackground}
                        resizeMode="cover"
                      />
                    </View>
                  )}
                />
                {/* 轮播图指示器 */}
                <View style={styles.paginationContainer}>
                  {celebrityData.images.map((_, index: number) => (
                    <View
                      key={index}
                      style={[
                        styles.paginationDot,
                        currentImageIndex === index && styles.paginationDotActive,
                      ]}
                    />
                  ))}
                </View>
              </>
            ) : (
              <>
                <Image
                  source={celebrityData?.headImage ? { uri: celebrityData.headImage } : image}
                  style={styles.videoBackground}
                  resizeMode="cover"
                />
              </>
            )}
          </View>

          {/* 选择语音消息标题 */}
          <Text style={styles.selectTitle}>{t('voiceMessage.selectVoiceMessage')}</Text>

          {/* 选项按钮 */}
          <View style={styles.optionsContainer}>
            <TouchableOpacity
              style={[styles.optionButton, styles.writeYourselfButton]}
              onPress={handleWriteYourself}
            >
              <Image
                source={require('../assets/images/home/<USER>')}
                style={styles.optionIcon}
              />
              <Text style={styles.optionText}>{t('voiceMessage.writeItYourself')}</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.optionButton, styles.recommendButton]}
              onPress={handleRecommendMessage}
            >
              <Image
                source={require('../assets/images/home/<USER>')}
                style={styles.optionIcon}
              />
              <Text style={styles.optionText}>{t('voiceMessage.makeRecommendMessage')}</Text>
            </TouchableOpacity>
          </View>

          {/* 音乐节广告 */}
          <View style={styles.adContainer}>
            <ImageBackground
              source={require('../assets/images/home/<USER>')}
              style={styles.adBackground}
              resizeMode="cover"
            >
            </ImageBackground>
          </View>
        </View>
      )}

      {/* 底部导航栏 */}
      <View style={styles.bottomTabBar}>
        <TouchableOpacity
          style={styles.tabItem}
          onPress={() => {
            // 先导航到 MainTabs，然后再导航到 Home
            navigation.navigate('MainTabs', { screen: 'Home' });
          }}
        >
          <Image
            source={IMAGES.tabIcons.home}
            style={[styles.tabIcon, { tintColor: '#767676' }]}
          />
          <Text style={[styles.tabText, { color: '#767676' }]}>{t('tabBar.home')}</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.tabItem}
          onPress={() => {
            // 先导航到 MainTabs，然后再导航到 Subscription
            navigation.navigate('MainTabs', { screen: 'Subscription' });
          }}
        >
          <Image
            source={IMAGES.tabIcons.subscript}
            style={[styles.tabIcon, { tintColor: '#767676' }]}
          />
          <Text style={[styles.tabText, { color: '#767676' }]}>{t('tabBar.subscript')}</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.tabItem}
          onPress={() => {
            // 先导航到 MainTabs，然后再导航到 Inbox
            navigation.navigate('MainTabs', { screen: 'Inbox' });
          }}
        >
          <Image
            source={IMAGES.tabIcons.inbox}
            style={[styles.tabIcon, { tintColor: '#D9C091' }]}
          />
          <Text style={[styles.tabText, { color: '#D9C091' }]}>{t('tabBar.inbox')}</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.tabItem}
          onPress={() => {
            // 先导航到 MainTabs，然后再导航到 Profile
            navigation.navigate('MainTabs', { screen: 'Profile' });
          }}
        >
          <Image
            source={IMAGES.tabIcons.my}
            style={[styles.tabIcon, { tintColor: '#767676' }]}
          />
          <Text style={[styles.tabText, { color: '#767676' }]}>{t('tabBar.my')}</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  errorText: {
    color: '#FF4D4D',
    fontFamily: FONTS.pretendard.light,
    fontSize: 16,
    textAlign: 'center',
  },
  contentContainer: {
    flex: 1,
    paddingBottom: 60, // 为底部导航栏留出空间
  },
  container: {
    flex: 1,
    backgroundColor: '#000000',
  },
  headerRight: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  searchButton: {
    marginRight: 15,
  },
  menuButton: {
    marginRight: 5,
  },
  icon: {
    width: 24,
    height: 24,
    tintColor: '#FFFFFF',
  },
  videoContainer: {
    width: '100%',
    height: width * 0.8,
    position: 'relative',
  },
  videoBackground: {
    width: '100%',
    height: '100%',
  },

  selectTitle: {
    color: '#D9C091',
    fontFamily: FONTS.bebasNeue.regular,
    fontSize: 24,
    letterSpacing: 1,
    textAlign: 'center',
    marginVertical: 20,
  },
  optionsContainer: {
    paddingHorizontal: 40,
    marginBottom: 20,
  },
  optionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: 30,
    paddingVertical: 15,
    paddingHorizontal: 20,
    marginBottom: 15,
  },
  writeYourselfButton: {
    backgroundColor: '#7246BB',
  },
  recommendButton: {
    backgroundColor: '#0896A6',
  },
  optionIcon: {
    width: 24,
    height: 24,
    tintColor: '#FFFFFF',
    marginRight: 15,
  },
  optionText: {
    color: '#FFFFFF',
    fontFamily: FONTS.pretendard.light,
    fontSize: 16,
  },
  adContainer: {
    marginHorizontal: 20,
    height: 120,
    borderRadius: 10,
    overflow: 'hidden',
    marginBottom: 20,
  },
  adBackground: {
    width: '100%',
    height: '100%',
    justifyContent: 'center',
  },

  carouselItemContainer: {
    width: '100%',
    height: '100%',
    position: 'relative',
  },
  paginationContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    position: 'absolute',
    bottom: 10,
    left: 0,
    right: 0,
  },
  paginationDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: 'rgba(255, 255, 255, 0.5)',
    marginHorizontal: 4,
  },
  paginationDotActive: {
    backgroundColor: '#FFFFFF',
    width: 10,
    height: 10,
    borderRadius: 5,
  },

  bottomTabBar: {
    flexDirection: 'row',
    backgroundColor: '#000000',
    borderTopWidth: 0.01,
    borderTopColor: '#333333',
    height: 60,
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
  },
  tabItem: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  tabIcon: {
    width: 20,
    height: 20,
    marginBottom: 5,
  },
  tabText: {
    fontFamily: 'Pretendard-Light',
    fontSize: 12,
  },
});

export default SelectVoiceMessageScreen;
