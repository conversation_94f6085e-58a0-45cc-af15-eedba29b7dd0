import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Image,
  TouchableOpacity,
  ScrollView,
  StatusBar,
} from 'react-native';
import { FONTS } from '../assets/fonts';
import { IMAGES } from '../assets/images';
import { Header } from '../components';
import { useNavigation, useRoute, RouteProp } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import LinearGradient from 'react-native-linear-gradient';
import { get } from '../services/api/apiService';
import { ActivityIndicator, Dimensions } from 'react-native';
import dayjs from 'dayjs';
import { useTranslation } from 'react-i18next';

// 获取屏幕宽度
const { width } = Dimensions.get('window');

// 计算当前日期和下个月的日期
const currentDate = new Date();
const nextMonthDate = new Date(currentDate);
nextMonthDate.setMonth(currentDate.getMonth() + 1);
const formattedCurrentDate = dayjs(currentDate).format('YYYY.MM.DD');
const formattedNextMonthDate = dayjs(nextMonthDate).format('YYYY.MM.DD');

// 定义导航参数类型
type RootStackParamList = {
  Home: undefined;
  Celebrity: undefined;
  Subscription: undefined;
  TierList: {
    subscriptionType?: 'upgrade' | 'downgrade' | 'new';
    celebrityNm?: string;
  };
  TierDetail: {
    id: number;
    name: string;
    description: string;
    price: string;
    celebrityNm?: string;
    subscriptionType?: 'upgrade' | 'downgrade' | 'new';
  };
  Alarm: {
    id: number;
    name: string;
    type: string;
    image: any;
  };
};

type TierListScreenNavigationProp = StackNavigationProp<RootStackParamList, 'TierList'>;
type TierListScreenRouteProp = RouteProp<RootStackParamList, 'TierList'>;

// 定义API返回的套餐信息类型
interface PackageInfo {
  id: number;
  name: string;
  description: string;
  price: string;
  color: string;
  backgroundColor: string;
  nm?: string;
  generationTime?: number;
  surcharge?: number;
  [key: string]: any;
}

// 默认订阅层级数据
const defaultTierData = [
  {
    id: 1,
    name: 'TIER 1',
    description: 'Subscribe for 3 voice alarms for a month',
    price: '100',
    color: '#8A6BFF',
    backgroundColor: '#FFFFFF',
  },
  {
    id: 2,
    name: 'TIER 2',
    description: 'Subscribe for 6 voice alarms for a month',
    price: '180',
    color: '#FFFFFF',
    backgroundColor: '#8A6BFF',
  },
  {
    id: 3,
    name: 'TIER 3',
    description: 'Subscribe for 10 voice alarms for a month',
    price: '250',
    color: '#FFFFFF',
    backgroundColor: '#00D1A0',
  },
];

const TierListScreen = () => {
  // 获取导航对象，用于返回上一页面或导航到其他页面
  const navigation = useNavigation<TierListScreenNavigationProp>();
  const route = useRoute<TierListScreenRouteProp>();
  const { t } = useTranslation();

  // 获取订阅类型参数和名人nm参数
  const { subscriptionType, celebrityNm } = route.params || {
    subscriptionType: undefined,
    celebrityNm: undefined,
  };

  // 标题固定为 SUBSCRIPTION
  const headerTitle = t('subscription.title');

  // 状态管理
  const [tierData, setTierData] = useState<PackageInfo[]>(defaultTierData);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [alertVisible, setAlertVisible] = useState(false);
  const [alertMessage, setAlertMessage] = useState('');
  const [showDowngradeConfirm, setShowDowngradeConfirm] = useState(false);
  const [selectedDowngradeTier, setSelectedDowngradeTier] = useState<PackageInfo | null>(null);

  // 获取套餐信息
  const fetchPackageInfo = async () => {
    if (!celebrityNm) {
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      // 根据订阅类型选择不同的API路径
      let endpoint;
      if (subscriptionType === 'downgrade') {
        endpoint = '/packageInformation/downgrade?nm=' + celebrityNm;
      } else if (subscriptionType === 'upgrade') {
        endpoint = '/packageInformation/upgrade?nm=' + celebrityNm;
      } else {
        // 'new' 状态或未指定状态
        endpoint = '/packageInformation/list?nm=' + celebrityNm;
      }

      console.log('Fetching package info from:', endpoint);
      const response = await get(endpoint);

      if (response && response.data && response.data.data) {
        console.log('Package info:', response.data.data);

        // 处理API返回的数据
        const apiData = response.data.data;

        // 将API数据转换为我们需要的格式
        const formattedData = apiData.map((item: any, index: number) => ({
          id: index + 1,
          name: item.packageTitle,
          description: item.packageDetails || 'No description available',
          price: item.amount || '0',
          color: index === 0 ? '#8A6BFF' : '#FFFFFF',
          backgroundColor: index === 0 ? '#FFFFFF' : index === 1 ? '#8A6BFF' : '#00D1A0',
          nm: item.nm,
          generationTime: item.generationTime,
          surcharge: item.surcharge,
          flag: item.flag || false, // 添加 flag 字段，如果 API 没有返回则默认为 false
        }));

        setTierData(formattedData.length > 0 ? formattedData : defaultTierData);
      } else {
        console.log('No data returned from API, using default data');
        setTierData(defaultTierData);
      }
    } catch (err) {
      console.error('Error fetching package info:', err);
      setError(t('subscription.failedToLoadPackageInfo'));
      setTierData(defaultTierData);
    } finally {
      setIsLoading(false);
    }
  };

  // 在组件挂载时获取套餐信息
  useEffect(() => {
    fetchPackageInfo();
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [celebrityNm, subscriptionType]);

  // 处理订阅选择
  const handleSelectTier = (tierId: number) => {
    console.log(`Selected tier: ${tierId}, Celebrity NM: ${celebrityNm}`);
    // 获取选中的 tier 数据
    const selectedTier = tierData.find(tier => tier.id === tierId);

    if (selectedTier) {
      // 检查 flag 字段，如果为 true，则显示提示信息
      if (selectedTier.flag) {
        // 使用自定义弹窗显示提示信息
        setAlertMessage(t('subscription.noLowerTiers'));
        setAlertVisible(true);
        return;
      }

      // 如果有celebrityNm参数，可以在这里处理与特定名人相关的订阅逻辑
      if (celebrityNm) {
        console.log(`Subscribing to celebrity with NM: ${celebrityNm} at tier level: ${tierId}`);
        // 这里可以添加实际的API调用代码
        // 例如: api.subscribe(celebrityNm, tierId, subscriptionType);
      }

      // 如果是降级操作，显示确认弹窗
      if (subscriptionType === 'downgrade') {
        setSelectedDowngradeTier(selectedTier);
        setShowDowngradeConfirm(true);
        return;
      }

      // 导航到 TierDetail 页面，并传递相应的参数
      navigation.navigate('TierDetail', {
        id: selectedTier.id,
        name: selectedTier.name,
        description: selectedTier.description,
        price: selectedTier.price,
        celebrityNm: celebrityNm, // 传递名人NM参数
        subscriptionType: subscriptionType, // 传递订阅类型参数
      });
    }
  };


  // 帮助按钮
  const helpButton = (
    <TouchableOpacity style={styles.helpButton}>
      <Text style={styles.helpButtonText}>?</Text>
    </TouchableOpacity>
  );

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="transparent" translucent={true} />

      {/* 使用全局 Header 组件 */}
      <Header title={headerTitle} rightComponentType="searchAndMenu" red={true}  />

      {/* 帮助按钮 */}
      {helpButton}

      {/* 验证提示弹窗 */}
      {alertVisible && (
        <View style={styles.alertOverlay}>
          <View style={styles.alertContainer}>
            <Text style={styles.alertMessage}>{alertMessage}</Text>
            <View style={styles.alertDivider} />
            <TouchableOpacity
              style={styles.alertButton}
              onPress={() => setAlertVisible(false)}
            >
              <Text style={styles.alertButtonText}>{t('common.ok')}</Text>
            </TouchableOpacity>
          </View>
        </View>
      )}

      {/* 降级确认弹窗 */}
      {showDowngradeConfirm && selectedDowngradeTier && (
        <View style={styles.alertOverlay}>
          <View style={styles.downgradeContainer}>
            <View style={styles.downgradeHandle} />

            <Text style={styles.downgradeTitle}>{t('subscription.downgrade')}</Text>

            <View style={styles.downgradeDivider} />

            <Text style={styles.downgradeText}>
              {t('subscription.downgradeText1')} {formattedCurrentDate}, {t('subscription.downgradeText2')} <Text style={styles.downgradeHighlight}>{t('subscription.downgradeText3')} {formattedNextMonthDate}, {t('subscription.downgradeText4')}</Text>
            </Text>

            <Text style={styles.downgradeQuestion}>
              {t('subscription.confirmDowngrade')}
            </Text>

            <View style={styles.downgradeButtonsContainer}>
              <TouchableOpacity
                style={[styles.downgradeButton, styles.downgradeButtonNo]}
                onPress={() => setShowDowngradeConfirm(false)}
              >
                <Text style={styles.downgradeButtonText}>{t('common.no')}</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[styles.downgradeButton, styles.downgradeButtonYes]}
                onPress={() => {
                  setShowDowngradeConfirm(false);
                  if (selectedDowngradeTier) {
                    // 导航到 TierDetail 页面，并传递相应的参数
                    navigation.navigate('TierDetail', {
                      id: selectedDowngradeTier.id,
                      name: selectedDowngradeTier.name,
                      description: selectedDowngradeTier.description,
                      price: selectedDowngradeTier.price,
                      celebrityNm: celebrityNm, // 传递名人NM参数
                      subscriptionType: subscriptionType, // 传递订阅类型参数
                    });
                  }
                }}
              >
                <Text style={styles.downgradeButtonText}>{t('common.yes')}</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      )}

      {/* 订阅类型标题 - 仅在 upgrade 或 downgrade 状态下显示 */}
      {(subscriptionType === 'upgrade' || subscriptionType === 'downgrade') && (
        <View style={styles.subscriptionTypeContainer}>
          <View style={styles.subscriptionTypeIndicator} />
          <Text style={styles.subscriptionTypeText}>
            {subscriptionType === 'upgrade' ? t('subscription.upgrade') : t('subscription.downgrade')}
          </Text>
        </View>
      )}

      {/* 加载状态 */}
      {isLoading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#8A6BFF" />
          <Text style={styles.loadingText}>{t('subscription.loadingPackageInfo')}</Text>
        </View>
      ) : error ? (
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>{error}</Text>
          <TouchableOpacity style={styles.retryButton} onPress={fetchPackageInfo}>
            <Text style={styles.retryButtonText}>{t('common.retry')}</Text>
          </TouchableOpacity>
        </View>
      ) : (
        /* 订阅层级列表 */
        <ScrollView style={styles.scrollView}>
          {tierData.map((tier) => (
            <View
              key={tier.id}
              style={[
                styles.tierCard,
                tier.id === 1 ? styles.tier1Card :
                tier.id === 2 ? styles.tier2Card :
                styles.tier3Card,
              ]}
            >
              <View style={styles.tierHeader}>
                <Image
                  source={require('../assets/images/home/<USER>')}
                  style={[
                    styles.tierIcon,
                    { tintColor: tier.id === 1 ? '#8A6BFF' : '#FFFFFF' },
                  ]}
                />
                <Text
                  style={[
                    styles.tierName,
                    { color: tier.color },
                  ]}
                >
                  {tier.name}
                </Text>
              </View>
              <Text
                style={[
                  styles.tierDescription,
                  { color: tier.color },
                ]}
              >
                {tier.description}
              </Text>
              {/* 显示生成时间和附加费用（如果有） */}
              {tier.generationTime && (
                <Text style={[styles.tierInfo, {color: tier.color}]}>
                  {t('subscription.generationTime')}: {tier.generationTime} {t('subscription.seconds')}
                </Text>
              )}
              {tier.surcharge && (
                <Text style={[styles.tierInfo, {color: tier.color}]}>
                  {t('subscription.surcharge')}: {tier.surcharge}
                </Text>
              )}
              <TouchableOpacity
                style={styles.priceButton}
                onPress={() => handleSelectTier(tier.id)}
              >
                <View style={styles.buttonContainer}>
                  {tier.flag ? (
                    // 如果 flag 为 true，使用灰色背景
                    <View
                      style={[
                        styles.gradientButton,
                        styles.gradientBackground,
                        { backgroundColor: '#999999' },
                      ]}
                    />
                  ) : (
                    // 否则使用渐变背景
                    <LinearGradient
                      colors={['rgba(173, 0, 255, 0.6)', 'rgba(2, 55, 189, 0.6)']}
                      start={{x: 0, y: 0}}
                      end={{x: 1, y: 0}}
                      style={[styles.gradientButton, styles.gradientBackground]}
                    />
                  )}
                  <View style={styles.buttonContent}>
                    <Text style={styles.priceButtonText}>₿ {tier.price}</Text>
                  </View>
                </View>
              </TouchableOpacity>
            </View>
          ))}

          {/* 底部切换选项 - 仅在 upgrade 状态下显示 */}
          {subscriptionType === 'upgrade' && (
            <TouchableOpacity
              style={styles.switchOptionButton}
              onPress={() => navigation.navigate('TierList', {
                subscriptionType: 'downgrade',
                celebrityNm: celebrityNm,
              })}
            >
              <Text style={styles.switchOptionText}>{t('subscription.wantToDowngrade')}</Text>
            </TouchableOpacity>
          )}

          {/* 底部切换选项 - 仅在 downgrade 状态下显示 */}
          {subscriptionType === 'downgrade' && (
            <TouchableOpacity
              style={styles.switchOptionButton}
              onPress={() => navigation.navigate('TierList', {
                subscriptionType: 'upgrade',
                celebrityNm: celebrityNm,
              })}
            >
              <Text style={styles.switchOptionText}>{t('subscription.wantToUpgrade')}</Text>
            </TouchableOpacity>
          )}

          {/* 底部间距 */}
          <View style={styles.bottomPadding} />
        </ScrollView>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000000',
  },
  // 验证弹窗样式
  alertOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 9999,
    elevation: 10,
  },
  alertContainer: {
    width: width * 0.85,
    backgroundColor: '#222222',
    borderRadius: 10,
    overflow: 'hidden',
  },
  alertMessage: {
    color: '#FFFFFF',
    fontSize: 16,
    fontFamily: FONTS.pretendard.light,
    textAlign: 'center',
    paddingVertical: 30,
    paddingHorizontal: 20,
  },
  alertDivider: {
    height: 1,
    backgroundColor: '#444444',
    marginHorizontal: 0,
  },
  alertButton: {
    backgroundColor: '#007AFF',
    paddingVertical: 15,
    alignItems: 'center',
    margin: 15,
    borderRadius: 10,
  },
  alertButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontFamily: FONTS.pretendard.light,
    fontWeight: '500',
  },

  // 降级确认弹窗样式
  downgradeContainer: {
    width: width * 0.9,
    backgroundColor: '#222222',
    borderRadius: 10,
    overflow: 'hidden',
    padding: 20,
    alignItems: 'center',
  },
  downgradeHandle: {
    width: 40,
    height: 4,
    backgroundColor: '#666666',
    borderRadius: 2,
    marginBottom: 20,
  },
  downgradeTitle: {
    color: '#D9C091',
    fontFamily: FONTS.bebasNeue.regular,
    fontSize: 28,
    letterSpacing: 2,
    marginBottom: 15,
  },
  downgradeDivider: {
    width: '100%',
    height: 1,
    backgroundColor: '#444444',
    marginBottom: 20,
  },
  downgradeText: {
    color: '#FFFFFF',
    fontFamily: FONTS.pretendard.light,
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 20,
    lineHeight: 24,
  },
  downgradeHighlight: {
    color: '#FF3B30',
  },
  downgradeQuestion: {
    color: '#FFFFFF',
    fontFamily: FONTS.pretendard.light,
    fontSize: 18,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 30,
    lineHeight: 26,
  },
  downgradeButtonsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
  },
  downgradeButton: {
    borderRadius: 10,
    paddingVertical: 15,
    width: '48%',
    alignItems: 'center',
  },
  downgradeButtonNo: {
    backgroundColor: '#FF3B30',
  },
  downgradeButtonYes: {
    backgroundColor: '#007AFF',
  },
  downgradeButtonText: {
    color: '#FFFFFF',
    fontSize: 18,
    fontFamily: FONTS.pretendard.light,
    fontWeight: '500',
  },
  headerRight: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  searchButton: {
    marginRight: 15,
  },
  menuButton: {},
  icon: {
    width: 24,
    height: 24,
    tintColor: '#FFFFFF',
  },
  helpButton: {
    position: 'absolute',
    top: 95,
    right: 20,
    width: 25,
    height: 25,
    borderRadius: 15,
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: '#FFFFFF',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 10,
  },
  helpButtonText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontFamily: FONTS.pretendard.light,
  },
  // 加载状态样式
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  loadingText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontFamily: FONTS.pretendard.light,
    marginTop: 20,
    textAlign: 'center',
  },
  // 错误状态样式
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  errorText: {
    color: '#FF4A4A',
    fontSize: 16,
    fontFamily: FONTS.pretendard.light,
    marginBottom: 20,
    textAlign: 'center',
  },
  retryButton: {
    backgroundColor: '#8A6BFF',
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 5,
  },
  retryButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontFamily: FONTS.bebasNeue.regular,
  },
  scrollView: {
    flex: 1,
    paddingHorizontal: 20,
    paddingTop: 40,
  },
  tierCard: {
    borderRadius: 15,
    padding: 20,
    marginBottom: 20,
    minHeight: 100,
  },
  tier1Card: {
    backgroundColor: '#FFFFFF',
  },
  tier2Card: {
    backgroundColor: '#8A6BFF',
  },
  tier3Card: {
    backgroundColor: '#00D1A0',
  },
  tierHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 10,
  },
  tierIcon: {
    width: 15,
    height: 20,
    resizeMode: 'contain',
    marginRight: 10,
  },
  tierName: {
    fontSize: 18,
    fontFamily: FONTS.bebasNeue.regular,
    letterSpacing: 1,
  },
  tierDescription: {
    fontSize: 16,
    fontFamily: FONTS.pretendard.light,
    marginBottom: 10,
  },
  tierInfo: {
    fontSize: 14,
    fontFamily: FONTS.pretendard.light,
    marginBottom: 5,
  },
  priceButton: {
    borderRadius: 5,
    overflow: 'hidden',
    width: '100%', // 确保按钮占满整个卡片宽度
  },
  gradientButton: {
    alignItems: 'center',
    justifyContent: 'center',
    width: '100%',
    height: 40,
  },
  priceButtonText: {
    color: '#FFFFFF',
    fontSize: 18,
    fontFamily: FONTS.bebasNeue.regular,
    textAlign: 'center',
  },
  buttonContainer: {
    width: '100%',
    height: 40,
  },
  gradientBackground: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  buttonContent: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  bottomPadding: {
    height: 30,
  },
  subscriptionTypeContainer: {
    paddingHorizontal: 20,
    marginTop: 20,
    flexDirection: 'row',
    alignItems: 'center',
  },
  subscriptionTypeIndicator: {
    width: 4,
    height: 20,
    backgroundColor: '#FF0000',
    marginRight: 10,
  },
  subscriptionTypeText: {
    color: '#FFFFFF',
    fontSize: 20,
    fontFamily: FONTS.bebasNeue.regular,
    letterSpacing: 1,
  },
  switchOptionButton: {
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 20,
    marginBottom: 10,
  },
  switchOptionText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontFamily: FONTS.pretendard.light,
    textDecorationLine: 'underline',
  },
});

export default TierListScreen;
