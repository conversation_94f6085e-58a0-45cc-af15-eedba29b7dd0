import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  StatusBar,
  ActivityIndicator,
  ImageBackground,
  Image,
  Vibration,
} from 'react-native';
import { useNavigation, useRoute, RouteProp } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { FONTS } from '../assets/fonts';
import { get } from '../services/api/apiService';
import notifee, { TriggerType, AndroidImportance, TimestampTrigger } from '@notifee/react-native';
import SoundPlayer from 'react-native-sound-player';
import { useTranslation } from 'react-i18next';

// 定义路由参数类型
type AlarmTriggerRouteParams = {
  alarmId?: number | string;
};

type RootStackParamList = {
  AlarmTrigger: AlarmTriggerRouteParams;
  MainTabs: {
    screen?: string;
    params?: any;
  };
};

type AlarmTriggerRouteProp = RouteProp<
  { AlarmTrigger: AlarmTriggerRouteParams },
  'AlarmTrigger'
>;

type AlarmTriggerScreenNavigationProp = StackNavigationProp<RootStackParamList, 'AlarmTrigger'>;

// 闹钟数据类型
interface AlarmData {
  id: number | string;
  title: string;
  setTime: string;
  timeType: string;
  morningImage?: string;
  wavUrl?: string;
  days: string;
  [key: string]: any;
}

const AlarmTriggerScreen = () => {
  const navigation = useNavigation<AlarmTriggerScreenNavigationProp>();
  const route = useRoute<AlarmTriggerRouteProp>();
  const { t } = useTranslation();

  // 从路由参数中获取闹钟ID
  const { alarmId } = route.params || {};

  console.log('AlarmTriggerScreen - Received alarmId:', alarmId);

  // 闹钟数据状态
  const [alarmData, setAlarmData] = useState<AlarmData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // 音频播放状态
  const [isPlaying, setIsPlaying] = useState(false);

  // 处理停止按钮点击
  const handleStop = useCallback(async () => {

    // 停止音频播放
    try {
      console.log(t('logs.stoppingSoundPlayback'));
      SoundPlayer.stop();
    } catch (stopError) {
      console.error(t('logs.errorStoppingSoundPlayback'), stopError);
    }

    // 停止震动
    console.log(t('logs.stoppingVibration'));
    Vibration.cancel();

    // 取消通知
    if (alarmId) {
      await notifee.cancelNotification(`alarm_${alarmId}`);
    }

    // 导航回主页
    navigation.navigate('MainTabs', { screen: 'Home' });
  }, [navigation, alarmId]);

  // 处理延迟5分钟按钮点击
  const handleDelayAlarm = useCallback(async () => {
    // 停止当前音频播放
    try {
      console.log(t('logs.pausingSoundPlaybackForDelay'));
      SoundPlayer.stop();
    } catch (stopError) {
      console.error(t('logs.errorStoppingSoundPlayback'), stopError);
    }

    // 停止震动
    console.log(t('logs.stoppingVibrationForDelay'));
    Vibration.cancel();

    // 取消当前通知
    if (alarmId) {
      await notifee.cancelNotification(`alarm_${alarmId}`);
    }

    console.log(t('logs.schedulingAlarmIn5Minutes'));

    // 创建一个5分钟后触发的新通知，保留原始闹钟的所有配置
    try {
      if (!alarmData) {
        console.error('No alarm data available for delay');
        return;
      }

      // 创建通知频道（仅Android需要）
      const channelId = await notifee.createChannel({
        id: 'alarm',
        name: 'Alarm Notifications',
        importance: AndroidImportance.HIGH, // 使用常量代替硬编码的数字
        sound: alarmData.wavUrl ? alarmData.wavUrl.split('/').pop()?.replace(/\.[^/.]+$/, '').replace(/\W/g, '_') : 'default',
        vibration: true,
      });

      // 计算5分钟后的时间
      const date = new Date();
      date.setMinutes(date.getMinutes() + 5);

      // 创建触发器
      const trigger: TimestampTrigger = {
        type: TriggerType.TIMESTAMP, // 使用常量代替硬编码的数字
        timestamp: date.getTime(), // 使用时间戳而不是日期对象
      };

      // 准备通知数据，保留原始闹钟的所有配置
      const notificationData: { [key: string]: string | number | object } = {
        alarmId: alarmId ? alarmId.toString() : '',
        title: alarmData.title || 'Alarm',
        time: alarmData.setTime || '',
        timeType: alarmData.timeType || '',
        days: alarmData.days || '',
        vibration: alarmData.vibration ? 'true' : 'false',
        repeatFlag: alarmData.repeatFlag ? 'true' : 'false',
        setWeek: alarmData.setWeek || '',
        setTime: alarmData.setTime || '',
        screen: 'AlarmTrigger', // 添加导航目标屏幕
        type: 'alarm',
      };

      // 添加可选字段
      if (alarmData.morningImage) {
        notificationData.morningImage = alarmData.morningImage;
      }

      if (alarmData.wavUrl) {
        notificationData.wavUrl = alarmData.wavUrl;
        notificationData.soundUrl = alarmData.wavUrl; // 保存原始声音URL，以便在闹钟触发时可以播放
      }

      // 显示通知
      await notifee.createTriggerNotification(
        {
          id: `alarm_${alarmId}_delayed`,
          title: alarmData.title || t('alarm.alarmTriggered'),
          body: t('alarm.delayedAlarmRinging'),
          android: {
            channelId,
            pressAction: {
              id: 'default',
              launchActivity: 'default',
            },
            importance: AndroidImportance.HIGH, // 使用常量代替硬编码的数字
            sound: alarmData.wavUrl ? alarmData.wavUrl.split('/').pop()?.replace(/\.[^/.]+$/, '').replace(/\W/g, '_') : 'default',
          },
          ios: {
            sound: alarmData.wavUrl || 'default',
            critical: true,
          },
          data: notificationData,
        },
        trigger,
      );

      console.log(t('logs.delayedAlarmScheduledSuccessfully'));
      console.log('Delayed alarm scheduled with original configuration:', notificationData);
    } catch (notificationError) {
      console.error(t('logs.errorSchedulingDelayedAlarm'), notificationError);
    }

    // 导航回主页
    navigation.navigate('MainTabs', { screen: 'Home' });
  }, [navigation, alarmId, alarmData, t]);

  // 获取闹钟数据
  useEffect(() => {
    const fetchAlarmData = async () => {
      if (!alarmId) {
        console.error('No alarm ID provided');
        setError(t('alarm.noAlarmIdProvided'));
        setIsLoading(false);
        return;
      }

      console.log('Fetching alarm data for ID:', alarmId);

      try {
        setIsLoading(true);
        // 调用API获取闹钟数据
        const response = await get(`/morningSchedule/${alarmId}`);
        console.log('Alarm data response:', JSON.stringify(response.data));

        if (!response) {
          console.error('No response received from API');
          setError(t('errors.noResponseFromApi'));
          return;
        }

        if (response.data && response.data.code === '200') {
          console.log('Successfully fetched alarm data:', response.data.data);

          if (!response.data.data) {
            console.error('Alarm data is empty');
            setError(t('alarm.alarmDataEmpty'));
            return;
          }

          // 设置闹钟数据
          const alarmDataFromApi = response.data.data;
          console.log('Setting alarm data with:', {
            id: alarmDataFromApi.id,
            setTime: alarmDataFromApi.setTime,
            timeType: alarmDataFromApi.timeType,
            morningImage: alarmDataFromApi.morningImage,
            wavUrl: alarmDataFromApi.wavUrl,
            vibration: alarmDataFromApi.vibration,
            content: alarmDataFromApi.content,
            repeatFlag: alarmDataFromApi.repeatFlag,
          });

          // 检查是否有 repeatFlag 字段
          console.log('Repeat flag value:', alarmDataFromApi.repeatFlag);
          console.log('Should show "Call in 5 minutes" button:',
            alarmDataFromApi.repeatFlag === true ||
            alarmDataFromApi.repeatFlag === 'true' ||
            alarmDataFromApi.repeatFlag === 1);

          setAlarmData(alarmDataFromApi);
        } else {
          const errorMsg = response.data?.msg || t('alarm.failedToFetchAlarmData');
          console.error('API error:', errorMsg);
          setError(errorMsg);
        }
      } catch (err) {
        console.error('Error fetching alarm data:', err);
        setError(t('alarm.fetchErrorTryAgain'));
      } finally {
        setIsLoading(false);
      }
    };

    fetchAlarmData();
  }, [alarmId, t]);

  // 播放音频和震动
  useEffect(() => {
    if (!alarmData) {
      console.log('No alarm data available, skipping audio playback and vibration');
      return;
    }

    // 检查是否需要震动
    const shouldVibrate = alarmData.vibration === true ||
                          alarmData.vibration === 'true' ||
                          alarmData.vibrationSwitch === true;

    // 如果需要震动，设置持续震动模式
    if (shouldVibrate) {
      console.log('Starting vibration');
      // 震动模式：震动300ms，暂停500ms，震动300ms，暂停500ms，无限循环
      Vibration.vibrate([300, 500, 300, 500], true);
    }

    // 如果没有音频URL，跳过音频播放
    if (!alarmData.wavUrl) {
      console.log('No audio URL available, skipping audio playback');
      return;
    }

    // 配置音频播放
    try {
      // 在 iOS 上设置混合音频模式，允许与其他应用程序的音频混合
      SoundPlayer.setMixAudio(true);
      console.log('Mixed audio mode enabled');

      // 设置音量为最大
      SoundPlayer.setVolume(1.0);
      console.log('Volume set to maximum');
    } catch (configError) {
      console.error('Error configuring audio:', configError);
    }

    console.log('Preparing to play audio from URL:', alarmData.wavUrl);

    const playAudio = async () => {
      try {
        if (!alarmData?.wavUrl) {
          console.error('No audio URL provided');
          return;
        }

        // 播放网络音频
        console.log('Playing network audio URL:', alarmData.wavUrl);

        // 检查URL是否可访问
        try {
          const response = await fetch(alarmData.wavUrl);
          console.log('URL fetch response status:', response.status);
          if (response.status >= 200 && response.status < 300) {
            console.log('URL is accessible');
          } else {
            console.error('URL returned error status:', response.status);
            return;
          }
        } catch (fetchError) {
          console.error('Error fetching URL:', fetchError);
          return;
        }

        // 播放网络音频
        SoundPlayer.playUrl(alarmData.wavUrl);
        console.log('playUrl method called successfully');
        setIsPlaying(true);
      } catch (playError) {
        console.error('Error playing audio:', playError);
        console.error('Error details:', JSON.stringify(playError));
      }
    };

    // 设置事件监听器
    const onFinishedPlayingSubscription = SoundPlayer.addEventListener('FinishedPlaying', ({ success }) => {
      console.log('FinishedPlaying event received, success:', success);
      // 循环播放
      playAudio();
    });

    const onFinishedLoadingURLSubscription = SoundPlayer.addEventListener('FinishedLoadingURL', ({ success, url }) => {
      console.log('Audio URL finished loading, success:', success);
      console.log('Loaded URL:', url);

      if (success) {
        // 尝试获取音频信息
        try {
          SoundPlayer.getInfo().then(info => {
            console.log('Audio info:', info);
            console.log('Audio duration:', info.duration);
          }).catch(infoError => {
            console.error('Error getting audio info:', infoError);
          });
        } catch (getInfoError) {
          console.error('Error calling getInfo:', getInfoError);
        }
      }
    });

    // 开始播放
    playAudio();

    // 清理函数
    return () => {
      try {
        console.log('Cleaning up sound player');
        SoundPlayer.stop();
        setIsPlaying(false);

        // 停止震动
        console.log('Stopping vibration');
        Vibration.cancel();

        // 移除事件监听器
        console.log('Removing event listeners');
        onFinishedPlayingSubscription.remove();
        onFinishedLoadingURLSubscription.remove();
        console.log('Event listeners removed');
      } catch (cleanupError) {
        console.error('Error during sound player cleanup:', cleanupError);
        console.error('Cleanup error details:', JSON.stringify(cleanupError));
      }
    };
  }, [alarmData]);

  // 如果正在加载，显示加载指示器
  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#00FF47" />
      </View>
    );
  }

  // 如果有错误，显示错误信息
  if (error) {
    return (
      <View style={styles.errorContainer}>
        <Text style={styles.errorText}>{error}</Text>
        <TouchableOpacity style={styles.errorButton} onPress={handleStop}>
          <Text style={styles.errorButtonText}>{t('common.returnToHome')}</Text>
        </TouchableOpacity>
      </View>
    );
  }

  // 检查是否有背景图
  const hasBackgroundImage = alarmData?.morningImage && alarmData.morningImage.length > 0;
  console.log('Background image URL:', alarmData?.morningImage);

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="transparent" translucent={true} />

      {hasBackgroundImage ? (
        <ImageBackground
          source={{ uri: alarmData?.morningImage }}
          style={styles.backgroundImage}
          onError={(imgError) => console.error('Background image loading error:', imgError.nativeEvent.error)}
        >
          <View style={styles.overlay}>
            <View style={styles.content}>
              <View style={styles.timeContainer}>
                <Text style={styles.timeLabel}>{t('alarm.time')}</Text>
                <Text style={styles.timeText}>{alarmData?.setTime || '00:00'}</Text>
                <Text style={styles.timeTypeText}>{alarmData?.timeType || 'AM'}</Text>
              </View>

              <View style={styles.bottomContainer}>
                <View style={styles.buttonContainer}>
                  <TouchableOpacity
                    style={styles.stopButton}
                    onPress={handleStop}
                  >
                    <Text style={styles.stopButtonText}>×</Text>
                  </TouchableOpacity>
                  <Text style={styles.stopLabel}>{t('alarm.stop')}</Text>
                </View>

                {(alarmData?.repeatFlag === true || alarmData?.repeatFlag === 'true') && (
                  <View style={styles.countdownContainer}>
                    <TouchableOpacity
                      style={styles.callButton}
                      onPress={handleDelayAlarm}
                    >
                      <Image
                        source={require('../assets/images/home/<USER>')}
                        style={styles.callIcon}
                        resizeMode="contain"
                      />
                      <Text style={styles.callText}>{t('alarm.callIn5Minutes')}</Text>
                    </TouchableOpacity>
                  </View>
                )}
              </View>
            </View>
          </View>
        </ImageBackground>
      ) : (
        <View style={styles.overlay}>
          <View style={styles.content}>
            <View style={styles.timeContainer}>
              <Text style={styles.timeLabel}>{t('alarm.time')}</Text>
              <Text style={styles.timeText}>{alarmData?.setTime || '00:00'}</Text>
              <Text style={styles.timeTypeText}>{alarmData?.timeType || 'AM'}</Text>
            </View>

            <View style={styles.bottomContainer}>
              <View style={styles.buttonContainer}>
                <TouchableOpacity
                  style={styles.stopButton}
                  onPress={handleStop}
                >
                  <Text style={styles.stopButtonText}>×</Text>
                </TouchableOpacity>
                <Text style={styles.stopLabel}>{t('alarm.stop')}</Text>
              </View>

              {(alarmData?.repeatFlag === true || alarmData?.repeatFlag === 'true') && (
                <View style={styles.countdownContainer}>
                  <TouchableOpacity
                    style={styles.callButton}
                    onPress={handleDelayAlarm}
                  >
                    <Image
                      source={require('../assets/images/home/<USER>')}
                      style={styles.callIcon}
                      resizeMode="contain"
                    />
                    <Text style={styles.callText}>{t('alarm.callIn5Minutes')}</Text>
                  </TouchableOpacity>
                </View>
              )}
            </View>
          </View>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000000',
  },
  backgroundImage: {
    flex: 1,
    width: '100%',
    height: '100%',
  },
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  content: {
    flex: 1,
    alignItems: 'center',
    width: '100%',
    paddingHorizontal: 20,
    justifyContent: 'space-between',
    paddingVertical: 40,
  },
  timeContainer: {
    alignItems: 'center',
    marginTop: 80,
  },
  bottomContainer: {
    width: '100%',
    alignItems: 'center',
    marginBottom: 40,
  },
  timeLabel: {
    color: '#D9C091',
    fontFamily: FONTS.bebasNeue.regular,
    fontSize: 24,
    letterSpacing: 2,
    marginBottom: 10,
    textAlign: 'center',
  },
  timeText: {
    color: '#D9C091',
    fontFamily: FONTS.bebasNeue.regular,
    fontSize: 60,
    letterSpacing: 2,
    textAlign: 'center',
  },
  timeTypeText: {
    color: '#D9C091',
    fontFamily: FONTS.bebasNeue.regular,
    fontSize: 30,
    letterSpacing: 2,
    marginTop: 10,
    marginBottom: 20,
    textAlign: 'center',
  },
  buttonContainer: {
    alignItems: 'center',
    marginBottom: 30,
  },
  stopButton: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 10,
  },
  stopButtonText: {
    color: '#FFFFFF',
    fontFamily: FONTS.bebasNeue.regular,
    fontSize: 40,
    lineHeight: 50,
  },
  stopLabel: {
    color: '#D9C091',
    fontFamily: FONTS.bebasNeue.regular,
    fontSize: 18,
    letterSpacing: 1,
  },
  countdownContainer: {
    width: '100%',
    alignItems: 'center',
  },
  callButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 25,
    paddingVertical: 12,
    paddingHorizontal: 30,
    minWidth: 200,
  },
  callIcon: {
    width: 20,
    height: 20,
    marginRight: 8,
    tintColor: '#FF004A',
  },
  callText: {
    color: '#FFFFFF',
    fontFamily: FONTS.pretendard.light,
    fontSize: 16,
    textAlign: 'center',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#000000',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#000000',
    padding: 20,
  },
  errorText: {
    color: '#FFFFFF',
    fontFamily: FONTS.pretendard.light,
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 20,
  },
  errorButton: {
    backgroundColor: '#7246BB',
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 10,
  },
  errorButtonText: {
    color: '#FFFFFF',
    fontFamily: FONTS.pretendard.light,
    fontSize: 16,
  },
});

export default AlarmTriggerScreen;
