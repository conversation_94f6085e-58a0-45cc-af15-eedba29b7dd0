import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Image,
  StatusBar,
  Dimensions,
  ScrollView,
  ActivityIndicator,
} from 'react-native';
import { FONTS } from '../assets/fonts';
import { IMAGES } from '../assets/images';
import { Header, SignOutConfirmModal } from '../components';
import { useNavigation, CommonActions } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { useTranslation } from 'react-i18next';

// 导入登出服务
import { logout } from '../services/auth/authService';
// 导入全局登出函数
import { setGlobalLoggedOut } from '../navigation';

// 定义导航参数类型
type RootStackParamList = {
  Home: undefined;
  Menu: undefined;
  Notice: undefined;
  Event: undefined;
  FAQ: undefined;
  About: undefined;
  Setting: undefined;
  LoginEntry: undefined;
};

type MenuScreenNavigationProp = StackNavigationProp<RootStackParamList, 'Menu'>;

const { width } = Dimensions.get('window');

// 菜单项数据（使用 i18n 键）
const getMenuItems = (t: any) => [
  {
    id: 'notice',
    title: t('menu.notice'),
    icon: require('../assets/images/menu/notice-icon.png'),
    screen: 'Notice',
  },
  {
    id: 'event',
    title: t('menu.event'),
    icon: require('../assets/images/menu/event-icon.png'),
    screen: 'Event',
  },
  {
    id: 'faq',
    title: t('menu.faq'),
    icon: require('../assets/images/menu/faq-icon.png'),
    screen: 'FAQ',
  },
  {
    id: 'about',
    title: t('menu.about'),
    icon: require('../assets/images/menu/about-icon.png'),
    screen: 'About',
  },
  {
    id: 'setting',
    title: t('menu.setting'),
    icon: require('../assets/images/menu/setting-icon.png'),
    screen: 'Setting',
  },
];

const MenuScreen = () => {
  const navigation = useNavigation<MenuScreenNavigationProp>();
  const { t } = useTranslation();

  // 使用 t 函数获取菜单项
  const menuItems = getMenuItems(t);

  // 处理菜单项点击
  const handleMenuItemPress = (screen: string) => {
    // @ts-ignore - 忽略类型检查，因为我们知道这些路由是存在的
    navigation.navigate(screen);
  };

  // 退出确认模态窗口是否显示
  const [signOutModalVisible, setSignOutModalVisible] = useState(false);

  // 显示退出确认模态窗口
  const handleSignOutPress = () => {
    setSignOutModalVisible(true);
  };

  // 处理退出登录
  const [isLoggingOut, setIsLoggingOut] = useState(false);

  const handleSignOut = async () => {
    try {
      setIsLoggingOut(true);
      console.log('Signing out...');

      // 调用登出服务，清除用户数据
      await logout();

      // 关闭模态窗口
      setSignOutModalVisible(false);

      // 尝试使用全局登出函数
      setGlobalLoggedOut(); // 尝试更新全局登录状态，但不依赖其结果

      // 无论全局登出函数是否成功，都直接导航到LoginEntry页面
      // 使用reset确保清除导航堆栈
      navigation.dispatch(
        CommonActions.reset({
          index: 0,
          routes: [{ name: 'LoginEntry' }],
        })
      );
    } catch (error) {
      console.error('Sign out error:', error);
    } finally {
      setIsLoggingOut(false);
    }
  };

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="transparent" translucent={true} />

      {/* 使用全局 Header 组件 */}
      <Header title={t('navigation.menu')} />

      <ScrollView style={styles.scrollView}>
        {/* 菜单项列表 */}
        {menuItems.map((item) => (
          <TouchableOpacity
            key={item.id}
            style={styles.menuItem}
            onPress={() => handleMenuItemPress(item.screen)}
          >
            <View style={styles.menuItemContent}>
              <Image source={item.icon} style={styles.menuIcon} />
              <Text style={styles.menuTitle}>{item.title}</Text>
            </View>
            <Image source={IMAGES.arrowRight} style={styles.arrowIcon} />
          </TouchableOpacity>
        ))}


        {/* 退出登录按钮 */}
        <TouchableOpacity
          style={styles.menuItem}
          onPress={handleSignOutPress}
        >
          <View style={styles.menuItemContent}>
            <Image
              source={require('../assets/images/menu/signout-icon.png')}
              style={[styles.menuIcon, styles.signOutIcon]}
            />
            <Text style={[styles.menuTitle, styles.signOutText]}>{t('auth.signOut')}</Text>
          </View>
          <Image source={IMAGES.arrowRight} style={styles.arrowIcon} />
        </TouchableOpacity>
      </ScrollView>

      {/* 版本信息 */}
      <View style={styles.versionContainer}>
        <Text style={styles.versionText}>{t('settings.versionInfo', { version: '1.1.0' })}</Text>
      </View>

      {/* 退出确认模态窗口 */}
      <SignOutConfirmModal
        visible={signOutModalVisible}
        onConfirm={handleSignOut}
        onCancel={() => setSignOutModalVisible(false)}
        isLoading={isLoggingOut}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000000',
  },
  scrollView: {
    flex: 1,
    paddingHorizontal: 20,
    marginTop: 20,
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 30,
    paddingHorizontal: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#333333',
  },
  menuItemContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  menuIcon: {
    height: 24,
    width: 'auto',
    aspectRatio: 1,
    marginRight: 15,
    tintColor: '#FFFFFF',
    resizeMode: 'contain',
  },
  menuTitle: {
    color: '#FFFFFF',
    fontSize: 18,
    fontFamily: FONTS.bebasNeue.regular,
    letterSpacing: 1,
  },
  arrowIcon: {
    height: 16,
    width: 'auto',
    aspectRatio: 0.5,
    tintColor: '#FFFFFF',
    resizeMode: 'contain',
  },
  separator: {
    height: 1,
    backgroundColor: '#333333',
    marginVertical: 10,
  },
  signOutIcon: {
    tintColor: '#FF0000',
  },
  signOutText: {
    color: '#FF0000',
  },
  versionContainer: {
    alignItems: 'center',
    paddingVertical: 20,
  },
  versionText: {
    color: '#666666',
    fontSize: 14,
    fontFamily: FONTS.pretendard.light,
  },
});

export default MenuScreen;
