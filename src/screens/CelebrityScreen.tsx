import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Image,
  TouchableOpacity,
  ScrollView,
  Dimensions,
  StatusBar,
  FlatList,
  ActivityIndicator,
} from 'react-native';
import { FONTS } from '../assets/fonts';
import { IMAGES } from '../assets/images';
import { Header, SortingBottomSheet } from '../components';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { useTranslation } from 'react-i18next';

// 导入名人相关服务
import {
  getCelebrityList,
  getCelebrityTypes,
  getSortingOptions,
  CelebrityItem,
  CelebrityTypeItem,
  SortingOptionItem
} from '../services/api/homeService';

// 定义导航参数类型
type RootStackParamList = {
  Home: undefined;
  Celebrity: {
    selectedCategory?: string;
  };
  Subscription: undefined;
  CelebrityDetail: {
    id: number;
    name: string;
    type: string;
    image: any;
    nm?: string; // 添加 nm 参数，可选
  };
};

type CelebrityScreenNavigationProp = StackNavigationProp<RootStackParamList, 'Celebrity'>;

// 获取屏幕尺寸
const { width: screenWidth } = Dimensions.get('window');

const CelebrityScreen = ({ route }: { route: any }) => {
  const navigation = useNavigation<CelebrityScreenNavigationProp>();
  const { t } = useTranslation();

  // 获取路由参数中的选中分类，如果没有则默认为'total'
  const initialCategory = route?.params?.selectedCategory || 'total';

  const [selectedCategory, setSelectedCategory] = useState(initialCategory);
  const [selectedCategoryNM, setSelectedCategoryNM] = useState('');
  const [sortingVisible, setSortingVisible] = useState(false);
  const [sortBy, setSortBy] = useState('');

  // 名人列表数据
  const [celebrityListData, setCelebrityListData] = useState<any[]>([]);
  // 名人列表加载状态
  const [isLoading, setIsLoading] = useState(false);

  // 分类数据
  const [categories, setCategories] = useState<Array<{
    id: string;
    name: string;
    icon: any;
    originalData?: CelebrityTypeItem | any;
  }>>([]);
  // 分类加载状态
  const [isCategoriesLoading, setIsCategoriesLoading] = useState(false);

  // 排序选项数据
  const [sortingOptions, setSortingOptions] = useState<Array<{
    id: string;
    label: string;
    value: string;
  }>>([]);
  // 排序选项加载状态
  const [isSortingOptionsLoading, setIsSortingOptionsLoading] = useState(false);

  // 获取名人类型数据
  useEffect(() => {
    const fetchCelebrityTypes = async () => {
      try {
        setIsCategoriesLoading(true);
        // 获取名人类型数据
        const data = await getCelebrityTypes();

        if (data && data.length > 0) {
          // 处理获取到的数据
          const allCategories = data.map((item) => ({
            id: item.category.toLowerCase(),
            name: item.categoryExplain || item.category || 'Unknown',
            // 使用reserve3字段作为图标URL，如果没有则使用默认图标
            icon: item.reserve3 ? { uri: item.reserve3 } :
                  item.category.toLowerCase() === 'total' ? IMAGES.categoryIcons.all :
                  IMAGES.categoryIcons[item.category.toLowerCase() as keyof typeof IMAGES.categoryIcons] ||
                  IMAGES.categoryIcons.all,
            // 保存原始数据以便后续使用
            originalData: item,
          }));

          // 确保有TOTAL选项，如果没有则添加
          if (!allCategories.some(cat => cat.id === 'total')) {
            allCategories.unshift({
              id: 'total',
              name: 'TOTAL',
              icon: IMAGES.categoryIcons.all,
              originalData: undefined,
            });
          }

          setCategories(allCategories);
        } else {
          // 如果没有数据，使用默认分类
          setCategories([]);
        }
      } catch (error) {
        console.error('Failed to fetch celebrity types:', error);
        // 出错时使用默认分类
        setCategories([]);
      } finally {
        setIsCategoriesLoading(false);
      }
    };

    fetchCelebrityTypes();
  }, []);

  // 获取排序选项数据
  useEffect(() => {
    const fetchSortingOptions = async () => {
      try {
        setIsSortingOptionsLoading(true);
        // 获取排序选项数据
        const data = await getSortingOptions();
        console.log('Sorting options data:', JSON.stringify(data));

        if (data && data.length > 0) {
          // 如果成功获取数据，更新排序选项数据
          setSortingOptions(data.map(item => ({
            id: String(item.id),
            label: item.name,
            value: item.code,
          })));
        } else {
          // 如果没有数据，使用默认排序选项
          setSortingOptions([]);
        }
      } catch (error) {
        console.error('Failed to fetch sorting options:', error);
        // 出错时使用默认排序选项
        setSortingOptions([]);
      } finally {
        setIsSortingOptionsLoading(false);
      }
    };

    fetchSortingOptions();
  }, []);

  // 获取名人列表数据
  const fetchCelebrityList = async (category:any, sortOption = sortBy) => {
    try {
      setIsLoading(true);
      // 获取名人列表数据，传递分类和排序参数
      const data = await getCelebrityList(selectedCategoryNM, sortOption);
      console.log('Celebrity list data:', JSON.stringify(data));
      if (data && data.length > 0) {
        // 如果成功获取数据，更新名人列表数据
        setCelebrityListData(data.map(item => ({
          id: item.id,
          type: item.category,
          name: item.name,
          image: item.headImage ? { uri: item.headImage } : IMAGES.defaultAvatar,
          categoryExplain: item.categoryExplain || '',
          headImage: item.headImage || '',
          category: item.category || 'UNKNOWN',
          nm: item.nm || '',
        })));
      }
    } catch (error) {
      console.error('Failed to fetch celebrity list:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // 初始加载名人列表数据
  useEffect(() => {
    fetchCelebrityList();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // 当分类或排序选项变化时，重新获取名人列表数据
  useEffect(() => {
    fetchCelebrityList(selectedCategory, sortBy);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selectedCategory, sortBy]);

  // 根据选择的分类过滤数据
  const filteredData = selectedCategory === 'total'
    ? celebrityListData
    : celebrityListData.filter(item =>
        item.type.toLowerCase() === selectedCategory.toLowerCase()
      );

  // 处理排序选项选择
  const handleSortSelect = (optionId: string) => {
    // 找到选中的排序选项
    console.log('Selected option ID:', optionId);
    const selectedOption = sortingOptions.find(option => option.id === optionId);
    if (selectedOption) {
      // 设置排序值（使用value字段）
      console.log('Selected option:', selectedOption);
      setSortBy(selectedOption.value);
      console.log(`Sorting by: ${selectedOption.label} (${selectedOption.value})`);
    }
  };

  // 打开排序底部弹窗
  const openSortingSheet = () => {
    setSortingVisible(true);
  };

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="transparent" translucent={true} />

      {/* 使用全局 Header 组件 */}
      <Header title={t('navigation.celebrity')}   rightComponentType="searchAndMenu" />

      {/* 音乐节广告 */}
      <View style={styles.festivalBanner}>
        <Image
          source={IMAGES.bannerIcon}
          style={styles.festivalImage}
          resizeMode="cover"
        />

      </View>

      {/* 分类选项卡 - 可水平滚动 */}
      <View style={styles.categoriesContainer}>
        {isCategoriesLoading ? (
          <View style={styles.categoriesLoadingContainer}>
            <ActivityIndicator size="small" color="#00FF66" />
          </View>
        ) : (
          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.categoriesScrollContent}
          >
            {categories.map(category => (
              <TouchableOpacity
                key={category.id}
                style={[
                  styles.categoryTab,
                  selectedCategory === category.id && styles.selectedCategoryTab,
                ]}
                onPress={() => {
                  // 设置选中的分类
                  setSelectedCategory(category.id);
                  setSelectedCategoryNM(category.originalData.nm)
                  // 立即重新获取列表数据
                  fetchCelebrityList(category.id, sortBy);
                }}
              >
                <Image
                  source={category.icon}
                  style={[
                    styles.categoryIcon,
                    selectedCategory === category.id && styles.selectedCategoryIcon,
                  ]}
                  resizeMode="contain"
                />
                <Text
                  style={[
                    styles.categoryText,
                    selectedCategory === category.id && styles.selectedCategoryText,
                  ]}
                >
                  {category.name}
                </Text>
              </TouchableOpacity>
            ))}
          </ScrollView>
        )}
      </View>

      {/* 分类图标 */}
      <View style={styles.categoryHeaderContainer}>
        <View style={styles.categoryHeaderSpacer} />
        <TouchableOpacity onPress={openSortingSheet}>
          <Image
            source={IMAGES.categoryIcons.all}
            style={styles.categoryHeaderIcon}
          />
        </TouchableOpacity>
      </View>

      {/* 排序底部弹窗 */}
      <SortingBottomSheet
        visible={sortingVisible}
        onClose={() => setSortingVisible(false)}
        onSelect={handleSortSelect}
        options={sortingOptions}
        title={t('sorting.title')}
        isLoading={isSortingOptionsLoading}
      />

      {/* 名人列表 */}
      <ScrollView style={styles.scrollView}>
        {isLoading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color="#00FF66" />
            <Text style={styles.loadingText}>{t('common.loading')}</Text>
          </View>
        ) : celebrityListData.length > 0 && filteredData.length > 0 ? (
          filteredData.map(item => (
            <TouchableOpacity
              key={item.id}
              style={styles.celebrityItem}
              onPress={() => navigation.navigate('CelebrityDetail', {
                id: item.id,
                name: item.name,
                type: item.category,
                image: item.image,
                nm: item.nm || '',
              })}
            >
              <Image source={item.image} style={styles.celebrityImage} />
              <View style={styles.celebrityInfo}>
                <Text style={styles.celebrityType}>{item.type}</Text>
                <Text style={styles.celebrityName}>{item.name}</Text>
              </View>
            </TouchableOpacity>
          ))
        ) : (
          <View style={styles.noDataContainer}>
            <Text style={styles.noDataText}>{t('common.noData')}</Text>
          </View>
        )}

        {/* 底部间距 */}
        <View style={styles.bottomPadding} />
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000000',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 50,
  },
  loadingText: {
    color: '#FFFFFF',
    fontFamily: FONTS.pretendard.light,
    fontSize: 14,
    marginTop: 10,
  },
  noDataContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 50,
  },
  noDataText: {
    color: '#FFFFFF',
    fontFamily: FONTS.pretendard.light,
    fontSize: 16,
  },
  categoriesLoadingContainer: {
    height: 50,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 15,
  },
  scrollView: {
    flex: 1,
  },
  festivalBanner: {
    width: screenWidth,
    height: 180,
    position: 'relative',
  },
  festivalImage: {
    width: '100%',
    height: '100%',
  },

  categoriesContainer: {
    borderBottomWidth: 1,
    borderBottomColor: '#333333',
  },
  categoryHeaderContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 15,
    paddingVertical: 10,
    justifyContent: 'space-between',
  },
  categoryHeaderSpacer: {
    flex: 1,
  },
  categoryHeaderIcon: {
    width: 24,
    height: 24,
  },
  categoriesScrollContent: {
    paddingVertical: 15,
    paddingHorizontal: 10,
  },
  categoryTab: {
    paddingHorizontal: 10,
    paddingVertical: 5,
    alignItems: 'center',
    flexDirection: 'row',
    marginHorizontal: 5,
  },
  selectedCategoryTab: {
    borderBottomWidth: 3,
    borderBottomColor: '#FF004A',
  },
  categoryIcon: {
    height:16,
    width: 20,
    marginRight: 5,
    tintColor: '#999999',
  },
  selectedCategoryIcon: {
    tintColor: '#FFFFFF',
  },
  categoryText: {
    color: '#999999',
    fontFamily: FONTS.bebasNeue.regular,
    fontSize: 18,
    letterSpacing: 1,
  },
  selectedCategoryText: {
    color: '#FFFFFF',
  },
  celebrityItem: {
    flexDirection: 'row',
    padding: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#333333',
  },
  celebrityImage: {
    width: 80,
    height: 80,
    borderRadius: 10,
  },
  celebrityInfo: {
    marginLeft: 15,
    justifyContent: 'center',
  },
  celebrityType: {
    color: '#999999',
    fontFamily: FONTS.bebasNeue.regular,
    fontSize: 14,
    marginBottom: 5,
  },
  celebrityName: {
    color: '#FFFFFF',
    fontFamily: FONTS.bebasNeue.regular,
    fontSize: 22,
    letterSpacing: 1,
  },
  bottomPadding: {
    height: 60,
  },
});

export default CelebrityScreen;
