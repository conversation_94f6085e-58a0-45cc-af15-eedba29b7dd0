import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Image,
  TouchableOpacity,
  StatusBar,
  FlatList,
  ActivityIndicator,
} from 'react-native';
import { FONTS } from '../assets/fonts';
import { Header } from '../components';
import { get } from '../services/api/apiService';
import { useNavigation, useRoute, RouteProp } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { useTranslation } from 'react-i18next';

// 定义导航参数类型
type RootStackParamList = {
  SearchResults: {
    keyword: string;
  };
  MainTabs: {
    screen: string;
  };
  CelebrityDetail: {
    id: string;
    name: string;
    type: string;
    image: any;
    nm: string;
  };
};

type SearchResultsRouteProp = RouteProp<RootStackParamList, 'SearchResults'>;
type SearchResultsNavigationProp = StackNavigationProp<RootStackParamList>;

// 搜索结果项类型
interface SearchResultItem {
  id: string | number;
  name: string;
  image?: any;
  headImage?: string;
  category?: string;
  categoryExplain?: string;
  nm?: string;
  code?: string;
  createDate?: string;
  status?: string;
  reserve1?: string;
  threeName?: string;
  categoryId?: string;
  country?: string;
  state?: string;
  collectionNumber?: number;
  numberofsubscriptions?: number;
  generateQuantity?: number;
  collectStatus?: boolean;
  subscriptStatus?: boolean;
}

// 移除未使用的导入

const SearchResultsScreen: React.FC = () => {
  const navigation = useNavigation<SearchResultsNavigationProp>();
  const route = useRoute<SearchResultsRouteProp>();
  const { keyword } = route.params;
  const { t } = useTranslation();

  const [isLoading, setIsLoading] = useState(true);
  const [searchResults, setSearchResults] = useState<SearchResultItem[]>([]);

  // 获取搜索结果
  const fetchSearchResults = useCallback(async () => {
    setIsLoading(true);
    try {
      // 调用搜索API
      const response = await get('/trend/list?keyword=' + keyword);
      console.log('Search results:', response);

      if (response && response.data && response.data.data && Array.isArray(response.data.data)) {
        setSearchResults(response.data.data);
      } else {
        setSearchResults([]);
      }
    } catch (error) {
      console.error('Error fetching search results:', error);
      setSearchResults([]);
    } finally {
      setIsLoading(false);
    }
  }, [keyword]);

  useEffect(() => {
    // 获取搜索结果
    fetchSearchResults();
  }, [fetchSearchResults]);

  // 处理菜单按钮点击
  const handleMenuPress = () => {
    console.log('Menu pressed in SearchResultsScreen');
    navigation.navigate('Menu' as never);
  };

  // 渲染搜索结果项
  const renderSearchResultItem = ({ item }: { item: SearchResultItem }) => {
    console.log('Item headImage:', item.headImage);
    return (
      <View>
        <TouchableOpacity
          style={styles.resultItem}
          onPress={() => navigation.navigate('CelebrityDetail', {
            id: item.id.toString(),
            name: item.name,
            type: item.category || '',
            image: item.headImage || '', // 使用headImage作为图片URL
            nm: item.nm || '',
          })}
        >
          <Image
            source={{ uri: item.headImage }}
            style={styles.resultImage}
            resizeMode="cover"
          />
          <View style={styles.resultInfo}>
            <Text style={styles.categoryText}>
              {item.category}
            </Text>
            <Text style={styles.nameText}>{item.name}</Text>
          </View>
        </TouchableOpacity>
        <View style={styles.resultDivider} />
      </View>
    );
  };

  // 渲染无结果视图
  const renderNoResults = () => (
    <View style={styles.noResultsWrapper}>
      <View style={styles.noResultsContainer}>
        <Text style={styles.noResultsTitle}>{t('search.noResults')}</Text>
        <View style={styles.divider} />
        <Text style={styles.noResultsText}>{t('search.unableToFindCelebrity')}</Text>
      </View>
      <TouchableOpacity
        style={styles.homeButton}
        onPress={() => navigation.navigate('MainTabs', { screen: 'Home' } as never)}
      >
        <Text style={styles.homeButtonText}>{t('common.home')}</Text>
      </TouchableOpacity>
    </View>
  );

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="transparent" translucent={true} />

      {/* 使用全局 Header 组件 */}
      <Header
        title={t('search.resultsFor', { keyword: keyword })}
        rightComponentType="menu"
        onMenuPress={handleMenuPress}
      />

      {isLoading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#FF0066" />
        </View>
      ) : searchResults.length > 0 ? (
        <FlatList
          data={searchResults}
          renderItem={renderSearchResultItem}
          keyExtractor={(item) => item.id.toString()}
          contentContainerStyle={styles.listContainer}
        />
      ) : (
        renderNoResults()
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000000',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  listContainer: {
    padding: 20,
  },
  resultItem: {
    flexDirection: 'row',
    marginBottom: 15,
    alignItems: 'center',
  },
  resultImage: {
    width: 70,
    height: 70,
    borderRadius: 10,
    marginRight: 15,
  },
  resultInfo: {
    flex: 1,
  },
  categoryText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontFamily: FONTS.bebasNeue.regular,
    marginBottom: 5,
  },
  nameText: {
    color: '#FFFFFF',
    fontSize: 24,
    fontFamily: FONTS.bebasNeue.regular,
  },
  resultDivider: {
    height: 1,
    backgroundColor: '#767676',
    marginTop: 15,
    marginBottom: 15,
  },
  noResultsWrapper: {
    flex: 1,
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingBottom: 30,
  },
  noResultsContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  noResultsTitle: {
    color: '#FF004A',
    fontSize: 32,
    fontFamily: FONTS.bebasNeue.regular,
    marginBottom: 20,
  },
  divider: {
    width: 60,
    height: 2,
    backgroundColor: '#FF004A',
    marginBottom: 20,
  },
  noResultsText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontFamily: FONTS.pretendard.light,
    textAlign: 'center',
  },
  homeButton: {
    backgroundColor: '#FF004A',
    paddingVertical: 15,
    marginHorizontal: 20,
    marginBottom: 30,
    borderRadius: 10,
    alignItems: 'center',
    justifyContent: 'center',
    height: 50,
  },
  homeButtonText: {
    color: '#FFFFFF',
    fontSize: 20,
    fontFamily: FONTS.bebasNeue.regular,
    letterSpacing: 1,
  },
});

export default SearchResultsScreen;
