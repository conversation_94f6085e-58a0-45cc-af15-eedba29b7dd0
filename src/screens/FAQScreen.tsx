import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Image,
  StatusBar,
  ScrollView,
  Dimensions,
  ActivityIndicator,
} from 'react-native';
import { FONTS } from '../assets/fonts';
import { IMAGES } from '../assets/images';
import { Header } from '../components';
import { get } from '../services/api/apiService';
import { showError } from '../services/toast';
import { useTranslation } from 'react-i18next';

const { width } = Dimensions.get('window');

const FAQScreen = () => {
  const { t } = useTranslation();
  // 状态管理
  const [faqData, setFaqData] = useState<{
    membership: Array<{
      id: string;
      question: string;
      answer: string;
      highlighted?: boolean;
    }>;
    voiceAlarm: Array<{
      id: string;
      question: string;
      answer: string;
      highlighted?: boolean;
    }>;
    voiceMessage: Array<{
      id: string;
      question: string;
      answer: string;
      highlighted?: boolean;
    }>;
  }>({
    membership: [],
    voiceAlarm: [],
    voiceMessage: [],
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [expandedItems, setExpandedItems] = useState<{ [key: string]: boolean }>({});

  // 获取FAQ数据
  const fetchFAQData = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      const response = await get('/faqProject/tree');
      console.log('FAQ response:', JSON.stringify(response));

      if (response && response.data && (response.data.code === 200 || response.data.code === '200') && response.data.data) {
        // 处理API响应数据
        const apiData = response.data.data;

        // 根据接口返回的数据结构整理数据
        const formattedData = {
          membership: [] as Array<{
            id: string;
            question: string;
            answer: string;
            highlighted?: boolean;
          }>,
          voiceAlarm: [] as Array<{
            id: string;
            question: string;
            answer: string;
            highlighted?: boolean;
          }>,
          voiceMessage: [] as Array<{
            id: string;
            question: string;
            answer: string;
            highlighted?: boolean;
          }>,
        };

        // 处理数据分类
        if (Array.isArray(apiData)) {
          apiData.forEach((category: any) => {
            // 检查分类是否有list属性
            if (category.list && Array.isArray(category.list)) {
              // 遍历每个分类下的问题列表
              category.list.forEach((item: any) => {
                const faqItem = {
                  id: item.nm || item.id || '',
                  question: item.question || 'Question',
                  answer: item.answer || '',
                  highlighted: false,
                };

                // 根据分类名称分类
                const categoryName = category.name ? category.name.toLowerCase() : '';
                if (categoryName.includes('voice alarm')) {
                  formattedData.voiceAlarm.push(faqItem);
                } else if (categoryName.includes('voice message')) {
                  formattedData.voiceMessage.push(faqItem);
                } else if (categoryName.includes('membership')) {
                  formattedData.membership.push(faqItem);
                }
              });
            }
          });
        }

        setFaqData(formattedData);
      } else {
        setError(t('faq.failedToLoad'));
      }
    } catch (err) {
      console.error('Error fetching FAQ data:', err);
      setError(t('faq.failedToLoad'));
      showError(t('faq.failedToLoad'));
    } finally {
      setLoading(false);
    }
  }, []);

  // 页面加载时获取数据
  useEffect(() => {
    fetchFAQData();
  }, [fetchFAQData]);

  // 处理FAQ项点击
  const handleFAQPress = (id: string) => {
    setExpandedItems(prev => ({
      ...prev,
      [id]: !prev[id],
    }));
  };

  // 刷新数据
  const handleRefresh = () => {
    fetchFAQData();
  };

  // 渲染FAQ项
  const renderFAQItem = (item: any) => {
    const isExpanded = expandedItems[item.id] || false;

    return (
      <View key={item.id} style={styles.faqItemContainer}>
        <TouchableOpacity
          style={[
            styles.faqItem,
            item.highlighted && styles.highlightedFaqItem,
            isExpanded && item.answer && styles.faqItemExpanded,
          ]}
          onPress={() => handleFAQPress(item.id)}
        >
          <Text
            style={styles.questionText}
            numberOfLines={isExpanded ? undefined : 1}
            ellipsizeMode="tail"
          >
            [{item.question}]
          </Text>
          <Image
            source={IMAGES.arrowRight}
            style={[
              styles.arrowIcon,
              isExpanded && styles.arrowIconExpanded,
            ]}
          />
        </TouchableOpacity>

        {isExpanded && (
          <View style={[
            styles.answerContainer,
            item.highlighted && styles.highlightedAnswerContainer,
          ]}>
            {item.answer ? (
              <Text style={styles.answerText}>{item.answer}</Text>
            ) : (
              <Text style={styles.emptyAnswerText}>{t('faq.noAnswerAvailable')}</Text>
            )}
          </View>
        )}
      </View>
    );
  };

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="transparent" translucent={true} />

      {/* 使用全局 Header 组件 */}
      <Header title={t('navigation.faq')} />

      {loading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#D9C091" />
        </View>
      ) : error ? (
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>{error}</Text>
          <TouchableOpacity style={styles.refreshButton} onPress={handleRefresh}>
            <Text style={styles.refreshButtonText}>{t('common.retry')}</Text>
          </TouchableOpacity>
        </View>
      ) : (
        <ScrollView style={styles.scrollView}>
          {/* 会员部分 */}
          <Text style={styles.sectionTitle}>{t('faq.membership')}</Text>
          {faqData.membership.length > 0 ? (
            faqData.membership.map(renderFAQItem)
          ) : (
            <Text style={styles.emptyText}>{t('faq.noMembershipFAQs')}</Text>
          )}

          {/* 语音闹钟部分 */}
          <Text style={styles.sectionTitle}>{t('faq.voiceAlarm')}</Text>
          {faqData.voiceAlarm.length > 0 ? (
            faqData.voiceAlarm.map(renderFAQItem)
          ) : (
            <Text style={styles.emptyText}>{t('faq.noVoiceAlarmFAQs')}</Text>
          )}

          {/* 语音消息部分 */}
          <Text style={styles.sectionTitle}>{t('faq.voiceMessage')}</Text>
          {faqData.voiceMessage.length > 0 ? (
            faqData.voiceMessage.map(renderFAQItem)
          ) : (
            <Text style={styles.emptyText}>{t('faq.noVoiceMessageFAQs')}</Text>
          )}

          {/* 底部间距 */}
          <View style={styles.bottomPadding} />
        </ScrollView>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000000',
  },
  scrollView: {
    flex: 1,
    paddingHorizontal: 20,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingTop: 50,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingTop: 50,
  },
  errorText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontFamily: FONTS.pretendard.light,
    marginBottom: 20,
  },
  refreshButton: {
    backgroundColor: '#D9C091',
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 5,
  },
  refreshButtonText: {
    color: '#000000',
    fontSize: 16,
    fontFamily: FONTS.pretendard.light,
  },
  emptyText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontFamily: FONTS.pretendard.light,
    textAlign: 'center',
    marginVertical: 20,
  },
  sectionTitle: {
    color: '#D9C091', // 金色文字
    fontSize: 18,
    fontFamily: FONTS.bebasNeue.regular,
    marginTop: 20,
    marginBottom: 15,
  },
  faqItemContainer: {
    marginBottom: 10,
  },
  faqItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    justifyContent: 'space-between',
    paddingVertical: 15,
    paddingHorizontal: 20,
    backgroundColor: '#222222',
    borderRadius: 30,
  },
  faqItemExpanded: {
    borderBottomLeftRadius: 0,
    borderBottomRightRadius: 0,
  },
  highlightedFaqItem: {
    borderWidth: 1,
    borderColor: '#D9C091', // 金色边框
    backgroundColor: '#1A1A1A', // 稍深一点的背景色
  },
  questionText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontFamily: FONTS.pretendard.light,
    flex: 1,
    marginRight: 10,
    lineHeight: 22,
  },
  arrowIcon: {
    height: 16,
    width: 'auto',
    aspectRatio: 1,
    tintColor: '#FFFFFF',
    resizeMode: 'contain',
  },
  arrowIconExpanded: {
    transform: [{ rotate: '90deg' }],
  },
  answerContainer: {
    backgroundColor: '#222222',
    padding: 20,
    marginTop: 0,
    borderBottomLeftRadius: 30,
    borderBottomRightRadius: 30,
    marginBottom: 10,
    borderTopWidth: 1,
    borderTopColor: '#333333',
  },
  highlightedAnswerContainer: {
    backgroundColor: '#1A1A1A',
    borderLeftWidth: 1,
    borderRightWidth: 1,
    borderBottomWidth: 1,
    borderColor: '#D9C091',
  },
  answerText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontFamily: FONTS.pretendard.light,
    lineHeight: 20,
    marginTop: 10,
  },
  emptyAnswerText: {
    color: '#999999',
    fontSize: 14,
    fontFamily: FONTS.pretendard.light,
    fontStyle: 'italic',
    marginTop: 10,
  },
  bottomPadding: {
    height: 40,
  },
});

export default FAQScreen;
