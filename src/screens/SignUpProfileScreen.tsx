import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  TextInput,
  Image,
  ScrollView,
  Dimensions,
  ActivityIndicator,
} from 'react-native';
import { useNavigation, useRoute, RouteProp } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';

// 导入图片资源
import { IMAGES } from '../assets/images';
import { FONTS } from '../assets/fonts';
import { Header, DatePickerModal } from '../components';

// 导入上下文
import { useAlert } from '../context/AlertContext';

// 导入API服务
import { post } from '../services/api/apiService';

// 导入i18n
import { useTranslation } from 'react-i18next';

// 定义导航类型
type AuthStackParamList = {
  Login: undefined;
  Agreement: undefined;
  SignUpForm: undefined;
  SignUpProfile: {
    email: string;
    password: string;
    confirmPassword: string;
  };
};

type SignUpProfileScreenNavigationProp = StackNavigationProp<AuthStackParamList, 'SignUpProfile'>;
type SignUpProfileScreenRouteProp = RouteProp<AuthStackParamList, 'SignUpProfile'>;

// 生日选择器的选项
// 在实际应用中，我们会使用 DateTimePicker 组件

const SignUpProfileScreen = () => {
  const navigation = useNavigation<SignUpProfileScreenNavigationProp>();
  const route = useRoute<SignUpProfileScreenRouteProp>();
  const { showAlert } = useAlert();
  const { t } = useTranslation();

  // 获取从 SignUpFormScreen 传递的数据
  const { email, password } = route.params;

  // 表单状态
  const [nickname, setNickname] = useState('');
  const [birthdate, setBirthdate] = useState<Date | null>(null);
  const [loading, setLoading] = useState(false);
  const [headImage, setHeadImage] = useState('');

  // 日期选择器模态框状态
  const [datePickerVisible, setDatePickerVisible] = useState(false);

  // 获取当前日期
  const currentDate = new Date();
  const currentYear = currentDate.getFullYear().toString();
  const currentMonth = String(currentDate.getMonth() + 1).padStart(2, '0');
  const currentDay = String(currentDate.getDate()).padStart(2, '0');

  const [selectedYear, setSelectedYear] = useState(currentYear);
  const [selectedMonth, setSelectedMonth] = useState(currentMonth);
  const [selectedDay, setSelectedDay] = useState(currentDay);


  // 打开日期选择器
  const openDatePicker = () => {
    setDatePickerVisible(true);
  };

  // 取消日期选择
  const cancelDateSelection = () => {
    setDatePickerVisible(false);
  };

  // 格式化日期显示
  const formatDate = (date: Date | null) => {
    if (!date) {
      return '';
    }
    return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
  };

  // 验证表单
  const validateForm = () => {
    if (!nickname || nickname.length < 3) {
      showAlert(t('signup.nicknameMinLength'));
      return false;
    }

    return true;
  };

  // 处理注册
  const handleSignUp = async () => {
    if (validateForm()) {
      setLoading(true);

      try {
        // 准备注册数据
        const registryData = {
          email: email,
          password: password,
          confirmPassword: password,
          birthday: birthdate ? formatDate(birthdate) : '',
          nickname: nickname,
          headimage: headImage,
        };

        console.log('Registering with data:', registryData);

        // 调用注册接口
        const response = await post('/account/registy', registryData);

        console.log('Registration response:', response.data);

        if (response.data && response.data.code === '200') {
          showAlert(t('signup.registrationSuccessful'));
          setTimeout(() => {
            navigation.navigate('Login');
          }, 1500);
        } else {
          const errorMsg = response.data?.msg || t('signup.registrationFailed');
          showAlert(errorMsg);
        }
      } catch (error) {
        console.error('Registration error:', error);
        showAlert(t('signup.registrationFailedTryAgain'));
      } finally {
        setLoading(false);
      }
    }
  };

  return (
    <View style={styles.container}>
      {/* 背景图片 */}
      <Image
        source={IMAGES.signupBackground}
        style={styles.backgroundImage}
        resizeMode="contain"
      />

      {/* 顶部导航 */}
      <Header showLogo={true} />

      {/* 日期选择器模态框 */}
      <DatePickerModal
        visible={datePickerVisible}
        onClose={cancelDateSelection}
        onConfirm={(year, month, day) => {
          // 创建新的日期对象
          const newDate = new Date(
            parseInt(year, 10),
            parseInt(month, 10) - 1,
            parseInt(day, 10)
          );
          setBirthdate(newDate);
          setSelectedYear(year);
          setSelectedMonth(month);
          setSelectedDay(day);
          setDatePickerVisible(false);
        }}
        initialYear={selectedYear}
        initialMonth={selectedMonth}
        initialDay={selectedDay}
      />

      <ScrollView style={styles.scrollContainer}>
        {/* 注册标题 */}
        <View style={styles.titleContainer}>
          <View style={styles.titleIndicator} />
          <Text style={styles.title}>{t('signup.title')}</Text>
        </View>

        {/* 昵称输入 */}
        <View style={styles.inputSection}>
          <Text style={styles.inputLabel}>{t('signup.enterNickname')}</Text>
          <TextInput
            style={styles.textInput}
            placeholder=""
            placeholderTextColor="#666"
            value={nickname}
            onChangeText={setNickname}
            autoCapitalize="none"
          />

          {/* 昵称格式提示 */}
          <View style={styles.warningContainer}>
            <Image source={IMAGES.warningIcon} style={styles.warningIcon} />
          </View>

          <View style={styles.requirementsList}>
            <View style={styles.requirementItem}>
              <Text style={styles.bulletPoint}>•</Text>
              <View style={styles.requirementTextContainer}>
                <Text>
                  <Text style={styles.requirementLabel}>{t('signup.configuration')} : </Text>
                  <Text style={styles.requirementText}>{t('signup.configurationValue')}</Text>
                </Text>
              </View>
            </View>
            <View style={styles.requirementItem}>
              <Text style={styles.bulletPoint}>•</Text>
              <View style={styles.requirementTextContainer}>
                <Text>
                  <Text style={styles.requirementLabel}>{t('signup.length')} : </Text>
                  <Text style={styles.requirementText}>{t('signup.lengthValueEmail')}</Text>
                </Text>
              </View>
            </View>
          </View>
        </View>

        {/* 生日输入 */}
        <View style={styles.inputSection}>
          <Text style={styles.inputLabel}>{t('signup.enterBirthday')}</Text>
          <Text style={[styles.inputLabel, { marginTop: 5 }]}>{t('signup.optional')}</Text>

          {/* 生日选择器 */}
          <View style={styles.birthdayContainer}>
            <View style={styles.datePickerRow}>
              <Text style={styles.datePickerColumnLabel}>{t('profile.year')}</Text>
              <Text style={styles.datePickerColumnLabel}>{t('profile.month')}</Text>
              <Text style={styles.datePickerColumnLabel}>{t('profile.day')}</Text>
            </View>

            <View style={styles.datePickerRow}>
              {/* 年份选择器 */}
              <TouchableOpacity
                style={styles.dateDropdown}
                onPress={openDatePicker}
              >
                <Text style={styles.dateDropdownText}>{selectedYear}</Text>
                <Image source={IMAGES.arrowRight} style={[styles.dropdownArrow, { transform: [{ rotate: '90deg' }] }]} />
              </TouchableOpacity>

              {/* 月份选择器 */}
              <TouchableOpacity
                style={styles.dateDropdown}
                onPress={openDatePicker}
              >
                <Text style={styles.dateDropdownText}>{selectedMonth}</Text>
                <Image source={IMAGES.arrowRight} style={[styles.dropdownArrow, { transform: [{ rotate: '90deg' }] }]} />
              </TouchableOpacity>

              {/* 日期选择器 */}
              <TouchableOpacity
                style={styles.dateDropdown}
                onPress={openDatePicker}
              >
                <Text style={styles.dateDropdownText}>{selectedDay}</Text>
                <Image source={IMAGES.arrowRight} style={[styles.dropdownArrow, { transform: [{ rotate: '90deg' }] }]} />
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </ScrollView>

      {/* 注册按钮 - 固定在底部 */}
      <View style={styles.bottomContainer}>
        <TouchableOpacity
          style={[styles.signUpButton, loading && styles.disabledButton]}
          onPress={handleSignUp}
          disabled={loading}
        >
          {loading ? (
            <ActivityIndicator color="#FFFFFF" size="small" />
          ) : (
            <Text style={styles.signUpButtonText}>{t('signup.title')}</Text>
          )}
        </TouchableOpacity>
      </View>
    </View>
  );
};

const { width: screenWidth } = Dimensions.get('window');

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000',
    position: 'relative',
  },
  scrollContainer: {
    flex: 1,
    backgroundColor: 'transparent',
    paddingBottom: 80, // 添加底部填充，防止内容被底部按钮遮挡
  },

  // 日期选择器模态框样式
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    justifyContent: 'flex-end', // 改为底部对齐
    alignItems: 'center',
  },
  modalContainer: {
    width: screenWidth * 0.9,
    backgroundColor: '#222',
    borderRadius: 10,
    padding: 20,
    maxHeight: '80%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 15,
    paddingBottom: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#333',
  },
  modalTitle: {
    color: '#FFFFFF',
    fontSize: 18,
    fontFamily: FONTS.bebasNeue.regular,
    letterSpacing: 1,
  },
  modalCloseButton: {
    color: '#FF0066',
    fontSize: 16,
    fontFamily: FONTS.pretendard.light,
  },
  optionsContainer: {
    maxHeight: 300,
  },
  optionItem: {
    paddingVertical: 12,
    paddingHorizontal: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#333',
  },
  selectedOption: {
    backgroundColor: 'rgba(255, 0, 102, 0.2)',
  },
  optionText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontFamily: FONTS.pretendard.light,
  },
  selectedOptionText: {
    color: '#FF0066',
    fontWeight: 'bold',
  },
  confirmButton: {
    backgroundColor: '#FF0066',
    borderRadius: 5,
    paddingVertical: 12,
    alignItems: 'center',
    marginTop: 20,
  },
  confirmButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontFamily: FONTS.bebasNeue.regular,
    letterSpacing: 1,
  },
  cancelButton: {
    flex: 1,
    height: 74 * screenWidth / 750,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#FF0066',
    marginHorizontal: 5,
    marginVertical: 5,
    borderRadius: 4,
  },
  cancelButtonText: {
    color: '#FFFFFF',
    fontSize: 18,
    fontFamily: FONTS.pretendard.light,
    fontWeight: 'bold',
  },
  submitButton: {
    flex: 1,
    height: 74 * screenWidth / 750,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#007BFF',
    marginHorizontal: 5,
    marginVertical: 5,
    borderRadius: 4,
  },
  submitButtonText: {
    color: '#FFFFFF',
    fontSize: 18,
    fontWeight: 'bold',
    fontFamily: FONTS.pretendard.light,
  },
  backgroundImage: {
    position: 'absolute',
    top: '50%',
    transform: [{ translateY: -150 }],
    right: -30,
    width: 300,
    height: 300,
    zIndex: 0,
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    marginTop: 20,
    marginBottom: 30,
    zIndex: 1,
  },
  titleIndicator: {
    width: 4,
    height: 24,
    backgroundColor: '#FF0066',
    marginRight: 10,
  },
  title: {
    color: '#FFFFFF',
    fontSize: 24,
    fontFamily: FONTS.bebasNeue.regular,
    letterSpacing: 1,
  },
  inputSection: {
    paddingHorizontal: 20,
    marginBottom: 20,
    zIndex: 1,
  },
  inputLabel: {
    color: '#FFFFFF',
    fontSize: 16,
    fontFamily: FONTS.bebasNeue.regular,
    letterSpacing: 1,
    marginBottom: 10,
  },
  inputLabel2: {
    color: '#FFFFFF',
    fontSize: 24,
    fontFamily: FONTS.bebasNeue.regular,
    letterSpacing: 1,
    textAlign: 'center',
    marginBottom: 10,
  },
  textInput: {
    borderBottomWidth: 1,
    borderBottomColor: '#FFFFFF',
    color: '#FFFFFF',
    fontSize: 16,
    fontFamily: FONTS.pretendard.light,
    paddingVertical: 10,
  },
  warningContainer: {
    alignItems: 'center',
    marginVertical: 10,
  },
  warningIcon: {
    width: 24,
    height: 24,
    tintColor: '#FFFFFF',
    resizeMode: 'contain'
  },
  requirementsList: {
    marginTop: 10,
  },
  requirementItem: {
    flexDirection: 'row',
    marginBottom: 10,
    alignItems: 'flex-start',
  },
  requirementTextContainer: {
    flex: 1,
  },
  bulletPoint: {
    color: '#FF9800',
    fontSize: 16,
    marginRight: 5,
    marginTop: -2,
  },
  requirementLabel: {
    color: '#FF9800',
    fontSize: 14,
    fontFamily: FONTS.pretendard.light,
  },
  requirementText: {
    color: '#BBBBBB',
    fontSize: 14,
    fontFamily: FONTS.pretendard.light,
  },
  birthdayContainer: {
    marginTop: 20,
  },
  datePickerRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 15,
  },
  datePickerColumnLabel: {
    color: '#999',
    fontSize: 12,
    fontFamily: FONTS.pretendard.light,
    flex: 1,
    textAlign: 'center',
    marginBottom: 5,
  },
  dateDropdown: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 10,
    marginHorizontal: 5,
    borderRadius: 4,
  },
  dateDropdownText: {
    color: '#FFFFFF',
    fontSize: 20,
    fontFamily: FONTS.pretendard.light,
    fontWeight: 'bold',
  },
  dropdownArrow: {
    width: 12,
    height: 12,
    tintColor: '#FFFFFF',
    marginLeft: 5,
  },
  bottomContainer: {
    width: '100%',
    alignItems: 'center',
    paddingVertical: 20,
    paddingBottom: 40,
    backgroundColor: '#000',
    position: 'absolute',
    bottom: 0,
    zIndex: 10,
  },
  signUpButton: {
    backgroundColor: '#FF0066',
    borderRadius: 5,
    paddingVertical: 15,
    paddingHorizontal: 30,
    width: '90%',
    alignItems: 'center',
  },
  signUpButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontFamily: FONTS.bebasNeue.regular,
    letterSpacing: 1,
  },
  disabledButton: {
    backgroundColor: '#666666',
    opacity: 0.7,
  },
});

export default SignUpProfileScreen;
