import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Image,
  TouchableOpacity,
  StatusBar,
  ActivityIndicator,
  ScrollView,
  Modal,
  Share,
  Linking,
  Platform,
} from 'react-native';
import { IMAGES } from '../assets/images';
import { FONTS } from '../assets/fonts';
import { SHARE_ICONS } from '../assets/images/share';
import { get, del } from '../services/api/apiService';
import { useNavigation, useRoute, RouteProp } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import LinearGradient from 'react-native-linear-gradient';
import { showSuccess, showError } from '../services/toast';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Header, ShareBottomSheet } from '../components';
import { useTranslation } from 'react-i18next';
// 定义导航参数类型
type RootStackParamList = {
  VoiceMessageDetail: {
    id: string | number;
    name?: string;
    image?: any;
    nm?: string;
  };
  MainTabs: {
    screen: string;
    params?: any;
  };
};

type VoiceMessageDetailScreenNavigationProp = StackNavigationProp<RootStackParamList, 'VoiceMessageDetail'>;
type VoiceMessageDetailScreenRouteProp = RouteProp<RootStackParamList, 'VoiceMessageDetail'>;

const VoiceMessageDetailScreen = () => {
  const navigation = useNavigation<VoiceMessageDetailScreenNavigationProp>();
  const route = useRoute<VoiceMessageDetailScreenRouteProp>();
  const { t } = useTranslation();

  // 从路由参数中获取消息ID和其他信息
  const { id, name, image, nm } = route.params || {};

  // 状态管理
  const [messageData, setMessageData] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [showShareOptions, setShowShareOptions] = useState(false);

  // 在组件挂载时获取消息详情
  useEffect(() => {
    const fetchMessageDetail = async () => {
      if (!id) {
        console.log('No message ID provided');
        setError(t('voiceMessage.messageIdNotExist'));
        setIsLoading(false);
        return;
      }

      try {
        setIsLoading(true);
        setError(null);

        // 调用语音消息详情接口
        console.log('Fetching voice message detail for ID:', id, 'nm:', nm);
        // 如果有 nm 参数，可以将其添加到请求中
        const endpoint = nm ? `/voiceMessage/${id}?nm=${nm}` : `/voiceMessage/${id}`;
        const response = await get(endpoint);
        console.log('Voice message detail response:', response.data);

        if (response.data && (response.data.code === '200' || response.data.code === 200)) {
          // 保存消息详情数据
          setMessageData(response.data.data);
        } else {
          setError(response.data?.msg || t('voiceMessage.failedToFetchMessageDetails'));
          console.error('Failed to fetch message details:', response.data);
        }
      } catch (err) {
        console.error('Error fetching message detail:', err);
        setError(t('voiceMessage.errorFetchingMessageDetails'));
      } finally {
        setIsLoading(false);
      }
    };

    fetchMessageDetail();
  }, [id, nm]);





  // 处理搜索按钮点击
  const handleSearchPress = () => {
    console.log('Search pressed in VoiceMessageDetailScreen');
  };

  // 处理菜单按钮点击
  const handleMenuPress = () => {
    console.log('Menu pressed in VoiceMessageDetailScreen');
  };

  // 处理删除按钮点击
  const handleDeletePress = () => {
    setShowDeleteConfirm(true);
  };

  // 取消删除
  const handleCancelDelete = () => {
    setShowDeleteConfirm(false);
  };

  // 处理分享按钮点击
  const handleSharePress = () => {
    setShowShareOptions(true);
  };

  // 处理分享选项选择
  const handleShareOptionSelect = async (option: string) => {
    if (!messageData) {
      return;
    }

    const shareMessage = t('voiceMessage.checkOutVoiceMessage', { name: messageData.voicePersonName || name });
    const shareUrl = `https://aivgen.com/share/message/${id}`;

    try {
      switch (option) {
        case 'facebook':
          // 分享到Facebook
          const fbUrl = `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(shareUrl)}`;
          await Linking.openURL(fbUrl);
          break;
        case 'instagram':
          // Instagram不支持直接分享链接，可以复制到剪贴板
          await Share.share({
            message: `${shareMessage} ${shareUrl}`,
            url: shareUrl,
          });
          break;
        case 'line':
          // 分享到LINE
          const lineUrl = `https://social-plugins.line.me/lineit/share?url=${encodeURIComponent(shareUrl)}`;
          await Linking.openURL(lineUrl);
          break;
        case 'talk':
          // 分享到KakaoTalk
          const kakaoUrl = `https://accounts.kakao.com/login?continue=https://story.kakao.com/s/share?url=${encodeURIComponent(shareUrl)}`;
          await Linking.openURL(kakaoUrl);
          break;
        case 'link':
          // 复制链接
          await Share.share({
            message: shareUrl,
            url: shareUrl,
          });
          break;
        default:
          // 默认分享
          await Share.share({
            message: `${shareMessage} ${shareUrl}`,
            url: shareUrl,
          });
      }
    } catch (err) {
      console.error('Error sharing:', err);
      showError(t('voiceMessage.failedToShare'));
    }
  };

  // 确认删除
  const handleConfirmDelete = async () => {
    if (!id) {
      showError(t('voiceMessage.messageIdNotExist'));
      return;
    }

    try {
      setIsDeleting(true);

      // 调用删除API
      const response = await del(`/voiceMessage/${id}`);
      console.log('Delete message response:', response.data);

      if (response.data && (response.data.code === '200' || response.data.code === 200)) {
        showSuccess(t('voiceMessage.messageDeletedSuccessfully'));

        // 延迟返回上一级页面
        setTimeout(() => {
          // 存储刷新标志
          AsyncStorage.setItem('@aivgen:message_refresh', 'true');
          // 返回上一级页面
          navigation.goBack();
        }, 1000);
      } else {
        showError(response.data?.msg || t('voiceMessage.failedToDeleteMessage'));
      }
    } catch (err) {
      console.error('Error deleting message:', err);
      showError(t('voiceMessage.errorDeletingMessage'));
    } finally {
      setIsDeleting(false);
      setShowDeleteConfirm(false);
    }
  };

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="transparent" translucent={true} />

      {/* 使用封装好的 Header 组件 */}
      <Header
        title={t('voiceMessage.title')}
        rightComponentType="menuAndSearch"
        onSearchPress={handleSearchPress}
        onMenuPress={handleMenuPress}
        showBack={true}
      />

      {isLoading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#D9C091" />
        </View>
      ) : error ? (
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>{error}</Text>
        </View>
      ) : (
        <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
          {/* 名人头像和名称 */}
          <View style={styles.profileSection}>
            <LinearGradient
              colors={['#00FF75', '#4C0099']}
              start={{ x: 0, y: 0 }}
              end={{ x: 0, y: 1 }}
              style={styles.profileImageBorder}
            >
              <Image
                source={
                  messageData?.voiceHeadImages
                    ? { uri: messageData.voiceHeadImages }
                    : (image || IMAGES.defaultAvatar)
                }
                style={styles.profileImage}
              />
            </LinearGradient>
            <Text style={styles.profileName}>
              {messageData?.voicePersonName || name || 'JECLINDE MUZENHEIM'}
            </Text>
          </View>

          {/* 删除按钮 - 放在消息内容上方 */}
          <TouchableOpacity style={styles.trashButton} onPress={handleDeletePress}>
            <Image source={require('../assets/images/home/<USER>')} style={styles.trashIcon} />
          </TouchableOpacity>

          {/* 消息内容 */}
          <View style={styles.messageBox}>
            <Text style={styles.messageText}>
              {messageData?.messageText || 'Dear Jessy.\nHappy 15th birthday.\nI was surprised when you grew up like this.\nI will always be watching, so I hope\nyou will always be a bright and hard-working lady.'}
            </Text>
          </View>

          {/* 背景图片 */}
          <View style={styles.backgroundImageWrapper}>
            <Image
              source={
                messageData?.voicePersonImages
                  ? { uri: messageData.voicePersonImages }
                  : require('../assets/images/home/<USER>')
              }
              style={styles.backgroundImage}
            />
          </View>

          {/* 分享按钮 */}
          <TouchableOpacity style={styles.shareButton} onPress={handleSharePress}>
            <Image source={require('../assets/images/home/<USER>')} style={styles.shareIcon} />
            <Text style={styles.shareText}>{t('voiceMessage.share')}</Text>
          </TouchableOpacity>
        </ScrollView>
      )}

      {/* 底部导航栏 */}
      <View style={styles.tabBar}>
        <TouchableOpacity
          style={styles.tabItem}
          onPress={() => navigation.navigate('MainTabs', { screen: 'Home' })}
        >
          <Image source={IMAGES.tabIcons.home} style={styles.tabIcon} />
          <Text style={styles.tabText}>{t('tabBar.home')}</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.tabItem}
          onPress={() => navigation.navigate('MainTabs', { screen: 'Subscription' })}
        >
          <Image source={IMAGES.tabIcons.subscript} style={styles.tabIcon} />
          <Text style={styles.tabText}>{t('tabBar.subscript')}</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.tabItem}
          onPress={() => navigation.navigate('MainTabs', { screen: 'Inbox' })}
        >
          <Image source={IMAGES.tabIcons.inbox} style={[styles.tabIcon, styles.activeTabIcon]} />
          <Text style={[styles.tabText, styles.activeTabText]}>{t('tabBar.inbox')}</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.tabItem}
          onPress={() => navigation.navigate('MainTabs', { screen: 'Profile' })}
        >
          <Image source={IMAGES.tabIcons.my} style={styles.tabIcon} />
          <Text style={styles.tabText}>{t('tabBar.my')}</Text>
        </TouchableOpacity>
      </View>

      {/* 删除确认对话框 */}
      <Modal
        visible={showDeleteConfirm}
        transparent={true}
        animationType="fade"
        onRequestClose={handleCancelDelete}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContainer}>
            <View style={styles.modalHandle} />

            <Text style={styles.modalTitle}>{t('common.delete')}</Text>

            <View style={styles.modalDivider} />

            <Text style={styles.modalText}>
              {t('voiceMessage.deleteConfirmationMessage')}
            </Text>

            <View style={styles.modalButtonsContainer}>
              <TouchableOpacity
                style={[styles.modalButton, styles.modalButtonNo]}
                onPress={handleCancelDelete}
              >
                <Text style={styles.modalButtonText}>{t('common.no')}</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[styles.modalButton, styles.modalButtonYes]}
                onPress={handleConfirmDelete}
                disabled={isDeleting}
              >
                {isDeleting ? (
                  <ActivityIndicator color="#FFFFFF" size="small" />
                ) : (
                  <Text style={styles.modalButtonText}>{t('common.yes')}</Text>
                )}
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>

      {/* 分享选项底部弹出菜单 */}
      <ShareBottomSheet
        visible={showShareOptions}
        onClose={() => setShowShareOptions(false)}
        onSelect={handleShareOptionSelect}
        options={[
          { id: 'line', label: 'LINE', icon: SHARE_ICONS.line },
          { id: 'talk', label: 'KakaoTalk', icon: SHARE_ICONS.talk },
          { id: 'facebook', label: 'Facebook', icon: SHARE_ICONS.facebook },
          { id: 'instagram', label: 'Instagram', icon: SHARE_ICONS.instagram },
          { id: 'link', label: 'Copy Link', icon: SHARE_ICONS.link },
        ]}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000000',
  },

  // 加载和错误状态样式
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    color: '#FF3B30',
    fontSize: 16,
    textAlign: 'center',
  },
  // 内容区域样式
  scrollView: {
    flex: 1,
  },
  // 头像和名称区域
  profileSection: {
    alignItems: 'center',
    marginTop: 24,
    marginBottom: 16,
  },
  profileImageBorder: {
    width: 120,
    height: 120,
    borderRadius: 60,
    padding: 2,
    justifyContent: 'center',
    alignItems: 'center',
  },
  profileImage: {
    width: 116,
    height: 116,
    borderRadius: 58,
    backgroundColor: '#000',
  },
  profileName: {
    color: '#FFFFFF',
    fontSize: 18,
    fontWeight: 'bold',
    marginTop: 12,
    textAlign: 'center',
  },
  // 删除按钮样式
  trashButton: {
    alignSelf: 'flex-end',
    marginRight: 16,
    marginBottom: 8,
    padding: 8,
  },
  trashIcon: {
    width: 24,
    height: 24,
    tintColor: '#FFFFFF',
    resizeMode: 'contain',
  },
  // 消息内容样式
  messageBox: {
    marginHorizontal: 16,
    marginBottom: 24,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    borderWidth: 1,
    borderColor: '#333333',
    borderRadius: 8,
    padding: 16,
    minHeight: 130, // 增加高度以容纳5行文本
  },
  messageText: {
    color: '#FFFFFF',
    fontSize: 16,
    lineHeight: 24,
  },
  // 背景图片样式
  backgroundImageWrapper: {
    marginHorizontal: 16,
    height: 250, // 根据设计稿设置高度为250px
    marginBottom: 24,
  },
  backgroundImage: {
    width: '100%',
    height: '100%',
    resizeMode: 'cover',
  },
  // 分享按钮样式
  shareButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#7246BB',
    marginHorizontal: 16,
    marginBottom: 32,
    height: 48,
    borderRadius: 8, // 按照设计稿要求
  },
  shareIcon: {
    width: 20,
    height: 20,
    marginRight: 8,
  },
  shareText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '500',
  },
  // 底部导航栏样式
  tabBar: {
    flexDirection: 'row',
    backgroundColor: '#000000',
    borderTopWidth: 1,
    borderTopColor: '#333333',
    height: 60,
    paddingBottom: 8,
  },
  tabItem: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  tabIcon: {
    width: 24,
    height: 24,
    marginBottom: 4,
    tintColor: '#767676',
  },
  activeTabIcon: {
    tintColor: '#D9C091',
  },
  tabText: {
    fontSize: 12,
    color: '#767676',
  },
  activeTabText: {
    color: '#D9C091',
  },

  // 删除确认对话框样式
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContainer: {
    width: '90%',
    backgroundColor: '#1A1A1A',
    borderRadius: 20,
    padding: 20,
    alignItems: 'center',
  },
  modalHandle: {
    width: 40,
    height: 4,
    backgroundColor: '#666666',
    borderRadius: 2,
    marginBottom: 20,
  },
  modalTitle: {
    color: '#D9C091',
    fontFamily: FONTS.bebasNeue.regular,
    fontSize: 28,
    letterSpacing: 2,
    marginBottom: 15,
  },
  modalDivider: {
    width: '100%',
    height: 1,
    backgroundColor: '#333333',
    marginBottom: 20,
  },
  modalText: {
    color: '#FFFFFF',
    fontSize: 16,
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 30,
  },
  modalButtonsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
  },
  modalButton: {
    flex: 1,
    height: 50,
    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
    marginHorizontal: 8,
  },
  modalButtonNo: {
    backgroundColor: '#FF3B30',
  },
  modalButtonYes: {
    backgroundColor: '#007AFF',
  },
  modalButtonText: {
    color: '#FFFFFF',
    fontSize: 18,
    fontWeight: 'bold',
  },
});

export default VoiceMessageDetailScreen;
