import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  Image,
  StatusBar,
  ScrollView,
  Dimensions,
  Linking,
  TouchableOpacity,
} from 'react-native';
import { FONTS } from '../assets/fonts';
import { IMAGES } from '../assets/images';
import { Header } from '../components';
import { useTranslation } from 'react-i18next';

const { width } = Dimensions.get('window');

const AboutScreen = () => {
  const { t } = useTranslation();

  // 应用信息
  const appInfo = {
    version: '1.1.0',
    developer: 'AIVgen Team',
    email: '<EMAIL>',
    website: 'https://www.aivgen.com',
  };

  // 处理链接点击
  const handleLinkPress = (url: string) => {
    Linking.openURL(url).catch((err) => console.error('Error opening URL:', err));
  };

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="transparent" translucent={true} />

      {/* 使用全局 Header 组件 */}
      <Header title={t('menu.about')} />

      <ScrollView style={styles.scrollView}>
        {/* 应用 Logo */}
        <View style={styles.logoContainer}>
          <Image source={IMAGES.logo} style={styles.logo} />
          <Text style={styles.appName}>AIVgen</Text>
          <Text style={styles.versionText}>{t('settings.versionInfo', { version: appInfo.version })}</Text>
        </View>

        {/* 应用描述 */}
        <View style={styles.sectionContainer}>
          <Text style={styles.sectionTitle}>{t('about.aboutAivgen')}</Text>
          <Text style={styles.descriptionText}>
            {t('about.description')}
          </Text>
        </View>

        {/* 联系信息 */}
        <View style={styles.sectionContainer}>
          <Text style={styles.sectionTitle}>{t('about.contactUs')}</Text>

          <View style={styles.contactItem}>
            <Text style={styles.contactLabel}>{t('about.email')}:</Text>
            <TouchableOpacity onPress={() => handleLinkPress(`mailto:${appInfo.email}`)}>
              <Text style={styles.contactValue}>{appInfo.email}</Text>
            </TouchableOpacity>
          </View>

          <View style={styles.contactItem}>
            <Text style={styles.contactLabel}>{t('about.website')}:</Text>
            <TouchableOpacity onPress={() => handleLinkPress(appInfo.website)}>
              <Text style={styles.contactValue}>{appInfo.website}</Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* 版权信息 */}
        <View style={styles.copyrightContainer}>
          <Text style={styles.copyrightText}>
            {t('about.copyright')}
          </Text>
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000000',
  },
  scrollView: {
    flex: 1,
    paddingHorizontal: 20,
  },
  logoContainer: {
    alignItems: 'center',
    marginTop: 40,
    marginBottom: 30,
  },
  logo: {
    width: 200,
    resizeMode: 'contain',
    marginBottom: 15,
  },
  appName: {
    color: '#FFFFFF',
    fontSize: 28,
    fontFamily: FONTS.bebasNeue.regular,
    letterSpacing: 2,
    marginBottom: 5,
  },
  versionText: {
    color: '#999999',
    fontSize: 16,
    fontFamily: FONTS.pretendard.light,
  },
  sectionContainer: {
    marginBottom: 30,
    backgroundColor: '#1A1A1A',
    borderRadius: 10,
    padding: 20,
  },
  sectionTitle: {
    color: '#D9C091', // 金色文字
    fontSize: 18,
    fontFamily: FONTS.bebasNeue.regular,
    marginBottom: 15,
    letterSpacing: 1,
  },
  descriptionText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontFamily: FONTS.pretendard.light,
    lineHeight: 24,
  },
  contactItem: {
    flexDirection: 'row',
    marginBottom: 10,
  },
  contactLabel: {
    color: '#FFFFFF',
    fontSize: 16,
    fontFamily: FONTS.pretendard.medium,
    width: 80,
  },
  contactValue: {
    color: '#D9C091', // 金色文字
    fontSize: 16,
    fontFamily: FONTS.pretendard.light,
    textDecorationLine: 'underline',
  },
  copyrightContainer: {
    alignItems: 'center',
    marginTop: 20,
    marginBottom: 40,
  },
  copyrightText: {
    color: '#666666',
    fontSize: 14,
    fontFamily: FONTS.pretendard.light,
    textAlign: 'center',
  },
});

export default AboutScreen;
