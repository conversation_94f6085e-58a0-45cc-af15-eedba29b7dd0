import React, { useState, useEffect, useRef, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Image,
  TouchableOpacity,
  StatusBar,
  FlatList,
  ActivityIndicator,
  Alert,
  Linking,
} from 'react-native';
import { FONTS } from '../assets/fonts';
import { IMAGES } from '../assets/images';
import { Header } from '../components';
import { useNavigation, useRoute, RouteProp } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { post, get } from '../services/api/apiService';
import { useTranslation } from 'react-i18next';
import { showSuccess, showError } from '../services/toast';
import notificationService from '../services/notificationService';

// 定义导航参数类型
type RootStackParamList = {
  Home: undefined;
  Celebrity: undefined;
  Subscription: undefined;
  Inbox: {
    activeTab?: string;
  };
  SelectCeleb: {
    sourceTab?: TabType;
  };
  Alarm: {
    id: number;
    name: string;
    type: string;
    image: any;
  };
  EditVoiceAlarm: {
    alarmId: number | string;
    name: string;
    image: any;
    nm?: string;
  };
  VoiceMessageDetail: {
    id: string | number;
    name?: string;
    image?: any;
    nm?: string;
    status?: 'normal' | 'countdown' | 'reject';
  };
};

type InboxScreenNavigationProp = StackNavigationProp<RootStackParamList, 'Inbox'>;
type InboxScreenRouteProp = RouteProp<RootStackParamList, 'Inbox'>;

// 标签页类型
type TabType = 'alarm' | 'message' | 'coupon';

// 闹钟数据类型
type AlarmItem = {
  id: number;
  name: string;
  image: any;
  title: string;
  time: string;
  days: string;
  status: 'active' | 'countdown' | 'download' | 'reject';
  countdown?: string;
  makeFlag?: number;
  wavUrl?: string;
  nm?: string;
  morningImage?: string;
  setWeek?: string;
  vibration?: boolean;
  repeatFlag?: boolean;
  setTime?: string;
};

// 消息数据类型
type MessageItem = {
  id: number;
  nm?: string;
  code?: string;
  name: string;
  image: any;
  date: string;
  status: 'normal' | 'countdown' | 'reject';
  countdown?: string;
  createDate?: string;
  setTime?: string;
  messageText?: string;
  voicePersonImages?: string;
  voiceHeadImages?: string;
  generationTime?: string;
  voicePersonName?: string;
  packageInformationVoiceMessageNm?: string;
  type?: string;
  createUser?: string;
};

// 语音消息数据和闹钟数据将从API获取

// 优惠券数据类型
type CouponItem = {
  id: number;
  title: string;
  date: string;
  count: number;
  color: string;
};

const InboxScreen = () => {
  const navigation = useNavigation<InboxScreenNavigationProp>();
  const route = useRoute<InboxScreenRouteProp>();
  const { t } = useTranslation();
  const [activeTab, setActiveTab] = useState<TabType>('alarm');
  const [isLoading, setIsLoading] = useState(false);
  const [_error, setError] = useState<string | null>(null);
  const [userInfo, setUserInfo] = useState<any>(null);
  const [realMessageData, setRealMessageData] = useState<MessageItem[]>([]);
  const messageDataRef = useRef<MessageItem[]>([]);
  const [countdowns, setCountdowns] = useState<{[key: string]: string}>({}); // 用于存储消息倒计时
  const [alarmCountdowns, setAlarmCountdowns] = useState<{[key: string]: string}>({}); // 用于存储闹钟倒计时
  const [couponList, setCouponList] = useState<CouponItem[]>([]); // 用于存储优惠券数据
  const [isCouponLoading, setIsCouponLoading] = useState(false); // 用于控制优惠券加载状态
  const [alarmListData, setAlarmListData] = useState<AlarmItem[]>([]); // 用于存储闹钟列表数据
  const [isAlarmLoading, setIsAlarmLoading] = useState(false); // 用于控制闹钟列表加载状态

  // 在组件挂载时检查并设置 activeTab 参数
  useEffect(() => {
    if (route.params?.activeTab) {
      // 将字符串参数转换为 TabType
      const tabParam = route.params.activeTab as TabType;
      if (tabParam === 'alarm' || tabParam === 'message' || tabParam === 'coupon') {
        setActiveTab(tabParam);
      }
    }
  }, [route.params]);

  // 获取用户信息
  useEffect(() => {
    const getUserInfo = async () => {
      try {
        const userInfoStr = await AsyncStorage.getItem('@aivgen:user_info');
        if (userInfoStr) {
          const info = JSON.parse(userInfoStr);
          setUserInfo(info);
        }
      } catch (err) {
        console.error('Error getting user info:', err);
      }
    };

    getUserInfo();
  }, []);

  // 获取闹钟列表
  const fetchAlarmList = useCallback(async () => {
    // 如果没有用户信息，则不调用接口
    if (!userInfo || !userInfo.nm || activeTab !== 'alarm') {
      return;
    }

    try {
      setIsAlarmLoading(true);

      const params = {
        curAccount: userInfo.nm,
      };

      console.log('调用闹钟列表接口，参数:', params);
      const response = await post('/morningSchedule/list', params);
      console.log('闹钟列表接口响应:', JSON.stringify(response.data));

      // 处理返回的数据
      if (response.data && response.data.code === '200' && response.data.data) {
        const alarms = response.data.data.map((item: any) => {
          // 直接使用 setWeek 字段
          const daysText = item.setWeek || '';

          // 处理时间显示
          const timeDisplay = item.setTime || '00:00';

          // 获取makeFlag和wavUrl
          const makeFlag = item.makeFlag !== undefined ? item.makeFlag : 1; // 默认为1
          const wavUrl = item.wavUrl || '';

          // 根据makeFlag和wavUrl确定状态
          let status: 'active' | 'countdown' | 'download' | 'reject' = 'active';

          if (makeFlag === 0) {
            status = 'countdown'; // After状态
          } else if (makeFlag === 1) {
            if (wavUrl) {
              status = 'download'; // 当makeFlag=1且wavUrl有值时为download状态
            } else {
              status = 'active'; // 当makeFlag=1且wavUrl无值时为正常状态
            }
          } else if (makeFlag === 2) {
            status = 'reject'; // Reject状态
          }

          return {
            id: item.id || Math.random(),
            name: item.voicePersonName || 'Unknown',
            image: item.morningImage ? { uri: item.morningImage } : IMAGES.defaultAvatar,
            title: item.morningName || 'Alarm',
            time: timeDisplay,
            days: daysText,
            status: status,
            countdown: item.generationTime || '',
            makeFlag: makeFlag,
            wavUrl: wavUrl,
            nm: item.voicePersonNm,
            morningImage: item.morningImage,
            setWeek: item.setWeek,
            vibration: item.vibration,
            repeatFlag: item.repeatFlag,
          };
        });

        setAlarmListData(alarms);
        // 重置刷新标志
        await AsyncStorage.setItem('@aivgen:alarm_refresh', 'false');
      } else {
        setAlarmListData([]);
      }
    } catch (err) {
      console.error('Error fetching alarm list:', err);
      setError('获取闹钟列表失败');
    } finally {
      setIsAlarmLoading(false);
    }
  }, [userInfo, activeTab]);

  // 检查是否需要刷新闹钟列表
  useEffect(() => {
    const checkAlarmRefresh = async () => {
      try {
        const shouldRefresh = await AsyncStorage.getItem('@aivgen:alarm_refresh');
        if (shouldRefresh === 'true') {
          console.log('检测到闹钟刷新标志，重新获取数据');
          fetchAlarmList();
        }
      } catch (err) {
        console.error('Error checking alarm refresh flag:', err);
      }
    };

    // 添加事件监听器，当应用从后台恢复时检查刷新标志
    const unsubscribe = navigation.addListener('focus', () => {
      checkAlarmRefresh();
    });

    // 初始检查
    checkAlarmRefresh();

    // 清理事件监听器
    return () => unsubscribe();
  }, [navigation, fetchAlarmList]);

  // 原有的获取闹钟列表的 useEffect
  useEffect(() => {
    fetchAlarmList();
  }, [fetchAlarmList]);

  // 获取语音消息列表
  useEffect(() => {
    const fetchVoiceMessages = async () => {
      // 如果没有用户信息，则不调用接口
      if (!userInfo || !userInfo.nm) {
        return;
      }

      try {
        setIsLoading(true);
        setError(null);

        const params = {
          createUser: userInfo.nm,
        };

        console.log('调用语音消息列表接口，参数:', params);
        const response = await post('/voiceMessage/list', params);
        console.log('语音消息列表接口响应:', JSON.stringify(response.data));

        // 处理返回的数据
        if (response.data && response.data.code === '200' && response.data.data) {
          const messages = response.data.data.map((item: any) => {
            // 根据 status 字段确定显示状态
            let status: 'normal' | 'countdown' | 'reject' = 'normal';

            if (item.status === '0') {
              status = 'countdown'; // AFTER状态
            } else if (item.status === '2') {
              status = 'reject'; // reject状态
            }

            // 格式化日期
            const createDate = item.createDate ? new Date(item.createDate) : new Date();
            const formattedDate = `${createDate.getFullYear()}.${String(createDate.getMonth() + 1).padStart(2, '0')}.${String(createDate.getDate()).padStart(2, '0')}`;

            // 不需要计算初始倒计时，将在useEffect中处理

            return {
              id: item.id,
              nm: item.nm,
              code: item.code,
              name: item.voicePersonName || 'Unknown',
              image: item.voicePersonImages ? { uri: item.voicePersonImages } : IMAGES.defaultAvatar,
              date: formattedDate,
              status: status,
              countdown: '',  // 初始化为空，将在倒计时函数中更新
              createDate: item.createDate,
              setTime: item.setTime,
              messageText: item.messageText,
              voicePersonImages: item.voicePersonImages,
              voiceHeadImages: item.voiceHeadImages,
              generationTime: item.generationTime,
              voicePersonName: item.voicePersonName,
              packageInformationVoiceMessageNm: item.packageInformationVoiceMessageNm,
              type: item.type,
              createUser: item.createUser,
            };
          });

          setRealMessageData(messages);
          messageDataRef.current = messages;
        } else {
          setRealMessageData([]);
          messageDataRef.current = [];
        }
      } catch (err) {
        console.error('Error fetching voice messages:', err);
        setError('获取语音消息列表失败');
      } finally {
        setIsLoading(false);
      }
    };

    fetchVoiceMessages();
  }, [userInfo]);

  // 解析时间字符串为小时、分钟、秒
  const parseTimeString = (timeString: string) => {
    if (!timeString) {
      return { hours: 0, minutes: 0, seconds: 0 };
    }

    const parts = timeString.split(':');
    if (parts.length !== 3) {
      return { hours: 0, minutes: 0, seconds: 0 };
    }

    return {
      hours: parseInt(parts[0], 10) || 0,
      minutes: parseInt(parts[1], 10) || 0,
      seconds: parseInt(parts[2], 10) || 0,
    };
  };

  // 格式化倒计时时间
  const formatCountdown = (hours: number, minutes: number, seconds: number) => {
    return `${String(hours).padStart(2, '0')}:${String(minutes).padStart(2, '0')}:${String(seconds).padStart(2, '0')}`;
  };

  // 消息倒计时效果
  useEffect(() => {
    // 初始化倒计时
    const initialCountdowns: {[key: string]: string} = {};

    realMessageData.forEach(item => {
      if (item.status === 'countdown' && item.generationTime) {
        const { hours, minutes, seconds } = parseTimeString(item.generationTime);
        initialCountdowns[item.id.toString()] = formatCountdown(hours, minutes, seconds);
      }
    });

    setCountdowns(initialCountdowns);

    // 每秒更新倒计时
    const timer = setInterval(() => {
      setCountdowns(prevCountdowns => {
        const newCountdowns = { ...prevCountdowns };

        Object.keys(newCountdowns).forEach(itemId => {
          const timeString = newCountdowns[itemId];
          const { hours, minutes, seconds } = parseTimeString(timeString);

          // 计算新的倒计时时间
          let newSeconds = seconds - 1;
          let newMinutes = minutes;
          let newHours = hours;

          if (newSeconds < 0) {
            newSeconds = 59;
            newMinutes -= 1;
          }

          if (newMinutes < 0) {
            newMinutes = 59;
            newHours -= 1;
          }

          // 如果倒计时结束，则移除该项
          if (newHours < 0) {
            delete newCountdowns[itemId];
          } else {
            newCountdowns[itemId] = formatCountdown(newHours, newMinutes, newSeconds);
          }
        });

        return newCountdowns;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, [realMessageData]);

  // 闹钟倒计时效果
  useEffect(() => {
    // 初始化闹钟倒计时
    const initialAlarmCountdowns: {[key: string]: string} = {};

    alarmListData.forEach(item => {
      if (item.makeFlag === 0 && item.countdown) {
        const { hours, minutes, seconds } = parseTimeString(item.countdown);
        initialAlarmCountdowns[item.id.toString()] = formatCountdown(hours, minutes, seconds);
      }
    });

    setAlarmCountdowns(initialAlarmCountdowns);

    // 每秒更新闹钟倒计时
    const alarmTimer = setInterval(() => {
      setAlarmCountdowns(prevCountdowns => {
        const newCountdowns = { ...prevCountdowns };

        Object.keys(newCountdowns).forEach(itemId => {
          const timeString = newCountdowns[itemId];
          const { hours, minutes, seconds } = parseTimeString(timeString);

          // 计算新的倒计时时间
          let newSeconds = seconds - 1;
          let newMinutes = minutes;
          let newHours = hours;

          if (newSeconds < 0) {
            newSeconds = 59;
            newMinutes -= 1;
          }

          if (newMinutes < 0) {
            newMinutes = 59;
            newHours -= 1;
          }

          // 如果倒计时结束，则移除该项
          if (newHours < 0) {
            delete newCountdowns[itemId];
          } else {
            newCountdowns[itemId] = formatCountdown(newHours, newMinutes, newSeconds);
          }
        });

        return newCountdowns;
      });
    }, 1000);

    return () => clearInterval(alarmTimer);
  }, [alarmListData]);

  // 获取优惠券数据
  useEffect(() => {
    const fetchCoupons = async () => {
      // 如果没有用户信息或不是优惠券标签页，则不调用接口
      if (!userInfo || !userInfo.nm || activeTab !== 'coupon') {
        return;
      }

      try {
        setIsCouponLoading(true);

        // 调用接口获取优惠券数据
        const response = await get('/couponAccount/hold', {
          accountNm: userInfo.nm,
        });

        console.log('优惠券数据响应:', JSON.stringify(response.data));

        // 处理返回的数据
        if (response.data && response.data.code === '200' && response.data.data) {
          // 将API返回的数据转换为CouponItem格式
          const coupons = response.data.data.map((item: any, index: number) => {
            // 根据索引设置不同的颜色
            const colors = ['#D9C091', '#ADFF2F', '#FF1493'];
            const colorIndex = index % colors.length;

            return {
              id: item.id || index + 1,
              title: item.couponName,
              date: item.couponEndTime,
              count: item.couponNum,
              color: colors[colorIndex],
            };
          });

          setCouponList(coupons);
        } else {
          setCouponList([]);
        }
      } catch (err) {
        console.error('Error fetching coupons:', err);
        setCouponList([]);
      } finally {
        setIsCouponLoading(false);
      }
    };

    fetchCoupons();
  }, [userInfo, activeTab]);

  // 处理标签页切换
  const handleTabPress = (tab: TabType) => {
    setActiveTab(tab);
  };

  // 处理添加新闹钟或消息
  const handleAddAlarm = () => {
    console.log('Add new item from tab:', activeTab);
    // 导航到选择名人页面，并传递来源标签页参数
    navigation.navigate('SelectCeleb', { sourceTab: activeTab });
  };

  // 处理搜索按钮点击
  const handleSearchPress = () => {
    console.log('Search pressed in InboxScreen');
    // 这里添加搜索功能的逻辑
  };

  // 处理菜单按钮点击
  const handleMenuPress = () => {
    console.log('Menu pressed in InboxScreen');
    // 导航到菜单页面
    navigation.navigate('Menu' as never);
  };

  // 处理闹钟设置
  const handleSetAlarm = async (item: AlarmItem) => {
    try {
      // 请求通知权限
      const hasPermission = await notificationService.requestNotificationPermissions();

      if (!hasPermission) {
        Alert.alert(
          t('alarm.notificationPermissionRequired'),
          t('alarm.notificationPermissionMessage'),
          [
            { text: t('common.cancel'), style: 'cancel' },
            {
              text: t('settings.openSettings'),
              onPress: () => Linking.openSettings(),
            },
          ]
        );
        return;
      }

      // 显示加载状态
      setIsLoading(true);

      // 从闹钟项中获取必要的数据
      const {
        id,
        title,
        time,
        days,
        wavUrl,
        setTime: itemSetTime,
      } = item;

      // 获取原始数据中的字段
      const morningImage = item.morningImage || '';
      const weekDays = item.setWeek || days;
      const vibration = item.vibration !== undefined ? item.vibration : true;
      const repeatFlag = item.repeatFlag !== undefined ? item.repeatFlag : true;
      const timeToUse = itemSetTime || time;

      // 解析时间
      const [hours, minutes] = timeToUse.split(':').map((part: string) => part.trim());

      // 创建闹钟日期（设置为下一个符合条件的日期）
      const now = new Date();
      const alarmDate = new Date();

      // 设置小时和分钟
      let hour = parseInt(hours, 10);
      if (hour < 12) {
        hour += 12;
      }

      alarmDate.setHours(hour);
      alarmDate.setMinutes(parseInt(minutes, 10));
      alarmDate.setSeconds(0);

      // 如果时间已经过去，设置为明天
      if (alarmDate.getTime() <= now.getTime()) {
        alarmDate.setDate(alarmDate.getDate() + 1);
      }

      // 获取时间戳
      const timestamp = alarmDate.getTime();

      // 准备通知数据
      const alarmData = {
        alarmId: id,
        title: title || 'Alarm',
        time: timeToUse,
        morningImage,
        wavUrl,
        days: weekDays,
        vibration: vibration ? 'true' : 'false', // 转换为字符串
        repeatFlag: repeatFlag ? 'true' : 'false', // 转换为字符串
        setWeek: weekDays,
        setTime: timeToUse,
        screen: 'AlarmTrigger' // 添加导航目标屏幕
      };

      // 设置闹钟
      const notificationId = await notificationService.scheduleAlarm(
        id,
        title || 'Alarm',
        `Your alarm for ${timeToUse} is ready`,
        timestamp,
        alarmData,
        wavUrl
      );

      // 显示成功消息
      showSuccess(t('alarm.alarmSaved'));

      console.log('Alarm set with ID:', notificationId);

    } catch (err) {
      console.error('Error setting alarm:', err);
      showError(t('alarm.failedToSetAlarm'));
    } finally {
      setIsLoading(false);
    }
  };

  // 渲染闹钟列表项
  const renderAlarmItem = ({ item }: { item: AlarmItem }) => {
    // 根据makeFlag和wavUrl确定状态
    let backgroundColor = '#6A4BFF'; // 默认背景色
    let overlay = null;
    let isClickable = false;

    // 基本内容，所有闹钟都会显示
    const baseContent = (
      <View style={styles.alarmContent}>
        <Text style={styles.alarmTitle}>{item.title}</Text>
        <View style={styles.timeContainer}>
          <Text style={styles.timeText}>
            <Text style={styles.pmText}>PM</Text> {item.time}
          </Text>
        </View>
        <Text style={styles.daysText}>{item.days}</Text>
      </View>
    );

    // 根据makeFlag和wavUrl确定状态和UI
    const makeFlag = item.makeFlag !== undefined ? item.makeFlag : 1;
    const wavUrl = item.wavUrl || '';

    if (makeFlag === 0) {
      if (wavUrl) {
        // Download状态 (当makeFlag=0且wavUrl有值时)
        backgroundColor = '#333333';
        overlay = (
          <>
            <Text style={styles.statusText}>{t('inbox.download')}</Text>
            <View style={styles.downloadIconContainer}>
              <TouchableOpacity
                style={styles.downloadButton}
                onPress={() => handleSetAlarm(item)}
              >
                <Image
                  source={require('../assets/images/home/<USER>')}
                  style={styles.downloadIcon}
                />
              </TouchableOpacity>
            </View>
            {/* <Text style={styles.nameTexts}>{item.name}</Text> */}
          </>
        );
        isClickable = true; // 可点击
      } else {
        // After状态 (倒计时)
        backgroundColor = '#333333';
        overlay = (
          <>
            <Text style={styles.statusText}>{t('inbox.after')}</Text>
            <Text style={styles.countdownText}>{alarmCountdowns[item.id.toString()] || item.countdown || '00:00:00'}</Text>
            <Text style={styles.nameTexts}>{item.name}</Text>
          </>
        );
        isClickable = true; // 可点击
      }
    } else if (makeFlag === 1) {
      if (wavUrl) {
        // Download状态
        backgroundColor = '#333333';
        overlay = (
          <>
            <Text style={styles.statusText}>{t('inbox.download')}</Text>
            <View style={styles.downloadIconContainer}>
              <TouchableOpacity
                style={styles.downloadButton}
                onPress={() => handleSetAlarm(item)}
              >
                <Image
                  source={require('../assets/images/home/<USER>')}
                  style={styles.downloadIcon}
                />
              </TouchableOpacity>
            </View>
            {/* <Text style={styles.nameTexts}>{item.name}</Text> */}
          </>
        );
        isClickable = true; // 可点击
      } else {
        // 正常状态
        backgroundColor = '#6A4BFF';
        isClickable = true; // 可点击
      }
    } else if (makeFlag === 2) {
      // Reject状态
      backgroundColor = '#333333';
      overlay = (
        <>
          {/* <Text style={styles.statusText}>{item.title}</Text> */}
          <Text style={styles.rejectText}>{t('inbox.reject')}</Text>
          {/* <Text style={styles.nameTexts}>{item.name}</Text> */}
        </>
      );
      isClickable = true; // 可点击
    }

    // 处理点击事件
    const handleItemPress = () => {
      // 获取用户的 nm 参数，如果有的话
      // 导航到 EditVoiceAlarm 页面，参考 AlarmScreen 的实现
      navigation.navigate('EditVoiceAlarm', {
        alarmId: item.id,
        name: item.name,
        image: item.image,
        nm: item.nm, 
      });
    };

    if (makeFlag === 1 && !wavUrl) {
      // 正常状态，没有遮罩
      return (
        <TouchableOpacity
          style={[styles.alarmItem, { backgroundColor }]}
          onPress={handleItemPress}
        >
          <Image source={item.image} style={styles.alarmImage} />
          {baseContent}
        </TouchableOpacity>
      );
    } else {
      // 有遮罩的状态
      return (
        <TouchableOpacity
          style={[styles.alarmItem, { backgroundColor }]}
          onPress={isClickable ? handleItemPress : undefined}
          disabled={!isClickable}
        >
          <Image source={item.image} style={styles.alarmImage} />
          {baseContent}
          <View style={styles.fullOverlay}>
            {overlay}
          </View>
        </TouchableOpacity>
      );
    }
  };

  // 渲染优惠券列表项
  const renderCouponItem = ({ item }: { item: CouponItem }) => {
    return (
      <View style={styles.couponItem}>
        <View style={styles.couponLeftSection}>
          <View style={styles.couponTicket}>
            <Image
              source={require('../assets/images/home/<USER>')}
              style={[styles.ticketImage, { tintColor: item.color }]}
            />
          </View>
          <View style={styles.couponInfo}>
            <Text style={styles.couponTitle}>{item.title}</Text>
            <Text style={styles.couponDate}>{item.date}</Text>
          </View>
        </View>
        <View style={styles.couponDivider} />
        <View style={styles.couponCount}>
          <Text style={styles.couponCountText}>X {item.count}</Text>
        </View>
      </View>
    );
  };

  // 处理消息项点击
  const handleMessageItemPress = (item: MessageItem) => {
    console.log('Message item pressed:', item.id);
    // 导航到消息详情页面，无论消息状态如何
    navigation.navigate('VoiceMessageDetail', {
      id: item.id,
      name: item.name,
      image: item.image,
      nm: item.nm,
      status: item.status, // 传递状态，以便详情页面可以相应地显示
    });
  };

  // 渲染消息列表项
  const renderMessageItem = ({ item }: { item: MessageItem }) => {
    // 根据状态渲染不同的内容
    let content;
    let backgroundColor = '#0088FF'; // 默认背景色
    let overlay = null;

    // 基本内容，所有消息都会显示
    const baseContent = (
      <View style={styles.messageContent}>
        <Text style={styles.messageDate}>{item.date}</Text>
        <Text style={styles.messageName}>{item.name}</Text>
      </View>
    );

    if (item.status === 'normal') {
      backgroundColor = '#0088FF';
      content = baseContent;
    } else if (item.status === 'countdown') {
      backgroundColor = '#333333';
      overlay = (
        <>
          <Text style={styles.statusText}>{t('inbox.after')}</Text>
          <Text style={styles.countdownText}>{countdowns[item.id.toString()] || item.generationTime || '00:00:00'}</Text>
          {/* <Text style={styles.nameText}>{item.name}</Text> */}
        </>
      );
      content = (
        <View style={styles.messageContentWithOverlay}>
          {baseContent}
          {overlay}
        </View>
      );
    } else if (item.status === 'reject') {
      backgroundColor = '#333333';
      overlay = (
        <>
          <Text style={styles.messageDate}>{item.date}</Text>
          <Text style={styles.rejectText}>{t('inbox.reject')}</Text>
          <Text style={styles.nameText}>{item.name}</Text>
        </>
      );
      content = (
        <View style={styles.messageContentWithOverlay}>
          {baseContent}
          {overlay}
        </View>
      );
    }

    if (item.status === 'normal') {
      return (
        <TouchableOpacity
          style={[styles.alarmItem, { backgroundColor }]}
          onPress={() => handleMessageItemPress(item)}
        >
          <Image source={item.image} style={styles.alarmImage} />
          {content}
        </TouchableOpacity>
      );
    } else {
      // 对于需要遮罩的状态，将遮罩层放在整个项目上，但仍然可以点击
      return (
        <TouchableOpacity
          style={[styles.alarmItem, { backgroundColor }]}
          onPress={() => handleMessageItemPress(item)}
        >
          <Image source={item.image} style={styles.alarmImage} />
          <View style={styles.messageContent}>
            <Text style={styles.messageDate}>{item.date}</Text>
            <Text style={styles.messageName}>{item.name}</Text>
          </View>
          <View style={styles.fullOverlay}>
            {overlay}
          </View>
        </TouchableOpacity>
      );
    }
  };

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="transparent" translucent={true} />

      {/* 使用全局 Header 组件 */}
      <Header
        title={t('navigation.inbox')}
        rightComponentType="menuAndSearch"
        onSearchPress={handleSearchPress}
        onMenuPress={handleMenuPress}
        showBack={false}
      />

      {/* 标签页 */}
      <View style={styles.tabsContainer}>
        <TouchableOpacity
          style={[styles.tabButton, activeTab === 'alarm' && styles.activeTabButton]}
          onPress={() => handleTabPress('alarm')}
        >
          <Image
            source={require('../assets/images/home/<USER>')}
            style={[styles.tabIcon, activeTab === 'alarm' && styles.activeTabIcon]}
          />
          <Text style={[styles.tabText, activeTab === 'alarm' && styles.activeTabText]}>
            {t('inbox.voiceAlarm')}
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.tabButton, activeTab === 'message' && styles.activeTabButton]}
          onPress={() => handleTabPress('message')}
        >
          <Image
            source={require('../assets/images/home/<USER>')}
            style={[styles.tabIcon, activeTab === 'message' && styles.activeTabIcon]}
          />
          <Text style={[styles.tabText, activeTab === 'message' && styles.activeTabText]}>
            {t('inbox.voiceMessage')}
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.tabButton, activeTab === 'coupon' && styles.activeTabButton]}
          onPress={() => handleTabPress('coupon')}
        >
          <Image
            source={require('../assets/images/home/<USER>')}
            style={[styles.tabIcon, activeTab === 'coupon' && styles.activeTabIcon]}
          />
          <Text style={[styles.tabText, activeTab === 'coupon' && styles.activeTabText]}>
            {t('inbox.coupon')}
          </Text>
        </TouchableOpacity>
      </View>

      {/* 标题 */}
      <View style={styles.titleContainer}>
        {(activeTab === 'alarm' && alarmListData.length > 0) ||
         (activeTab === 'message' && realMessageData.length > 0) ||
         (activeTab === 'coupon' && couponList.length > 0) ? (
          <Text style={styles.titleText}>
            {activeTab === 'alarm' ? t('inbox.celebVoiceAlarmList') :
             activeTab === 'message' ? t('inbox.celebVoiceMessageList') :
             activeTab === 'coupon' ? t('inbox.celebCouponList') : ''}
          </Text>
        ) : null}
      </View>

      {/* 闹钟列表 */}
      {activeTab === 'alarm' && (
        <>
          {isAlarmLoading ? (
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="large" color="#D9C091" />
            </View>
          ) : (
            <FlatList
              data={alarmListData}
              renderItem={renderAlarmItem}
              keyExtractor={(item) => item.id.toString()}
              contentContainerStyle={styles.listContainer}
              ListEmptyComponent={
                <View style={styles.emptyContainer}>
                  <Text style={styles.emptyText}>{t('inbox.noAlarms')}</Text>
                </View>
              }
              ListFooterComponent={
                <View style={styles.addContainerInline}>
                  {/* 如果列表为空，显示 Add voice alarm 文本 */}
                  {alarmListData.length === 0 && (
                    <Text style={styles.addText}>{t('inbox.addVoiceAlarm')}</Text>
                  )}
                  {/* 无论列表是否有数据，都显示 Add 图标按钮 */}
                  <TouchableOpacity onPress={handleAddAlarm}>
                    <Image
                      source={require('../assets/images/home/<USER>')}
                      style={styles.addIcon}
                    />
                  </TouchableOpacity>
                </View>
              }
            />
          )}
        </>
      )}

      {/* 消息列表 */}
      {activeTab === 'message' && (
        <>
          {isLoading ? (
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="large" color="#D9C091" />
            </View>
          ) : (
            <FlatList
              data={realMessageData}
              renderItem={renderMessageItem}
              keyExtractor={(item) => item.id.toString()}
              contentContainerStyle={styles.listContainer}
              ListEmptyComponent={
                <View style={styles.emptyContainer}>
                  <Text style={styles.emptyText}>{t('inbox.noVoiceMessages')}</Text>
                </View>
              }
              ListFooterComponent={
                <View style={styles.addContainerInline}>
                  {/* 如果列表为空，显示 Add voice message 文本 */}
                  {realMessageData.length === 0 && (
                    <Text style={styles.addText}>{t('inbox.addVoiceMessage')}</Text>
                  )}
                  {/* 无论列表是否有数据，都显示 Add 图标按钮 */}
                  <TouchableOpacity onPress={handleAddAlarm}>
                    <Image
                      source={require('../assets/images/home/<USER>')}
                      style={styles.addIcon}
                    />
                  </TouchableOpacity>
                </View>
              }
            />
          )}
        </>
      )}

      {/* 优惠券列表 */}
      {activeTab === 'coupon' && (
        <>
          {isCouponLoading ? (
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="large" color="#D9C091" />
            </View>
          ) : (
            <FlatList
              data={couponList}
              renderItem={renderCouponItem}
              keyExtractor={(item) => item.id.toString()}
              contentContainerStyle={styles.listContainer}
              ListEmptyComponent={
                <View style={styles.emptyContainer}>
                  <Text style={styles.emptyText}>{t('inbox.noCoupons')}</Text>
                </View>
              }
            />
          )}
        </>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000000',
  },
  headerRight: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  searchButton: {
    marginRight: 15,
  },
  menuButton: {},
  icon: {
    width: 24,
    height: 24,
    tintColor: '#FFFFFF',
  },
  tabsContainer: {
    flexDirection: 'row',
    borderBottomWidth: 1,
    borderBottomColor: '#333333',
  },
  tabButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 15,
    paddingHorizontal: 5,
  },
  activeTabButton: {
    borderBottomWidth: 2,
    borderBottomColor: '#D9C091',
  },
  tabIcon: {
    width: 20,
    height: 20,
    marginRight: 5,
    resizeMode: 'contain',
    tintColor: '#767676',
  },
  activeTabIcon: {
    tintColor: '#D9C091',
  },
  tabText: {
    color: '#767676',
    fontSize: 18,
    fontFamily: FONTS.bebasNeue.regular,
  },
  activeTabText: {
    color: '#D9C091',
  },
  titleContainer: {
    paddingVertical: 15,
    paddingHorizontal: 20,
    alignItems: 'center',
  },
  titleText: {
    color: '#D9C091',
    fontSize: 20,
    fontFamily: FONTS.bebasNeue.regular,
    letterSpacing: 1,
    textAlign: 'center',
  },
  listContainer: {
    paddingHorizontal: 20,
    paddingBottom: 20, // 减少底部边距
  },
  alarmItem: {
    flexDirection: 'row',
    borderRadius: 15,
    marginBottom: 15,
    overflow: 'hidden',
  },
  alarmImage: {
    width: 80,
    height: 80,
    borderRadius: 40,
    margin: 10,
  },
  alarmContent: {
    flex: 1,
    padding: 15,
    justifyContent: 'center',
  },
  alarmTitle: {
    color: '#FFFFFF',
    fontSize: 20,
    fontFamily: FONTS.bebasNeue.regular,
  },
  timeContainer: {
    flexDirection: 'row',
    alignItems: 'baseline',
    marginVertical: 5,
  },
  pmText: {
    color: '#00FF66',
    fontSize: 20,
    fontFamily: FONTS.bebasNeue.regular,
  },
  timeText: {
    color: '#FFFFFF',
    fontSize: 24,
    fontFamily: FONTS.bebasNeue.regular,
  },
  daysText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontFamily: FONTS.pretendard.light,
  },
  statusText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontFamily: FONTS.pretendard.light,
    fontWeight: 'bold',
  },
  countdownText: {
    color: '#FF4D4D',
    fontSize: 24,
    fontFamily: FONTS.pretendard.light,
    fontWeight: 'bold',
    marginVertical: 5,
  },
  nameText: {
    color: '#AAAAAA',
    fontSize: 14,
    fontFamily: FONTS.pretendard.light,
  },
  nameTexts: {
    color: '#AAAAAA',
    fontSize: 14,
    fontFamily: FONTS.pretendard.light,
  },
  downloadIconContainer: {
    marginVertical: 5,
  },
  downloadButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#4285F4',
    alignItems: 'center',
    justifyContent: 'center',
  },
  downloadIcon: {
    width: 40,
    height: 40,
  },
  rejectText: {
    color: '#FF4D4D',
    fontSize: 24,
    fontFamily: FONTS.pretendard.light,
    fontWeight: 'bold',
    marginVertical: 5,
  },
  emptyContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 30,
  },
  emptyText: {
    color: '#767676',
    fontSize: 16,
    fontFamily: FONTS.pretendard.light,
  },
  loadingContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 50,
  },
  addContainerInline: {
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 10,
    marginBottom: 20, // 减少底部边距
    width: '100%',
  },
  addText: {
    color: '#FFFFFF',
    fontFamily: FONTS.bebasNeue.regular,
    fontSize: 18,
    letterSpacing: 1,
    marginBottom: 10,
    textAlign: 'center',
  },
  addIcon: {
    width: 30,
    height: 30,
    tintColor: '#FFFFFF',
  },
  messageContent: {
    flex: 1,
    padding: 15,
    justifyContent: 'center',
  },
  messageContentWithOverlay: {
    flex: 1,
    position: 'relative',
  },
  messageOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 15,
  },
  fullOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 10,
  },
  messageDate: {
    color: '#FFFFFF',
    fontSize: 16,
    fontFamily: FONTS.pretendard.light,
    fontWeight: 'bold',
  },
  messageName: {
    color: '#FFFFFF',
    fontSize: 20,
    fontFamily: FONTS.pretendard.light,
    fontWeight: 'bold',
    marginTop: 5,
  },
  // 优惠券样式
  couponItem: {
    flexDirection: 'row',
    backgroundColor: '#1E1E1E',
    borderRadius: 15,
    marginBottom: 15,
    overflow: 'hidden',
    height: 100,
    borderWidth: 1,
    borderColor: '#333333',
    borderStyle: 'dashed',
  },
  couponLeftSection: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 15,
  },
  couponTicket: {
    width: 60,
    height: 60,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 15,
  },
  ticketImage: {
    width: 50,
    height: 50,
    resizeMode: 'contain',
  },
  couponInfo: {
    flex: 1,
  },
  couponTitle: {
    color: '#FFFFFF',
    fontSize: 16,
    fontFamily: FONTS.bebasNeue.regular,
    fontWeight: 'bold',
  },
  couponDate: {
    color: '#AAAAAA',
    fontSize: 14,
    fontFamily: FONTS.pretendard.light,
    marginTop: 5,
  },
  couponDivider: {
    width: 1,
    backgroundColor: '#333333',
    height: '100%',
  },
  couponCount: {
    width: 80,
    justifyContent: 'center',
    alignItems: 'center',
  },
  couponCountText: {
    color: '#FFFFFF',
    fontSize: 20,
    fontFamily: FONTS.bebasNeue.regular,
    fontWeight: 'bold',
  },
});

export default InboxScreen;
