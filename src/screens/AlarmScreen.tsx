import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Image,
  TouchableOpacity,
  ScrollView,
  StatusBar,
  Dimensions,
  SafeAreaView,
  ActivityIndicator,
  Alert,
  Linking,
  Platform,
} from 'react-native';
import { useTranslation } from 'react-i18next';
import LinearGradient from 'react-native-linear-gradient';
import { FONTS } from '../assets/fonts';
import { IMAGES } from '../assets/images';
import { Header } from '../components';
import { get, post } from '../services/api/apiService';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useNavigation, useRoute, RouteProp, useFocusEffect } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import {
  requestNotificationPermission,
  setNotificationPermissionModalHandler,
  saveNotificationPermissionStatus
} from '../services/permissions';
import NotificationPermissionModal from '../components/NotificationPermissionModal';
import { showSuccess, showError } from '../services/toast';
import notificationService from '../services/notificationService';
import { downloadAudioFile, checkLocalAudioFile } from '../services/downloadService';

// 定义导航参数类型
type RootStackParamList = {
  Home: undefined;
  Celebrity: undefined;
  Subscription: undefined;
  TierList: {
    subscriptionType?: 'upgrade' | 'downgrade' | 'new';
    celebrityNm?: string;
  };
  CelebrityDetail: {
    id: number;
    name: string;
    type: string;
    image: any;
  };
  Alarm: {
    id: number;
    name: string;
    type: string;
    image: any;
    nm?: string; // 添加nm参数
  };
  CreateVoiceAlarm: {
    id: number;
    name: string;
    image: any;
    nm?: string; // 添加nm参数
  };
  EditVoiceAlarm: {
    alarmId: number | string;
    name: string;
    image: any;
    nm?: string;
  };
  AlarmTrigger: {
    title?: string;
    time?: string;
    timeType?: string;
    backgroundImage?: string;
    wavUrl?: string;
    alarmId?: number | string;
  };
  MainTabs: {
    screen?: string;
    params?: any;
  };
  Profile: undefined;
  Inbox: undefined;
};

const { width } = Dimensions.get('window');
// 设计稿宽度为750，计算缩放比例
const scale = width / 750;

// 定义路由参数类型
type AlarmRouteParams = {
  id?: number;
  name?: string;
  type?: string;
  image?: any;
  nm?: string; // 添加nm参数
  refresh?: boolean; // 添加刷新参数
  timestamp?: number; // 添加时间戳参数
};

// 闹钟数据类型
type AlarmItem = {
  id: number;
  name: string;
  image: any;
  title: string;
  time: string;
  days: string;
  status: 'active' | 'countdown' | 'download' | 'reject';
  countdown?: string;
  timeType?: string;
  makeFlag?: number; // 0 = After, 1 = 当前没有遮罩, 2 = reject
  wavUrl?: string; // 如果有值且makeFlag=1，则为download状态
  code?: string;
  nm?: string;
  createDate?: string;
  flag?: boolean;
  reserve1?: string;
  reserve2?: string;
  voicePersonNm?: string;
  setWeek?: string;
  morningImage?: string;
  morningName?: string;
  curAccount?: string;
  weather?: string;
  weatherSwitch?: boolean;
  dressSwitch?: boolean;
  vibrationSwitch?: boolean;
  vibration?: boolean;
  content?: string;
  generationTime?: string;
  setTime?: string;
  repeatFlag?: boolean; // 添加重复标志
};

// 模拟闹钟数据
const alarmData: AlarmItem[] = [
  {
    id: 1,
    name: 'ARINDA GRANDE',
    image: IMAGES.defaultAvatar,
    title: 'AFTERNOON WORK BEGINS',
    time: '02 : 00',
    days: 'Every Mon, Tue, Wed, Thu',
    status: 'active',
    timeType: 'PM',
  },
  {
    id: 2,
    name: 'ARINDA GRANDE',
    image: IMAGES.defaultAvatar,
    title: 'AFTER',
    time: '01:59:48',
    days: 'ARINDA GRANDE',
    status: 'countdown',
    countdown: '01:59:48',
    timeType: 'PM',
  },
  {
    id: 3,
    name: 'ARINDA GRANDE',
    image: IMAGES.defaultAvatar,
    title: 'DOWNLOAD',
    time: '',
    days: 'ARINDA GRANDE',
    status: 'download',
    timeType: 'PM',
  },
  {
    id: 4,
    name: 'ARINDA GRANDE',
    image: IMAGES.defaultAvatar,
    title: '2023-12-28',
    time: '',
    days: 'ARINDA GRANDE',
    status: 'reject',
    timeType: 'PM',
  },
];

type AlarmRouteProp = RouteProp<
  { Alarm: AlarmRouteParams },
  'Alarm'
>;

type AlarmScreenNavigationProp = StackNavigationProp<RootStackParamList, 'Alarm'>;

const AlarmScreen = () => {
  const navigation = useNavigation<AlarmScreenNavigationProp>();
  const route = useRoute<AlarmRouteProp>();
  // 获取翻译函数
  const { t } = useTranslation();

  // 从路由参数中获取名人信息
  const { id, name, type, image, nm, refresh, timestamp } = route.params || {
    id: 1,
    name: 'ARINDA GRANDE',
    type: 'MUSICIAN',
    image: IMAGES.defaultAvatar,
    nm: '',
    refresh: false,
    timestamp: 0,
  };

  // 添加状态管理
  const [celebrityData, setCelebrityData] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [alarmListData, setAlarmListData] = useState<AlarmItem[]>([]); // 添加闹钟列表数据状态
  const [alertVisible, setAlertVisible] = useState(false);
  // 通知权限对话框状态
  const [notificationModalVisible, setNotificationModalVisible] = useState(false);
  const [permissionCallback, setPermissionCallback] = useState<((result: boolean) => void) | null>(null);

  // 闹钟倒计时状态
  const [alarmCountdowns, setAlarmCountdowns] = useState<{[key: string]: string}>({}); // 用于存储闹钟倒计时

  // 点赞和订阅状态
  const [likeCount, setLikeCount] = useState(0);
  const [isLiked, setIsLiked] = useState(false);
  const [isSubscribed, setIsSubscribed] = useState(false);
  const [tierNum, setTierNum] = useState(0);
  const [callNum, setCallNum] = useState(0);
  const [country, setCountry] = useState('');
  const [state, setState] = useState('');
  const [subscriptionCount, setSubscriptionCount] = useState(0);
  const [generateQuantity, setGenerateQuantity] = useState(0);
  const [celebrityImages, setCelebrityImages] = useState<any[]>([]);

  // 定义获取数据的函数
  const fetchData = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);

      // 使用 AsyncStorage 获取用户信息
      const userInfoStr = await AsyncStorage.getItem('@aivgen:user_info');
      let account = '';

      if (userInfoStr) {
        const userInfo = JSON.parse(userInfoStr);
        account = userInfo.nm || '';
      }

      // 如果没有nm参数，则不调用接口
      if (!nm) {
        setIsLoading(false);
        return;
      }

      // 调用名人详情接口
      const detailResponse = await get('/voicePerson/detail/', {
        nm: nm,
        account: account,
      });

      console.log('Celebrity detail responseALARM:', JSON.stringify(detailResponse.data));

      if (detailResponse.data && detailResponse.data.code === '200' && detailResponse.data.data) {
        // 保存接口返回的数据
        setCelebrityData(detailResponse.data.data);

        // 更新点赞和订阅状态
        setIsLiked(detailResponse.data.data.collectStatus || false);
        setIsSubscribed(detailResponse.data.data.subscriptStatus || false);

        // 更新点赞数量
        if (detailResponse.data.data.collectionNumber !== undefined) {
          setLikeCount(detailResponse.data.data.collectionNumber);
        }

        // 更新其他字段
        setTierNum(detailResponse.data.data.tierNum || 0);
        setCallNum(detailResponse.data.data.callNum || 0);
        setCountry(detailResponse.data.data.country || '');
        setState(detailResponse.data.data.state || '');
        setSubscriptionCount(detailResponse.data.data.numberofsubscriptions || 0);
        setGenerateQuantity(detailResponse.data.data.generateQuantity || 0);
        setCelebrityImages(detailResponse.data.data.images || []);
      } else {
        setError(detailResponse.data.msg || t('alarm.failedToGetData'));
      }

      // 调用闹钟列表接口
      const alarmListResponse = await post('/morningSchedule/list', {
        curAccount: account,
        voicePersonNm: nm,
      });

      console.log(t('logs.alarmListResponse'), JSON.stringify(alarmListResponse.data));

      if (alarmListResponse.data && alarmListResponse.data.code === '200' && alarmListResponse.data.data) {
        // 处理闹钟列表数据
        const alarmList = alarmListResponse.data.data.map((item: any) => {
          // 直接使用 setWeek 字段
          const daysText = item.setWeek || '';

          // 处理时间显示
          const timeDisplay = item.setTime || '00:00';

          // 根据makeFlag和wavUrl确定状态
          let status: 'active' | 'countdown' | 'download' | 'reject' = 'active';

          // 获取makeFlag和wavUrl
          const makeFlag = item.makeFlag !== undefined ? item.makeFlag : 1; // 默认为1
          const wavUrl = item.wavUrl || '';

          // 根据makeFlag和wavUrl确定状态
          if (makeFlag === 0) {
            if (wavUrl) {
              status = 'download'; // 当makeFlag=0且wavUrl有值时也为download状态
            } else {
              status = 'countdown'; // After状态
            }
          } else if (makeFlag === 1) {
            if (wavUrl) {
              status = 'download'; // 当makeFlag=1且wavUrl有值时为download状态
            } else {
              status = 'active'; // 当makeFlag=1且wavUrl无值时为正常状态
            }
          } else if (makeFlag === 2) {
            status = 'reject'; // Reject状态
          }

          return {
            id: item.id || Math.random(),
            name: name, // 使用路由参数中的 name
            image: item.morningImage ? { uri: item.morningImage } : image, // 使用路由参数中的 image
            title: item.morningName || 'Alarm',
            time: timeDisplay,
            days: daysText,
            status: status,
            countdown: item.generationTime || '',
            timeType: item.timeType || 'PM',
            makeFlag: makeFlag,
            wavUrl: wavUrl,
            setTime: item.setTime,
            // 保存原始数据的所有字段
            code: item.code,
            nm: item.nm,
            createDate: item.createDate,
            flag: item.flag,
            reserve1: item.reserve1,
            reserve2: item.reserve2,
            voicePersonNm: item.voicePersonNm,
            setWeek: item.setWeek,
            morningImage: item.morningImage,
            morningName: item.morningName,
            curAccount: item.curAccount,
            weather: item.weather,
            weatherSwitch: item.weatherSwitch,
            dressSwitch: item.dressSwitch,
            vibrationSwitch: item.vibrationSwitch,
            vibration: item.vibration,
            content: item.content,
            generationTime: item.generationTime,
            repeatFlag: item.repeatFlag
          };
        });

        setAlarmListData(alarmList);
      }
    } catch (err) {
      console.error('Error fetching data:', err);
      setError(t('alarm.errorFetchingData'));
    } finally {
      setIsLoading(false);
    }
  }, [nm, name, image]);

  // 在组件挂载时调用接口
  useEffect(() => {
    fetchData();
  }, [nm, refresh, timestamp, fetchData]); // 添加 refresh 和 timestamp 到依赖数组，当它们变化时重新获取数据

  // 使用 useFocusEffect 在页面获取焦点时检查刷新标志
  useFocusEffect(
    useCallback(() => {
      // 检查刷新标志
      const checkRefreshFlag = async () => {
        try {
          const refreshFlag = await AsyncStorage.getItem('@aivgen:alarm_refresh');
          if (refreshFlag === 'true') {
            // 清除刷新标志
            await AsyncStorage.removeItem('@aivgen:alarm_refresh');
            // 刷新数据
            fetchData();
          }
        } catch (err) {
          console.error('Error checking refresh flag:', err);
        }
      };

      checkRefreshFlag();
    // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []) // 移除 fetchData 依赖项，避免无限循环
  );

  // 处理点赞
  const handleLike = async () => {
    try {
      // 使用 AsyncStorage 获取用户信息
      const userInfoStr = await AsyncStorage.getItem('@aivgen:user_info');
      let account = '';

      if (userInfoStr) {
        const userInfo = JSON.parse(userInfoStr);
        account = userInfo.nm || '';
      }

      // 调用点赞接口
      const response = await post('/favoriteInformation/collectAndCancel', {
        peopleId: celebrityData?.nm || nm,
        account: account,
      });

      console.log('Like response:', JSON.stringify(response.data));

      // 更新UI
      if (isLiked) {
        setLikeCount(likeCount - 1);
      } else {
        setLikeCount(likeCount + 1);
      }
      setIsLiked(!isLiked);
    } catch (err) {
      console.error('Error handling like:', err);
    }
  };

  // 处理订阅
  const handleSubscribe = () => {
    setIsSubscribed(!isSubscribed);
  };

  // 处理订阅按钮点击
  const handleSubscription = (subscriptionType: 'upgrade' | 'downgrade' | 'new') => {
    console.log(`Subscription button clicked: ${subscriptionType}`);
    // 导航到订阅层级列表页面，并传递类型参数和当前选中的celebrityData.nm
    navigation.navigate('TierList', {
      subscriptionType,
      celebrityNm: celebrityData?.nm || '',
    });
  };

  // 处理设置闹钟
  const handleSetAlarm = async (item: AlarmItem) => {
    try {
      // 请求通知权限
      const hasPermission = await notificationService.requestNotificationPermissions();

      if (!hasPermission) {
        Alert.alert(
          t('alarm.notificationPermissionRequired'),
          t('alarm.notificationPermissionMessage'),
          [
            { text: t('common.cancel'), style: 'cancel' },
            {
              text: t('settings.openSettings'),
              onPress: () => Linking.openSettings(),
            },
          ]
        );
        return;
      }

      // 显示加载状态
      setIsLoading(true);

      // 从闹钟项中获取必要的数据
      const {
        id,
        title,
        time,
        timeType,
        days,
        wavUrl,
      } = item;

      // 如果有音频URL，先下载音频文件
      let localAudioPath = wavUrl;
      if (wavUrl && wavUrl.startsWith('http')) {
        try {
          console.log('Downloading audio file for alarm:', id);
          localAudioPath = await downloadAudioFile(
            wavUrl,
            id,
            (progress) => {
              console.log(`Download progress: ${progress.toFixed(2)}%`);
            }
          );
          console.log('Audio file downloaded to:', localAudioPath);
        } catch (downloadError) {
          console.error('Failed to download audio file:', downloadError);
          showError('Failed to download audio file. Using default alarm sound.');
          // 继续使用原始URL或默认音频
          localAudioPath = wavUrl;
        }
      }

      // 获取原始数据中的字段
      const morningImage = item.morningImage || '';
      const weekDays = item.setWeek || days;
      const vibration = item.vibration !== undefined ? item.vibration : true;
      const repeatFlag = item.repeatFlag !== undefined ? item.repeatFlag : true;
      const setTime = item.setTime || time;

      // 解析时间
      const [hours, minutes] = setTime.split(':').map(part => part.trim());

      // 创建闹钟日期（设置为下一个符合条件的日期）
      const now = new Date();
      const alarmDate = new Date();

      // 设置小时和分钟
      let hour = parseInt(hours, 10);
      if (timeType === 'PM' && hour < 12) {
        hour += 12;
      } else if (timeType === 'AM' && hour === 12) {
        hour = 0;
      }

      alarmDate.setHours(hour);
      alarmDate.setMinutes(parseInt(minutes, 10));
      alarmDate.setSeconds(0);

      // 如果时间已经过去，设置为明天
      if (alarmDate.getTime() <= now.getTime()) {
        alarmDate.setDate(alarmDate.getDate() + 1);
      }

      // 获取时间戳
      const timestamp = alarmDate.getTime();

      // 准备通知数据
      const alarmData = {
        alarmId: id,
        title: title || 'Alarm',
        time: setTime,
        timeType,
        morningImage,
        wavUrl,
        days: weekDays,
        vibration: vibration ? 'true' : 'false', // 转换为字符串
        repeatFlag: repeatFlag ? 'true' : 'false', // 转换为字符串
        setWeek: weekDays,
        setTime,
        screen: 'AlarmTrigger' // 添加导航目标屏幕
      };

      // 设置闹钟
      const notificationId = await notificationService.scheduleAlarm(
        id,
        title || 'Alarm',
        `Your alarm for ${setTime} ${timeType} is ready`,
        timestamp,
        alarmData,
        wavUrl
      );

      // 显示成功消息
      showSuccess(t('alarm.alarmSaved'));

      console.log('Alarm set with ID:', notificationId);

    } catch (err) {
      console.error('Error setting alarm:', err);
      showError(t('alarm.failedToSetAlarm'));
    } finally {
      setIsLoading(false);
    }
  };

  // 处理通知权限请求
  const handleShowNotificationPermission = (callback: (result: boolean) => void) => {
    setPermissionCallback(() => callback);
    setNotificationModalVisible(true);
  };

  // 处理通知权限允许
  const handleAllowNotification = () => {
    setNotificationModalVisible(false);
    if (permissionCallback) {
      // 保存权限状态
      saveNotificationPermissionStatus(true);
      permissionCallback(true);
      setPermissionCallback(null);
    }
  };

  // 处理通知权限拒绝
  const handleDenyNotification = () => {
    setNotificationModalVisible(false);
    if (permissionCallback) {
      // 保存权限状态
      saveNotificationPermissionStatus(false);
      permissionCallback(false);
      setPermissionCallback(null);
    }
  };

  // 设置通知权限处理函数
  useEffect(() => {
    setNotificationPermissionModalHandler(handleShowNotificationPermission);

    return () => {
      // 清理函数
      setNotificationPermissionModalHandler(() => () => Promise.resolve(true));
    };
  }, []);

  // 解析时间字符串为小时、分钟、秒
  const parseTimeString = (timeString: string) => {
    if (!timeString) {
      return { hours: 0, minutes: 0, seconds: 0 };
    }

    const parts = timeString.split(':');
    if (parts.length !== 3) {
      return { hours: 0, minutes: 0, seconds: 0 };
    }

    return {
      hours: parseInt(parts[0], 10) || 0,
      minutes: parseInt(parts[1], 10) || 0,
      seconds: parseInt(parts[2], 10) || 0,
    };
  };

  // 格式化倒计时时间
  const formatCountdown = (hours: number, minutes: number, seconds: number) => {
    return `${String(hours).padStart(2, '0')}:${String(minutes).padStart(2, '0')}:${String(seconds).padStart(2, '0')}`;
  };

  // 闹钟倒计时效果
  useEffect(() => {
    // 初始化闹钟倒计时
    const initialAlarmCountdowns: {[key: string]: string} = {};

    alarmListData.forEach(item => {
      if (item.makeFlag === 0 && item.countdown) {
        const { hours, minutes, seconds } = parseTimeString(item.countdown);
        initialAlarmCountdowns[item.id.toString()] = formatCountdown(hours, minutes, seconds);
      }
    });

    setAlarmCountdowns(initialAlarmCountdowns);

    // 每秒更新闹钟倒计时
    const alarmTimer = setInterval(() => {
      setAlarmCountdowns(prevCountdowns => {
        const newCountdowns = { ...prevCountdowns };

        Object.keys(newCountdowns).forEach(itemId => {
          const timeString = newCountdowns[itemId];
          const { hours, minutes, seconds } = parseTimeString(timeString);

          // 计算新的倒计时时间
          let newSeconds = seconds - 1;
          let newMinutes = minutes;
          let newHours = hours;

          if (newSeconds < 0) {
            newSeconds = 59;
            newMinutes -= 1;
          }

          if (newMinutes < 0) {
            newMinutes = 59;
            newHours -= 1;
          }

          // 如果倒计时结束，则移除该项
          if (newHours < 0) {
            delete newCountdowns[itemId];
          } else {
            newCountdowns[itemId] = formatCountdown(newHours, newMinutes, newSeconds);
          }
        });

        return newCountdowns;
      });
    }, 1000);

    return () => clearInterval(alarmTimer);
  }, [alarmListData]);

  // 自定义右侧组件
  const headerRightComponent = (
    <View style={styles.headerRight}>
      <TouchableOpacity style={styles.searchButton}>
        <Image source={IMAGES.searchIcon} style={styles.icon} />
      </TouchableOpacity>
      <TouchableOpacity style={styles.menuButton}>
        <Image source={IMAGES.menuIcon} style={styles.icon} />
      </TouchableOpacity>
    </View>
  );

  // 渲染闹钟列表项
  const renderAlarmItem = ({ item }: { item: AlarmItem }) => {
    // 根据makeFlag和wavUrl确定状态
    let backgroundColor = '#6A4BFF'; // 默认背景色
    let overlay = null;
    let isClickable = false;

    // 基本内容，所有闹钟都会显示
    const baseContent = (
      <View style={styles.alarmContent}>
        <Text style={styles.alarmTitle}>{item.title}</Text>
        <View style={styles.timeContainer}>
          <Text style={styles.timeText}>
            <Text style={styles.pmText}>{item.timeType || 'PM'}</Text> {item.time}
          </Text>
        </View>
        <Text style={styles.daysText}>{item.days}</Text>
      </View>
    );

    // 根据makeFlag和wavUrl确定状态和UI
    const makeFlag = item.makeFlag !== undefined ? item.makeFlag : 1;
    const wavUrl = item.wavUrl || '';

    if (makeFlag === 0) {
      if (wavUrl) {
        // Download状态 (当makeFlag=0且wavUrl有值时)
        backgroundColor = '#333333';
        overlay = (
          <>
            <Text style={styles.statusText}>DOWNLOAD</Text>
            <View style={styles.downloadIconContainer}>
              <TouchableOpacity
                style={styles.downloadButton}
                onPress={() => handleSetAlarm(item)}
              >
                <Image
                  source={require('../assets/images/home/<USER>')}
                  style={styles.downloadIcon}
                />
              </TouchableOpacity>
            </View>
            {/* <Text style={styles.nameTexts}>{item.name}</Text> */}
          </>
        );
        isClickable = true; // 可点击
      } else {
        // After状态 (倒计时)
        backgroundColor = '#333333';
        overlay = (
          <>
            <Text style={styles.statusText}>AFTER</Text>
            <Text style={styles.countdownText}>{alarmCountdowns[item.id.toString()] || item.countdown || '00:00:00'}</Text>
            <Text style={styles.nameTexts}>{item.name}</Text>
          </>
        );
        isClickable = true; // 可点击
      }
    } else if (makeFlag === 1) {
      if (wavUrl) {
        // Download状态
        backgroundColor = '#333333';
        overlay = (
          <>
            <Text style={styles.statusText}>DOWNLOAD</Text>
            <View style={styles.downloadIconContainer}>
              <TouchableOpacity
                style={styles.downloadButton}
                onPress={() => handleSetAlarm(item)}
              >
                <Image
                  source={require('../assets/images/home/<USER>')}
                  style={styles.downloadIcon}
                />
              </TouchableOpacity>
            </View>
            {/* <Text style={styles.nameTexts}>{item.name}</Text> */}
          </>
        );
        isClickable = true; // 可点击
      } else {
        // 正常状态
        backgroundColor = '#6A4BFF';
        isClickable = true; // 可点击
      }
    } else if (makeFlag === 2) {
      // Reject状态
      backgroundColor = '#333333';
      overlay = (
        <>
          {/* <Text style={styles.statusText}>{item.title}</Text> */}
          <Text style={styles.rejectText}>REJECT</Text>
          {/* <Text style={styles.nameTexts}>{item.name}</Text> */}
        </>
      );
      isClickable = true; // 可点击
    }

    // 处理点击事件
    const handleItemPress = () => {
      navigation.navigate('EditVoiceAlarm', {
        alarmId: item.id,
        name: item.name,
        image: item.image,
        nm: celebrityData?.nm || '',
      });
    };

    if (makeFlag === 1 && !wavUrl) {
      // 正常状态，没有遮罩
      return (
        <TouchableOpacity
          style={[styles.alarmItem, { backgroundColor }]}
          onPress={handleItemPress}
        >
          <Image source={item.image} style={styles.alarmImage} />
          {baseContent}
        </TouchableOpacity>
      );
    } else {
      // 有遮罩的状态
      return (
        <TouchableOpacity
          style={[styles.alarmItem, { backgroundColor }]}
          onPress={isClickable ? handleItemPress : undefined}
          disabled={!isClickable}
        >
          <Image source={item.image} style={styles.alarmImage} />
          {baseContent}
          <View style={styles.fullOverlay}>
            {overlay}
          </View>
        </TouchableOpacity>
      );
    }
  };

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="transparent" translucent={true} />

      {/* 使用全局 Header 组件 */}
      <Header title={celebrityData?.name}  rightComponentType="searchAndMenu" />

      {alertVisible  && (
        <View style={styles.alertOverlay}>
          <View style={styles.alertContainer}>
            <Text style={styles.alertMessage}>
              Alarm limit has been reached.Upgrade your plan to continue.
            </Text>
            <Text style={styles.alertslot}>
            *Slots will refill upon renewal!
            </Text>
           
            <View style={styles.alertDivider} />
            <TouchableOpacity
              style={styles.alertButton}
              onPress={() => setAlertVisible(false)}
            >
              <Text style={styles.alertButtonText}>{t('common.ok')}</Text>
            </TouchableOpacity>
          </View>
        </View>
      )}


      {isLoading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#00FF47" />
        </View>
      ) : error ? (
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>{error}</Text>
        </View>
      ) : (
        <ScrollView style={styles.scrollView} contentContainerStyle={styles.scrollViewContent}>
          <View style={styles.content}>
            {/* 名人头像 */}
            <View style={styles.profileContainer}>
              <View style={styles.profileImageContainer}>
                <Image
                  source={celebrityData?.headImage ? { uri: celebrityData.headImage } : image}
                  style={styles.profileImage}
                  resizeMode="cover"
                />
              </View>
              <Text style={styles.nameText}>{celebrityData?.name || name}</Text>
            </View>

            {/* 点赞和订阅按钮 */}
            <View style={styles.actionButtonsContainer}>
              <TouchableOpacity
                style={styles.likeButton}
                onPress={handleLike}
              >
                <Image
                  source={IMAGES.heart}
                  style={[styles.likeIcon, isLiked && styles.likedIcon]}
                />
                <Text style={[styles.likeCount, isLiked && styles.likedText]}>{likeCount}</Text>
              </TouchableOpacity>

              <View
                style={styles.subscribeButton}
                // onPress={handleSubscribe}
              >
                <Image
                  source={IMAGES.heartFilled}
                  style={[styles.subscribeIcon, isSubscribed && styles.likedIcon]}
                />
                <Text style={[styles.subscribeText, isSubscribed && styles.likedText]}>
                Subscription
                </Text>
              </View>

              <View style={styles.alarmButton}>
                <Image source={require('../assets/images/home/<USER>')} style={styles.alarmIcon} />
                <Text style={styles.tierText}>{celebrityData?.tier || '_____' }</Text>
              </View>
            </View>

            {/* Upgrade 按钮 - 只有当 tierNum > 0 时才显示 */}
            {celebrityData?.tierNum > 0 && (
              <TouchableOpacity
                style={styles.upgradeButton}
                onPress={() => handleSubscription(celebrityData?.tier ? 'upgrade' : 'new')}
              >
                <Image
                  source={require('../assets/images/home/<USER>')}
                  style={styles.upgradeIcon}
                />
                <Text style={styles.upgradeText}>Upgrade</Text>
              </TouchableOpacity>
            )}

            {/* 警告信息 */}
            {!celebrityData?.subscriptStatus && (
              <View style={styles.warningContainer}>
                <Image source={require('../assets/images/warning-icon.png')} style={styles.warningIcon} />
                <Text style={styles.warningText}>
                  You'll need to purchase a subscription{'\n'}
                  to get a morning call{'\n'}
                  in {celebrityData?.name || name}'s voice.
                </Text>
              </View>
            )}

            {/* 闹钟列表部分 */}
            <View style={styles.containerAlarm}>
              {/* 如果有数据，显示标题和列表 */}
              {alarmListData.length > 0 ? (
                <>
                  {/* 闹钟列表标题 */}
                  <View style={styles.titleContainer}>
                    <Text style={styles.titleText}>CELEB VOICE ALARM LIST</Text>
                  </View>

                  {/* 闹钟列表 */}
                  <View style={styles.listContainer}>
                    {alarmListData.map(item => (
                      <React.Fragment key={item.id}>
                        {renderAlarmItem({ item })}
                      </React.Fragment>
                    ))}
                  </View>
                </>
              ) : null}
            </View>

            {/* Add 按钮和文本 */}
            {!isLoading && !error && celebrityData?.subscriptStatus && (
              <View style={styles.addContainerInline}>
                {/* 如果列表为空，显示 Add voice alarm 文本 */}
                {alarmListData.length === 0 && (
                  <Text style={styles.addText}>Add voice alarm</Text>
                )}

                {/* 无论列表是否有数据，都显示 Add 图标按钮 */} 
                <TouchableOpacity onPress={() => {
                  // 请求通知权限
                  console.log('CelebrityData',celebrityData.availableTime)
                  if(celebrityData.availableTime == 0) {
                    return setAlertVisible(true)
                  }
                  requestNotificationPermission().then(hasPermission => {
                    console.log('是否授权', hasPermission);
                    if (hasPermission) {
                      // 如果用户授予权限，导航到创建闹钟页面
                      navigation.navigate('CreateVoiceAlarm', { id: id || 0, name: name || '', image: image, nm: nm || '' });
                    } else {
                      // 如果用户拒绝权限，显示提示
                      Alert.alert(
                        'Notification Permission Required',
                        'To receive voice alarm notifications, please allow notifications in your device settings.',
                        [
                          { text: 'Cancel', style: 'cancel' },
                          {
                            text: 'Open Settings',
                            onPress: () => Linking.openSettings(),
                          },
                        ]
                      );
                    }
                  });
                }}>
                  <Image
                    source={require('../assets/images/home/<USER>')}
                    style={styles.addIcon}
                  />
                </TouchableOpacity>
              </View>
            )}

            {/* 底部空间，确保内容不被订阅按钮遮挡 */}
            <View style={styles.bottomSpace} />
          </View>
        </ScrollView>
      )}

      {/* 订阅按钮 - 保持在固定位置 */}
      {!isLoading && !error && !celebrityData?.subscriptStatus && (
        <View style={styles.subscriptionContainer}>
          <TouchableOpacity
            style={styles.subscriptionButton}
            onPress={() => handleSubscription('new')}
          >
            <LinearGradient
              colors={['rgba(173, 0, 255, 0.6)', 'rgba(2, 55, 189, 0.6)']}
              start={{x: 0, y: 0}}
              end={{x: 1, y: 0}}
              style={styles.gradientButton}
            >
              <Image source={IMAGES.heartFilled} style={styles.subscriptionIcon} />
              <Text style={styles.subscriptionText}>Subscription</Text>
            </LinearGradient>
          </TouchableOpacity>
        </View>
      )}

      {/* 通知权限请求对话框 */}
      <NotificationPermissionModal
        visible={notificationModalVisible}
        onAllow={handleAllowNotification}
        onDeny={handleDenyNotification}
      />

      {/* 底部导航栏 */}
      <View style={styles.bottomTabBar}>
        <TouchableOpacity
          style={styles.tabItem}
          onPress={() => navigation.navigate('MainTabs', { screen: 'Home' })}
        >
          <Image
            source={IMAGES.tabIcons.home}
            style={[styles.tabIcon, { tintColor: '#767676' }]}
          />
          <Text style={[styles.tabText, { color: '#767676' }]}>HOME</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.tabItem}
          onPress={() => navigation.navigate('MainTabs', { screen: 'Subscription' })}
        >
          <Image
            source={IMAGES.tabIcons.subscript}
            style={[styles.tabIcon, { tintColor: '#767676' }]}
          />
          <Text style={[styles.tabText, { color: '#767676' }]}>SUBSCRIPTION</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.tabItem}
          onPress={() => navigation.navigate('MainTabs', { screen: 'Inbox' })}
        >
          <Image
            source={IMAGES.tabIcons.inbox}
            style={[styles.tabIcon, { tintColor: '#767676' }]}
          />
          <Text style={[styles.tabText, { color: '#767676' }]}>INBOX</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.tabItem}
          onPress={() => navigation.navigate('MainTabs', { screen: 'Profile' })}
        >
          <Image
            source={IMAGES.tabIcons.my}
            style={[styles.tabIcon, { tintColor: '#767676' }]}
          />
          <Text style={[styles.tabText, { color: '#767676' }]}>MY</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000000',
  },
  scrollView: {
    flex: 1,
    backgroundColor: '#000000',
  },
  scrollViewContent: {
    paddingBottom: 0, // 移除底部空间，因为已经在 addContainerInline 中添加了足够的边距
  },
  bottomSpace: {
    height: 0, // 移除底部空间，因为已经在 addContainerInline 中添加了足够的边距
  },
  headerRight: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  searchButton: {
    marginRight: 15,
  },
  menuButton: {
    marginRight: 5,
  },
  icon: {
    width: 24,
    height: 24,
    tintColor: '#FFFFFF',
  },
  content: {
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  profileContainer: {
    alignItems: 'center',
    marginTop: 20,
  },
  profileImageContainer: {
    width: 150,
    height: 150,
    borderRadius: 75,
    borderWidth: 2,
    borderColor: '#00C853',
    borderTopColor: '#00C853',
    borderRightColor: '#2980B9',
    borderBottomColor: '#8E44AD',
    borderLeftColor: '#2980B9',
    overflow: 'hidden',
    marginBottom: 10,
  },
  profileImage: {
    width: '100%',
    height: '100%',
  },
  nameText: {
    color: '#FFFFFF',
    fontFamily: FONTS.bebasNeue.regular,
    fontSize: 24,
    letterSpacing: 1,
    marginTop: 10,
    marginBottom: 20,
  },
  actionButtonsContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    width: '100%',
  },
  likeButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#6A0DAD',
    borderRadius: 20,
    paddingVertical: 2,
    marginRight: 10,
    width: 80,
  },
  likeIcon: {
    width: 15,
    height: 15,
    tintColor: '#FFFFFF',
    marginRight: 5,
    resizeMode: 'contain'
  },
  likedIcon: {
    tintColor: '#FF0000',
  },
  likeCount: {
    color: '#FFFFFF',
    fontFamily: FONTS.pretendard.light,
    fontSize: 16,
  },
  likedText: {
    color: '#FF0000',
  },
  subscribeButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#8E44AD',
    borderRadius: 20,
    paddingVertical: 2,
    paddingHorizontal: 15,
    marginRight: 10,
  },
  subscribeIcon: {
    width: 15,
    height: 15,
    tintColor: '#FFFFFF',
    marginRight: 5,
     resizeMode: 'contain'
  },
  subscribeText: {
    color: '#FFFFFF',
    fontFamily: FONTS.pretendard.light,
    fontSize: 16,
  },
  alarmButton: {
    backgroundColor: '#091F75',
    paddingHorizontal: 15,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 20,
    paddingVertical: 2,
    marginRight: 10,
    width: 80,
  },
  alarmIcon: {
    width: 10,
    height: 20,
    tintColor: '#FFFFFF',
    marginRight: 5,
    resizeMode: 'contain',
  },
  tierText: {
    color: '#FFFFFF',
    fontFamily: FONTS.pretendard.light,
    fontSize: 14,
    marginBottom: 2,

  },
  upgradeButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#7246BB',
    borderRadius: 30,
    paddingVertical: 8,
    paddingHorizontal: 30,
    marginTop: 15,
  },
  upgradeIcon: {
    width: 20,
    height: 20,
    tintColor: '#FFFFFF',
    marginRight: 10,
  },
  upgradeText: {
    color: '#FFFFFF',
    fontFamily: FONTS.pretendard.light,
    fontSize: 14,
  },
  warningContainer: {
    alignItems: 'center',
    marginVertical: 20,
  },
  warningIcon: {
    width: 40,
    height: 40,
    resizeMode: 'contain',
    marginBottom: 20,
  },
  warningText: {
    color: '#FFFFFF',
    fontFamily: FONTS.pretendard.light,
    fontSize: 16,
    textAlign: 'center',
    lineHeight: 24,
  },
  subscriptionContainer: {
    position: 'absolute',
    bottom: 70, // 减少底部边距
    alignItems: 'center',
    width: '100%', // 添加宽度100%
    justifyContent: 'center', // 添加水平居中
    left: 0, // 确保从左边开始
    right: 0, // 确保延伸到右边
  },
  subscriptionButton: {
    width: 200,
    height: 40,
    marginBottom: 10,
    borderRadius: 30,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  addContainer: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  addContainerInline: {
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 20,
    marginBottom: 70, // 增加底部边距，确保不被 tabbar 挡住
    width: '100%',
  },
  addText: {
    color: '#FFFFFF',
    fontFamily: FONTS.bebasNeue.regular,
    fontSize: 18,
    letterSpacing: 1,
    marginBottom: 10,
    textAlign: 'center',
  },
  addIcon: {
    width: 30,
    height: 30,
    tintColor: '#FFFFFF',
  },
  gradientButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    width: '100%',
    height: '100%',
  },
  subscriptionIcon: {
    width: 20,
    height: 20,
    tintColor: '#FFFFFF',
    marginRight: 10,
  },
  subscriptionText: {
    color: '#FFFFFF',
    fontFamily: FONTS.bebasNeue.regular,
    fontSize: 18,
    letterSpacing: 1,
  },
  bottomTabBar: {
    flexDirection: 'row',
    backgroundColor: '#000000',
    borderTopWidth: 0.01,
    borderTopColor: '#333333',
    height: 60,
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
  },
  tabItem: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  tabIcon: {
    width: 20,
    height: 20,
    marginBottom: 5,
  },
  tabText: {
    fontFamily: 'Pretendard-Light',
    fontSize: 12,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorContainer: {
    padding: 20,
    backgroundColor: 'rgba(255, 0, 0, 0.1)',
    borderRadius: 10,
    margin: 20,
  },
  errorText: {
    color: '#FF0000',
    fontFamily: FONTS.pretendard.light,
    fontSize: 16,
    textAlign: 'center',
  },
  containerAlarm: {
    width: '100%',
  },
  // 闹钟列表样式
  titleContainer: {
    paddingVertical: 15,
    paddingHorizontal: 20,
    alignItems: 'center',
    marginTop: 20,
  },
  titleText: {
    color: '#D9C091',
    fontSize: 20,
    fontFamily: FONTS.bebasNeue.regular,
    letterSpacing: 1,
    textAlign: 'center',
  },
  listContainer: {
    width: '100%',
  },
  alarmItem: {
    flexDirection: 'row',
    borderRadius: 15,
    marginBottom: 15,
    overflow: 'hidden',
  },
  alarmImage: {
    width: 80,
    height: 80,
    borderRadius: 40,
    margin: 10,
  },
  alarmContent: {
    flex: 1,
    padding: 15,
    justifyContent: 'center',
  },
  alarmTitle: {
    color: '#FFFFFF',
    fontSize: 20,
    fontFamily: FONTS.bebasNeue.regular,
  },
  timeContainer: {
    flexDirection: 'row',
    alignItems: 'baseline',
    marginVertical: 5,
  },
  pmText: {
    color: '#00FF66',
    fontSize: 20,
    fontFamily: FONTS.bebasNeue.regular,
  },
  timeText: {
    color: '#FFFFFF',
    fontSize: 24,
    fontFamily: FONTS.bebasNeue.regular,
  },
  daysText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontFamily: FONTS.pretendard.light,
  },
  statusText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontFamily: FONTS.pretendard.light,
    fontWeight: 'bold',
  },
  countdownText: {
    color: '#FF4D4D',
    fontSize: 24,
    fontFamily: FONTS.pretendard.light,
    fontWeight: 'bold',
    marginVertical: 5,
  },
  nameTexts:{
    color: '#AAAAAA',
    fontSize: 14,
    fontFamily: FONTS.pretendard.light,
  },
  downloadIconContainer: {
    marginVertical: 5,
  },
  downloadButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#4285F4',
    alignItems: 'center',
    justifyContent: 'center',
  },
  downloadIcon: {
    width: 40,
    height: 40,
  },
  rejectText: {
    color: '#FF4D4D',
    fontSize: 24,
    fontFamily: FONTS.pretendard.light,
    fontWeight: 'bold',
    marginVertical: 5,
  },
  fullOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 10,
  },

// 验证弹窗样式
alertOverlay: {
  position: 'absolute',
  top: 0,
  left: 0,
  right: 0,
  bottom: 0,
  backgroundColor: 'rgba(0, 0, 0, 0.7)',
  justifyContent: 'center',
  alignItems: 'center',
  zIndex: 1000,
},
alertContainer: {
  width: width * 0.85,
  backgroundColor: '#222222',
  borderRadius: 10,
  overflow: 'hidden',
},
alertMessage: {
  color: '#fff',
  fontSize: 16,
  fontFamily: FONTS.pretendard.light,
  textAlign: 'center',
  paddingHorizontal: 20,
  paddingTop: 30,
},
alertslot: {
  color: '#fff',
  fontSize: 14,
  fontFamily: FONTS.pretendard.light,
  textAlign: 'center',
  paddingBottom: 30,
  marginTop: 5,
},
alertDivider: {
  height: 1,
  backgroundColor: '#444444',
  marginHorizontal: 0,
},
alertButton: {
  backgroundColor: '#007AFF',
  paddingVertical: 15,
  alignItems: 'center',
  margin: 15,
  borderRadius: 10,
},
alertButtonText: {
  color: '#FFFFFF',
  fontSize: 16,
  fontFamily: FONTS.pretendard.light,
  fontWeight: '500',
},


});

export default AlarmScreen;
