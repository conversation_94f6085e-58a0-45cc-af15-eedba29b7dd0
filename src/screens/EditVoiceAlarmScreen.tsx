import React, { useState, useRef, useEffect, useMemo } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Image,
  TouchableOpacity,
  TextInput,
  ScrollView,
  StatusBar,
  Dimensions,
  ActivityIndicator,
} from 'react-native';
import { FONTS } from '../assets/fonts';
import { Header, CustomSwitch } from '../components';
import { useNavigation, useRoute, RouteProp } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { get, post, del } from '../services/api/apiService';
import AsyncStorage from '@react-native-async-storage/async-storage';
import Carousel from 'react-native-reanimated-carousel';
import { showSuccess, showError } from '../services/toast';
import { useTranslation } from 'react-i18next';

// 定义导航参数类型
type RootStackParamList = {
  Home: undefined;
  Alarm: {
    id?: number;
    name?: string;
    type?: string;
    image?: any;
    refresh?: boolean;
    timestamp?: number;
    nm?: string; // 添加nm参数
  };
  CreateVoiceAlarm: {
    id: number;
    name: string;
    image: any;
    nm?: string; // 添加nm参数
  };
  EditVoiceAlarm: {
    alarmId: string; // 闹钟ID
    name: string;
    image: any;
    nm?: string;
  };
  VoiceMessageConfirm: {
    id?: string;
    source?: 'voiceMessage' | 'alarm'; // 添加来源页面标识
  };
};

type EditVoiceAlarmRouteProp = RouteProp<
  { EditVoiceAlarm: { alarmId: string; name: string; image: any; nm?: string } },
  'EditVoiceAlarm'
>;

type EditVoiceAlarmScreenNavigationProp = StackNavigationProp<RootStackParamList, 'EditVoiceAlarm'>;

const { width } = Dimensions.get('window');

const EditVoiceAlarmScreen = () => {
  const navigation = useNavigation<EditVoiceAlarmScreenNavigationProp>();
  const route = useRoute<EditVoiceAlarmRouteProp>();
  const { t } = useTranslation();

  // 从路由参数中获取名人信息和闹钟ID
  const { alarmId, name, image, nm } = route.params || {
    alarmId: '',
    name: 'ARINDA GRANDE',
    image: require('../assets/images/home/<USER>'),
    nm: '',
  };
  console.log('nmnmnm',nm)
  // 添加状态管理
  const [celebrityData, setCelebrityData] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // 获取当前时间
  const getCurrentTime = () => {
    const now = new Date();
    const hours = now.getHours();
    const minutes = now.getMinutes();

    // 确定是上午还是下午
    const period = hours >= 12 ? 'PM' : 'AM';

    // 转换为12小时制
    const hour12 = hours % 12 || 12;
    const hourStr = hour12 < 10 ? `0${hour12}` : `${hour12}`;
    const minuteStr = minutes < 10 ? `0${minutes}` : `${minutes}`;

    return { period, hour: hourStr, minute: minuteStr };
  };

  // 获取当前时间
  const currentTime = getCurrentTime();

  // 状态
  const [title, setTitle] = useState('');
  const [selectedPeriod, setSelectedPeriod] = useState(currentTime.period);
  const [selectedHour, setSelectedHour] = useState(currentTime.hour);
  const [selectedMinute, setSelectedMinute] = useState(currentTime.minute);
  const [selectedDays, setSelectedDays] = useState({
    sun: true,
    mon: false,
    tue: false,
    wed: false,
    thu: false,
    fri: false,
    sat: true,
  });
  const [message, setMessage] = useState('');
  const [currentBackground, setCurrentBackground] = useState(0);
  const [morningImage, setMorningImage] = useState('');
  const [vibrationEnabled, setVibrationEnabled] = useState(true);
  const [replayEnabled, setReplayEnabled] = useState(true);
  const [showConfirmModal, setShowConfirmModal] = useState(false);
  const [selectedBackgroundIndex, setSelectedBackgroundIndex] = useState<number | null>(null);
  const [alertVisible, setAlertVisible] = useState(false);
  const [alertMessage, setAlertMessage] = useState('');
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [shouldUpdateBackground, setShouldUpdateBackground] = useState(false);

  // 在组件挂载时调用接口获取名人详情和闹钟详情
  useEffect(() => {
    const fetchData = async () => {
      try {
        setIsLoading(true);
        setError(null);

        // 使用 AsyncStorage 获取用户信息
        const userInfoStr = await AsyncStorage.getItem('@aivgen:user_info');
        let account = '';

        if (userInfoStr) {
          const userInfo = JSON.parse(userInfoStr);
          account = userInfo.nm || '';
        }

        // 获取名人详情
        if (nm) {
          const celebrityResponse = await get('/voicePerson/detail', {
            nm: nm,
            account: account,
          });

          console.log('Celebrity detail response:', JSON.stringify(celebrityResponse.data));

          if (celebrityResponse.data && celebrityResponse.data.code === '200' && celebrityResponse.data.data) {
            // 保存接口返回的数据
            setCelebrityData(celebrityResponse.data.data);
          } else {
            console.error('Failed to fetch celebrity details:', celebrityResponse.data);
          }
        }

        // 获取闹钟详情
        if (alarmId) {
          const alarmResponse = await get(`/morningSchedule/${alarmId}`);
          console.log('Alarm detail response:', JSON.stringify(alarmResponse.data));

          if (alarmResponse.data && alarmResponse.data.code === '200' && alarmResponse.data.data) {
            const alarmData = alarmResponse.data.data;

            // 设置闹钟数据
            setTitle(alarmData.morningName || '');
            setMessage(alarmData.content || '');

            // 设置时间
            if (alarmData.setTime) {
              const timeParts = alarmData.setTime.split(':');
              if (timeParts.length === 2) {
                setSelectedHour(timeParts[0]);
                setSelectedMinute(timeParts[1]);
              }
            }

            // 设置时间类型（AM/PM）
            if (alarmData.timeType) {
              setSelectedPeriod(alarmData.timeType);
            }

            // 设置星期
            if (alarmData.setWeek) {
              const weekDays = alarmData.setWeek.split(',').map(Number);
              const newSelectedDays = {
                sun: weekDays.includes(1),
                mon: weekDays.includes(2),
                tue: weekDays.includes(3),
                wed: weekDays.includes(4),
                thu: weekDays.includes(5),
                fri: weekDays.includes(6),
                sat: weekDays.includes(7),
              };
              setSelectedDays(newSelectedDays);
            }

            // 设置选项
            setVibrationEnabled(alarmData.vibrationSwitch || false);
            setReplayEnabled(alarmData.repeatFlag || false);

            // 保存闹钟的背景图片URL，稍后在backgrounds准备好后设置
            if (alarmData.morningImage) {
              // 将morningImage保存到state中，以便在backgrounds准备好后使用
              setMorningImage(alarmData.morningImage);
            }
          } else {
            setError(alarmResponse.data?.msg || t('alarm.failedToFetchAlarmData'));
          }
        }
      } catch (err) {
        console.error('Error fetching data:', err);
        setError(t('alarm.errorFetchingData'));
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [nm, alarmId]);

  // 时间选择器数据
  // 使用 useMemo 创建小时数据，避免重复创建
  const hourItems = useMemo(() => {
    return ['01', '02', '03', '04', '05', '06', '07', '08', '09', '10', '11', '12'];
  }, []);

  // 使用 useMemo 创建分钟数据，避免重复创建
  const minuteItems = useMemo(() => {
    const items: string[] = [];
    for (let i = 0; i < 60; i++) {
      items.push(i < 10 ? `0${i}` : `${i}`);
    }
    return items;
  }, []);

  // 滚动选择器引用
  const amPmScrollViewRef = useRef<ScrollView>(null);
  const hourScrollViewRef = useRef<ScrollView>(null);
  const minuteScrollViewRef = useRef<ScrollView>(null);

  // 滚动状态标志
  const [, setIsScrolling] = useState(false); // 只使用 setter
  const [initialScrollDone, setInitialScrollDone] = useState(false);

  // 背景图片 - 使用接口返回的 images 数组或默认图片
  const defaultBackgrounds = useMemo(() => [
    require('../assets/images/home/<USER>'),
    require('../assets/images/home/<USER>'),
    require('../assets/images/home/<USER>'),
  ], []);

  // 使用 celebrityImages 或默认背景
  const backgrounds = useMemo(() => {
    // 从 celebrityData 中获取图片数组，如果有的话
    const celebrityImages = celebrityData?.images || [];

    return celebrityImages.length > 0
      ? celebrityImages.map((img: { resourceAddress: string }) => ({ uri: img.resourceAddress }))
      : defaultBackgrounds;
  }, [celebrityData, defaultBackgrounds]);

  // 当 morningImage 或 backgrounds 变化时，设置背景图片
  useEffect(() => {
    if (morningImage && backgrounds.length > 0) {
      console.log('Setting background based on morningImage:', morningImage);
      console.log('Available backgrounds:', JSON.stringify(backgrounds));

      // 查找匹配的背景图片索引
      const backgroundIndex = backgrounds.findIndex((bg: { uri: string }) => {
        // 检查 bg.uri 是否与 morningImage 完全匹配
        const exactMatch = bg.uri === morningImage;

        // 检查 bg.uri 是否包含 morningImage 的文件名部分
        const morningImageFilename = morningImage.split('/').pop();
        const uriContainsFilename = bg.uri && morningImageFilename && bg.uri.includes(morningImageFilename);

        return exactMatch || uriContainsFilename;
      });

      console.log('Found background index:', backgroundIndex);

      if (backgroundIndex !== -1) {
        setCurrentBackground(backgroundIndex);
        if (carouselRef.current) {
          carouselRef.current.scrollTo({ index: backgroundIndex, animated: false });
        }
      } else {
        // 如果找不到匹配的背景图片，设置一个默认的背景图片
        console.log('No matching background found, using default');
        setCurrentBackground(0);
      }
    }
  }, [morningImage, backgrounds]);

  // 设置时间选择器的初始滚动位置
  useEffect(() => {
    // 设置 AM/PM 选择器的初始位置
    setTimeout(() => {
      const amPmIndex = selectedPeriod === 'AM' ? 0 : 1;
      amPmScrollViewRef.current?.scrollTo({ y: amPmIndex * 40, animated: false });

      // 设置小时选择器的初始位置
      const hourIndex = hourItems.indexOf(selectedHour);
      if (hourIndex !== -1) {
        hourScrollViewRef.current?.scrollTo({ y: hourIndex * 40, animated: false });
      }

      // 设置分钟选择器的初始位置
      const minuteIndex = minuteItems.indexOf(selectedMinute);
      if (minuteIndex !== -1) {
        minuteScrollViewRef.current?.scrollTo({ y: minuteIndex * 40, animated: false });
      }

      // 设置初始滚动完成标志
      setInitialScrollDone(true);
    }, 300);
  }, [selectedPeriod, selectedHour, selectedMinute, hourItems, minuteItems]);

  // 处理星期选择
  const handleDaySelect = (day: keyof typeof selectedDays) => {
    setSelectedDays({
      ...selectedDays,
      [day]: !selectedDays[day],
    });
  };

  // 移除未使用的处理函数

  // 滚动处理函数已移除，因为我们不再使用 FlatList

  // 轮播图引用
  const carouselRef = useRef<any>(null);

  // 用于双击检测
  const lastTapRef = useRef(0);

  // 处理双击背景图片
  const handleDoubleClickBackground = (index: number) => {
    console.log('Double click background image at index:', index);
    setSelectedBackgroundIndex(index);
    setShowConfirmModal(true);
  };

  // 处理确认应用背景
  const handleApplyBackground = () => {
    console.log('Apply background at index:', selectedBackgroundIndex);
    if (selectedBackgroundIndex !== null) {
      setCurrentBackground(selectedBackgroundIndex);
      // 设置标志，表示应该更新背景图片
      setShouldUpdateBackground(true);
    }
    setShowConfirmModal(false);
  };

  // 处理取消应用背景
  const handleCancelApplyBackground = () => {
    console.log('Cancel apply background');
    setSelectedBackgroundIndex(null);
    setShowConfirmModal(false);
  };

  // 处理背景图片切换
  const handleBackgroundChange = (direction: 'prev' | 'next') => {
    // 重置背景更新标志，确保箭头切换不会更新背景图片
    setShouldUpdateBackground(false);

    if (direction === 'prev') {
      const newIndex = currentBackground === 0 ? backgrounds.length - 1 : currentBackground - 1;
      setCurrentBackground(newIndex);
      if (carouselRef.current) {
        carouselRef.current.scrollTo({ index: newIndex, animated: true });
      }
    } else {
      const newIndex = currentBackground === backgrounds.length - 1 ? 0 : currentBackground + 1;
      setCurrentBackground(newIndex);
      if (carouselRef.current) {
        carouselRef.current.scrollTo({ index: newIndex, animated: true });
      }
    }
  };

  // 不再需要单独的渲染函数，直接在轮播组件中使用内联渲染

  // 显示删除确认弹窗
  const handleDeleteAlarm = () => {
    setShowDeleteConfirm(true);
  };

  // 确认删除闹钟
  const confirmDeleteAlarm = async () => {
    try {
      setIsLoading(true); // 显示半透明遮罩 loading
      setShowDeleteConfirm(false);

      // 调用删除接口，使用 DELETE 方法
      const response = await del(`/morningSchedule/${alarmId}`);
      console.log('Delete alarm response:', response.data);

      if (response.data && response.data.code === '200') {
        showSuccess(t('alarm.alarmDeletedSuccessfully'));

        // 延迟返回上一级页面
        setTimeout(() => {
          // 使用 AsyncStorage 存储刷新标志
          AsyncStorage.setItem('@aivgen:alarm_refresh', 'true');
          // 返回上一级页面
          navigation.goBack();
        }, 1000);
      } else {
        showError(response.data?.msg || t('alarm.failedToDeleteAlarm'));
      }
    } catch (err) {
      console.error('Error deleting alarm:', err);
      showError(t('alarm.errorDeletingAlarm'));
    } finally {
      setIsLoading(false); // 隐藏 loading
    }
  };

  // 取消删除闹钟
  const cancelDeleteAlarm = () => {
    setShowDeleteConfirm(false);
  };

  // 处理编辑闹钟
  const handleEditAlarm = async () => {
    // 验证表单
    if (!validateForm()) {
      return;
    }

    try {
      setIsLoading(true); // 显示半透明遮罩 loading

      // 使用 nm 参数，优先使用 celebrityData 中的 nm
      const voicePersonNm = celebrityData?.nm || nm || '';

      // 获取当前选中的背景图片
      const selectedBackground = backgrounds[currentBackground];
      // 如果用户双击了背景图片并确认应用，则使用新的背景图片
      // 否则使用原来的背景图片
      const backgroundImageUrl = shouldUpdateBackground ? (selectedBackground.uri || '') : morningImage;

      // 获取用户信息
      const userInfoStr = await AsyncStorage.getItem('@aivgen:user_info');
      let curAccount = '';
      if (userInfoStr) {
        const userInfo = JSON.parse(userInfoStr);
        curAccount = userInfo.nm || '';
      }

      // 构建星期字符串，例如 "1,2,3,4,5,6,7"
      const weekDays = [];
      if (selectedDays.sun) { weekDays.push(1); }
      if (selectedDays.mon) { weekDays.push(2); }
      if (selectedDays.tue) { weekDays.push(3); }
      if (selectedDays.wed) { weekDays.push(4); }
      if (selectedDays.thu) { weekDays.push(5); }
      if (selectedDays.fri) { weekDays.push(6); }
      if (selectedDays.sat) { weekDays.push(7); }
      const setWeek = weekDays.join(',');

      // 构建时间字符串，例如 "08:30"
      const setTime = `${selectedHour}:${selectedMinute}`;

      // 构建请求参数
      const params = {
        id: alarmId, // 闹钟ID，使用id而不是nm
        curAccount: curAccount,
        dressSwitch: false, // 默认值
        morningName: title,
        repeatPlayMiute: '', // 默认值
        repeatPlayTimes: '', // 默认值
        setTime: setTime,
        setWeek: setWeek,
        timeType: selectedPeriod, // AM 或 PM
        vibrationSwitch: vibrationEnabled,
        repeatFlag: replayEnabled,
        vibration: vibrationEnabled,
        voicePersonNm: voicePersonNm,
        // wavUrl: 'http://74.227.128.36:8005/0a22ea786c524a7d8bb8e240329cc650.wav' , // 默认值'https://www.kvcm.io/file/defaultMp3.mp3'
        weather: '', // 默认值
        weatherSwitch: false, // 默认值
        content: message,
        morningImage: backgroundImageUrl, // 添加背景图片
      };

      console.log('Updating alarm with params:', JSON.stringify(params));

      // 调用接口
      const response = await post('/morningSchedule/save', params);
      console.log('Update alarm response:', response.data);

      // 处理响应
      if (response.data && response.data.code === '200') {
        // 更新成功
        showSuccess(t('alarm.alarmUpdatedSuccessfully'));

        // 延迟返回上一级页面
        setTimeout(() => {
          // 使用 AsyncStorage 存储刷新标志
          AsyncStorage.setItem('@aivgen:alarm_refresh', 'true');
          // 返回上一级页面
          navigation.goBack();
        }, 1000);
      } else {
        // 更新失败
        showError(response.data?.msg || t('alarm.failedToUpdateAlarm'));
      }
    } catch (err) {
      console.error('Error updating alarm:', err);
      showError(t('alarm.errorUpdatingAlarm'));
    } finally {
      setIsLoading(false); // 隐藏 loading
    }
  };

  // 验证表单函数
  const validateForm = () => {
    // 验证标题
    if (!title.trim()) {
      setAlertMessage(t('alarm.enterTitleAtLeastOneLetter'));
      setAlertVisible(true);
      return false;
    }

    // 验证消息
    if (!message.trim()) {
      setAlertMessage(t('alarm.enterMessageAtLeastOneLetter'));
      setAlertVisible(true);
      return false;
    }

    // 验证是否选择了星期
    const hasSelectedDay = Object.values(selectedDays).some(value => value);
    if (!hasSelectedDay) {
      setAlertMessage(t('alarm.selectAtLeastOneDay'));
      setAlertVisible(true);
      return false;
    }

    return true;
  };

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="transparent" translucent={true} />

      {/* 使用全局 Header 组件 */}
      <Header title={t('alarm.editVoiceAlarm')} />

      {/* 半透明遮罩 Loading */}
      {isLoading && (
        <View style={styles.loadingOverlay}>
          <ActivityIndicator size="large" color="#00FF47" />
        </View>
      )}

      {/* 验证提示弹窗 */}
      {alertVisible && (
        <View style={styles.alertOverlay}>
          <View style={styles.alertContainer}>
            <Text style={styles.alertMessage}>{alertMessage}</Text>
            <View style={styles.alertDivider} />
            <TouchableOpacity
              style={styles.alertButton}
              onPress={() => setAlertVisible(false)}
            >
              <Text style={styles.alertButtonText}>{t('common.ok')}</Text>
            </TouchableOpacity>
          </View>
        </View>
      )}

      {/* 背景确认弹窗 */}
      {showConfirmModal && (
        <View style={styles.modalOverlay}>
          <View style={styles.modalContainer}>
            <View style={styles.modalHandle} />

            <Text style={styles.modalTitle}>{t('alarm.apply')}</Text>

            <View style={styles.modalDivider} />

            <Text style={styles.modalText}>
              {t('alarm.backgroundConfirmMessage')}
            </Text>

            <View style={styles.modalButtonsContainer}>
              <TouchableOpacity
                style={[styles.modalButton, styles.modalButtonNo]}
                onPress={handleCancelApplyBackground}
              >
                <Text style={styles.modalButtonText}>{t('common.no')}</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[styles.modalButton, styles.modalButtonYes]}
                onPress={handleApplyBackground}
              >
                <Text style={styles.modalButtonText}>{t('common.yes')}</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      )}

      {/* 删除确认弹窗 */}
      {showDeleteConfirm && (
        <View style={styles.modalOverlay}>
          <View style={styles.modalContainer}>
            <View style={styles.modalHandle} />

            <Text style={styles.deleteModalTitle}>{t('common.delete')}</Text>

            <View style={styles.modalDivider} />

            <Text style={styles.modalText}>
              {t('alarm.deleteConfirmationMessage')}
            </Text>

            <View style={styles.modalButtonsContainer}>
              <TouchableOpacity
                style={[styles.modalButton, styles.deleteModalButtonNo]}
                onPress={cancelDeleteAlarm}
              >
                <Text style={styles.modalButtonText}>{t('common.no')}</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[styles.modalButton, styles.deleteModalButtonYes]}
                onPress={confirmDeleteAlarm}
              >
                <Text style={styles.modalButtonText}>{t('common.yes')}</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      )}

      {error ? (
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>{error}</Text>
        </View>
      ) : (
        <ScrollView
          style={styles.scrollView}
          nestedScrollEnabled={true}
          scrollEventThrottle={16}
        >
          <View style={styles.content}>
            {/* 名人头像和名称 */}
            <View style={styles.celebrityContainer}>
              <Image
                source={celebrityData?.headImage ? { uri: celebrityData.headImage } : image}
                style={styles.celebrityImage}
                resizeMode="cover"
              />
              <Text style={styles.celebrityName}>{celebrityData?.name || name}</Text>
            </View>

          {/* 标题输入 */}
          <View style={styles.inputContainer}>
            <Text style={styles.inputLabel}>{t('alarm.title')}</Text>
            <TextInput
              style={styles.titleInput}
              value={title}
              onChangeText={setTitle}
              placeholder={t('alarm.enterTitle')}
              placeholderTextColor="#666666"
              maxLength={20}
            />
            <Text style={styles.charCount}>{`(${title.length} / 20)`}</Text>
          </View>

          {/* 时间选择 */}
          <View style={styles.sectionContainer}>
            <View style={styles.sectionTitleContainer}>
              <Image
                source={require('../assets/images/home/<USER>')}
                style={styles.sectionIcon}
              />
              <Text style={styles.sectionTitle}>{t('alarm.time')}</Text>
            </View>



            {/* 时间选择器 - 滚动版 */}
            <View
              style={styles.timePickerContainer}
              onStartShouldSetResponder={() => true}
              onResponderGrant={() => {}}
            >
              {/* 选择器高亮区域 */}
              <View style={styles.timePickerHighlight} />

              {/* AM/PM 选择器 */}
              <View style={styles.timeColumn}>
                <ScrollView
                  ref={amPmScrollViewRef}
                  showsVerticalScrollIndicator={false}
                  contentContainerStyle={styles.timeScrollContent}
                  snapToInterval={40}
                  decelerationRate="fast"
                  nestedScrollEnabled={true}
                  scrollEnabled={true}
                  directionalLockEnabled={true}
                  disableScrollViewPanResponder={false}
                  onStartShouldSetResponder={() => true}
                  onStartShouldSetResponderCapture={() => true}
                  onMoveShouldSetResponder={() => true}
                  onMoveShouldSetResponderCapture={() => true}
                  onScroll={() => {}}
                  scrollEventThrottle={160}
                  onMomentumScrollEnd={(event) => {
                    if (!initialScrollDone) {
                      return;
                    }

                    const y = event.nativeEvent.contentOffset.y;
                    const index = Math.round(y / 40);
                    const period = index === 0 ? 'AM' : 'PM';

                    if (period !== selectedPeriod) {
                      setSelectedPeriod(period);
                      // 确保滚动到正确的位置
                      setTimeout(() => {
                        amPmScrollViewRef.current?.scrollTo({
                          y: period === 'AM' ? 0 : 40,
                          animated: false,
                        });
                      }, 10);
                    }

                    setIsScrolling(false);
                  }}
                  onScrollBeginDrag={() => {
                    setIsScrolling(true);
                  }}
                >
                  <TouchableOpacity
                    style={styles.timeItem}
                    activeOpacity={0.8}
                    onPress={() => {
                      if (selectedPeriod !== 'AM') {
                        setSelectedPeriod('AM');
                        amPmScrollViewRef.current?.scrollTo({ y: 0, animated: true });
                      }
                    }}
                  >
                    <Text style={[styles.timeItemText, selectedPeriod === 'AM' && styles.selectedTimeItemText]}>
                      AM
                    </Text>
                  </TouchableOpacity>

                  <TouchableOpacity
                    style={styles.timeItem}
                    activeOpacity={0.8}
                    onPress={() => {
                      if (selectedPeriod !== 'PM') {
                        setSelectedPeriod('PM');
                        amPmScrollViewRef.current?.scrollTo({ y: 40, animated: true });
                      }
                    }}
                  >
                    <Text style={[styles.timeItemText, selectedPeriod === 'PM' && styles.selectedTimeItemText]}>
                      PM
                    </Text>
                  </TouchableOpacity>
                </ScrollView>
              </View>

              {/* 小时选择器 */}
              <View style={styles.timeColumn}>
                <ScrollView
                  ref={hourScrollViewRef}
                  showsVerticalScrollIndicator={false}
                  contentContainerStyle={styles.timeScrollContent}
                  snapToInterval={40}
                  decelerationRate="fast"
                  nestedScrollEnabled={true}
                  scrollEnabled={true}
                  directionalLockEnabled={true}
                  disableScrollViewPanResponder={false}
                  onStartShouldSetResponder={() => true}
                  onStartShouldSetResponderCapture={() => true}
                  onMoveShouldSetResponder={() => true}
                  onMoveShouldSetResponderCapture={() => true}
                  onScroll={() => {}}
                  scrollEventThrottle={160}
                  onMomentumScrollEnd={(event) => {
                    if (!initialScrollDone) {
                      return;
                    }

                    const y = event.nativeEvent.contentOffset.y;
                    const index = Math.round(y / 40);
                    if (index >= 0 && index < hourItems.length) {
                      const hour = hourItems[index];
                      if (hour !== selectedHour) {
                        setSelectedHour(hour);
                        // 确保滚动到正确的位置
                        setTimeout(() => {
                          hourScrollViewRef.current?.scrollTo({
                            y: index * 40,
                            animated: false,
                          });
                        }, 10);
                      }
                    }

                    setIsScrolling(false);
                  }}
                  onScrollBeginDrag={() => {
                    setIsScrolling(true);
                  }}
                >
                  {hourItems.map((hour, index) => (
                    <TouchableOpacity
                      key={`hour-${hour}`}
                      style={styles.timeItem}
                      activeOpacity={0.8}
                      onPress={() => {
                        if (selectedHour !== hour) {
                          setSelectedHour(hour);
                          hourScrollViewRef.current?.scrollTo({ y: index * 40, animated: true });
                        }
                      }}
                    >
                      <Text style={[styles.timeItemText, selectedHour === hour && styles.selectedTimeItemText]}>
                        {hour}
                      </Text>
                    </TouchableOpacity>
                  ))}
                </ScrollView>
              </View>

              {/* 分隔符 */}
              <Text style={styles.timeSeparator}>:</Text>

              {/* 分钟选择器 */}
              <View style={styles.timeColumn}>
                <ScrollView
                  ref={minuteScrollViewRef}
                  showsVerticalScrollIndicator={false}
                  contentContainerStyle={styles.timeScrollContent}
                  snapToInterval={40}
                  decelerationRate="fast"
                  nestedScrollEnabled={true}
                  scrollEnabled={true}
                  directionalLockEnabled={true}
                  disableScrollViewPanResponder={false}
                  onStartShouldSetResponder={() => true}
                  onStartShouldSetResponderCapture={() => true}
                  onMoveShouldSetResponder={() => true}
                  onMoveShouldSetResponderCapture={() => true}
                  onScroll={() => {}}
                  scrollEventThrottle={160}
                  onMomentumScrollEnd={(event) => {
                    if (!initialScrollDone) {
                      return;
                    }

                    const y = event.nativeEvent.contentOffset.y;
                    const index = Math.round(y / 40);
                    if (index >= 0 && index < minuteItems.length) {
                      const minute = minuteItems[index];
                      if (minute !== selectedMinute) {
                        setSelectedMinute(minute);
                        // 确保滚动到正确的位置
                        setTimeout(() => {
                          minuteScrollViewRef.current?.scrollTo({
                            y: index * 40,
                            animated: false,
                          });
                        }, 10);
                      }
                    }

                    setIsScrolling(false);
                  }}
                  onScrollBeginDrag={() => {
                    setIsScrolling(true);
                  }}
                >
                  {minuteItems.map((minute, index) => (
                    <TouchableOpacity
                      key={`minute-${minute}`}
                      style={styles.timeItem}
                      activeOpacity={0.8}
                      onPress={() => {
                        if (selectedMinute !== minute) {
                          setSelectedMinute(minute);
                          minuteScrollViewRef.current?.scrollTo({ y: index * 40, animated: true });
                        }
                      }}
                    >
                      <Text style={[styles.timeItemText, selectedMinute === minute && styles.selectedTimeItemText]}>
                        {minute}
                      </Text>
                    </TouchableOpacity>
                  ))}
                </ScrollView>
              </View>
            </View>
          </View>

          {/* 星期选择 */}
          <View style={styles.sectionContainer}>
            <View style={styles.sectionTitleContainer}>
              <Image
                source={require('../assets/images/home/<USER>')}
                style={styles.sectionIcon}
              />
              <Text style={styles.sectionTitle}>{t('alarm.week')}</Text>
            </View>

            <View style={styles.weekContainer}>
              <TouchableOpacity
                style={[styles.dayButton, selectedDays.sun && styles.selectedDay]}
                onPress={() => handleDaySelect('sun')}
              >
                <Text
                  style={[
                    styles.dayText,
                    styles.sundayText,
                    selectedDays.sun && styles.selectedDayText,
                  ]}
                >
                  SUN
                </Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.dayButton, selectedDays.mon && styles.selectedDay]}
                onPress={() => handleDaySelect('mon')}
              >
                <Text
                  style={[styles.dayText, selectedDays.mon && styles.selectedDayText]}
                >
                  MON
                </Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.dayButton, selectedDays.tue && styles.selectedDay]}
                onPress={() => handleDaySelect('tue')}
              >
                <Text
                  style={[styles.dayText, selectedDays.tue && styles.selectedDayText]}
                >
                  TUE
                </Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.dayButton, selectedDays.wed && styles.selectedDay]}
                onPress={() => handleDaySelect('wed')}
              >
                <Text
                  style={[styles.dayText, selectedDays.wed && styles.selectedDayText]}
                >
                  WED
                </Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.dayButton, selectedDays.thu && styles.selectedDay]}
                onPress={() => handleDaySelect('thu')}
              >
                <Text
                  style={[styles.dayText, selectedDays.thu && styles.selectedDayText]}
                >
                  THU
                </Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.dayButton, selectedDays.fri && styles.selectedDay]}
                onPress={() => handleDaySelect('fri')}
              >
                <Text
                  style={[styles.dayText, selectedDays.fri && styles.selectedDayText]}
                >
                  FRI
                </Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.dayButton, selectedDays.sat && styles.selectedDay]}
                onPress={() => handleDaySelect('sat')}
              >
                <Text
                  style={[
                    styles.dayText,
                    styles.saturdayText,
                    selectedDays.sat && styles.selectedDayText,
                  ]}
                >
                  SAT
                </Text>
              </TouchableOpacity>
            </View>
          </View>

          {/* 消息输入 */}
          <View style={styles.sectionContainer}>
            <View style={styles.sectionTitleContainer}>
              <Image
                source={require('../assets/images/home/<USER>')}
                style={styles.sectionIcon}
              />
              <Text style={styles.sectionTitle}>MESSAGE</Text>
            </View>

            <TextInput
              style={styles.disabledMessageInput}
              value={message}
              editable={false}
              placeholder="Message cannot be edited."
              placeholderTextColor="#666666"
              multiline
              maxLength={100}
            />
            <Text style={styles.charCount}>{`(${message.length} / 100)`}</Text>
          </View>

          {/* 背景选择 */}
          <View style={styles.sectionContainer}>
            <View style={styles.sectionTitleContainer}>
              <Image
                source={require('../assets/images/home/<USER>')}
                style={styles.sectionIcon}
              />
              <Text style={styles.sectionTitle}>BACKGROUND</Text>
            </View>

            <View style={styles.backgroundContainer}>
              {/* 轮播组件和箭头 */}
              <View style={styles.carouselWrapper}>
                {/* 轮播组件 */}
                <Carousel
                  ref={carouselRef}
                  width={width - 40}
                  height={250}
                  data={backgrounds}
                  renderItem={({ item, index }: { item: any; index: number }) => {
                    return (
                      <TouchableOpacity
                        activeOpacity={0.9}
                        onPress={() => {
                          const now = Date.now();
                          const DOUBLE_PRESS_DELAY = 300;
                          if (lastTapRef.current && (now - lastTapRef.current) < DOUBLE_PRESS_DELAY) {
                            // 双击事件
                            handleDoubleClickBackground(index);
                          } else {
                            // 单击事件
                            setCurrentBackground(index);
                            // 重置背景更新标志，确保单击不会更新背景图片
                            setShouldUpdateBackground(false);
                            lastTapRef.current = now;
                          }
                        }}
                      >
                        <Image
                          source={item}
                          style={styles.backgroundImage}
                          resizeMode="cover"
                        />
                      </TouchableOpacity>
                    );
                  }}
                  onSnapToItem={(index) => {
                    // 重置背景更新标志，确保滑动切换不会更新背景图片
                    setShouldUpdateBackground(false);
                    setCurrentBackground(index);
                  }}
                  loop
                  enabled={true}
                />

                {/* 左箭头 - 放在轮播图内部 */}
                <TouchableOpacity
                  style={[styles.backgroundArrow, styles.leftArrow]}
                  onPress={() => handleBackgroundChange('prev')}
                >
                  <Image
                    source={require('../assets/images/home/<USER>')}
                    style={styles.arrowImage}
                  />
                </TouchableOpacity>

                {/* 右箭头 - 放在轮播图内部 */}
                <TouchableOpacity
                  style={[styles.backgroundArrow, styles.rightArrow]}
                  onPress={() => handleBackgroundChange('next')}
                >
                  <Image
                    source={require('../assets/images/home/<USER>')}
                    style={styles.arrowImage}
                  />
                </TouchableOpacity>
              </View>
            </View>

            <Text style={styles.backgroundHint}>
              Click the photo to set the background that you want to use with the voice alarm.
            </Text>

            {/* 背景选择指示器 */}
            <View style={styles.indicatorContainer}>
              {backgrounds.map((_: any, index: number) => (
                <View
                  key={index}
                  style={[
                    styles.indicator,
                    index === currentBackground && styles.activeIndicator,
                  ]}
                />
              ))}
            </View>
          </View>

          {/* 选项 */}
          <View style={styles.sectionContainer}>
            <View style={styles.sectionTitleContainer}>
              <Image
                source={require('../assets/images/home/<USER>')}
                style={styles.sectionIcon}
              />
              <Text style={styles.sectionTitle}>OPTION</Text>
            </View>

            <View style={styles.optionContainer}>
              <View style={styles.optionItem}>
                <View style={styles.optionTextContainer}>
                  <Image
                    source={require('../assets/images/home/<USER>')}
                    style={styles.optionIcon}
                  />
                  <Text style={styles.optionText}>Vibration</Text>
                </View>
                <CustomSwitch
                  value={vibrationEnabled}
                  onValueChange={setVibrationEnabled}
                />
              </View>

              <View style={styles.optionDivider} />

              <View style={styles.optionItem}>
                <View style={styles.optionTextContainer}>
                  <Image
                    source={require('../assets/images/home/<USER>')}
                    style={styles.optionIcon}
                  />
                  <Text style={styles.optionText}>Replay</Text>
                </View>
                <CustomSwitch
                  value={replayEnabled}
                  onValueChange={setReplayEnabled}
                />
              </View>
            </View>
          </View>

          {/* Edit Celeb Voice Alarm 文案 */}
          <View style={styles.createLabelContainer}>
            <Text style={styles.createLabelText}>Edit Celeb Voice Alarm</Text>
          </View>

          {/* 底部按钮容器 */}
          <View style={styles.buttonContainer}>
            {/* 删除按钮 */}
            <TouchableOpacity
              style={[styles.actionButton, styles.deleteButton]}
              onPress={handleDeleteAlarm}
            >
              <Image
                source={require('../assets/images/home/<USER>')}
                style={styles.actionButtonIcon}
              />
              <Text style={styles.actionButtonText}>Delete</Text>
            </TouchableOpacity>

            {/* 编辑按钮 */}
            <TouchableOpacity
              style={[styles.actionButton, styles.editButton]}
              onPress={handleEditAlarm}
            >
              <Image
                source={require('../assets/images/home/<USER>')}
                style={styles.actionButtonIcon}
              />
              <Text style={styles.actionButtonText}>Edit</Text>
            </TouchableOpacity>
          </View>

        </View>
      </ScrollView>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000000',
  },
  loadingOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 9999,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#000000',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#000000',
    padding: 20,
  },
  errorText: {
    color: '#FF4747',
    fontFamily: FONTS.pretendard.light,
    fontSize: 16,
    textAlign: 'center',
  },
  scrollView: {
    flex: 1,
  },
  content: {
    padding: 20,
  },
  celebrityContainer: {
    alignItems: 'center',
    marginBottom: 20,
  },
  celebrityImage: {
    width: 80,
    height: 80,
    borderRadius: 40,
  },
  celebrityName: {
    color: '#FFFFFF',
    fontFamily: FONTS.bebasNeue.regular,
    fontSize: 18,
    marginTop: 10,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 20,
    width: '100%',
    paddingHorizontal :30,
  },
  inputLabel: {
    color: '#FFFFFF',
    fontFamily: FONTS.pretendard.light,
    fontSize: 16,
    marginRight: 10,
  },
  titleInput: {
    flex: 1,
    color: '#FFFFFF',
    fontFamily: FONTS.pretendard.light,
    fontSize: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#333333',
    textAlign: 'center',
    paddingVertical: 5,
  },
  charCount: {
    color: '#666666',
    fontFamily: FONTS.pretendard.light,
    fontSize: 12,
    marginLeft: 10,
    alignSelf: 'flex-end',
  },
  sectionContainer: {
    marginBottom: 30,
  },
  sectionTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 15,
  },
  sectionIcon: {
    width: 20,
    height: 20,
    tintColor: '#D9C091',
    marginRight: 10,
    resizeMode:'contain',
  },
  sectionTitle: {
    color: '#D9C091',
    fontFamily: FONTS.bebasNeue.regular,
    fontSize: 18,
    letterSpacing: 1,
  },
  timeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 15,
  },
  selectedPeriod: {
    backgroundColor: 'transparent',
  },
  periodText: {
    color: '#666666',
    fontFamily: FONTS.bebasNeue.regular,
    fontSize: 18,
    textAlign: 'center',
    height: 40,
    lineHeight: 40,
    paddingTop: 5,
  },
  selectedPeriodText: {
    color: '#FFFFFF',
    fontSize: 24,
    fontWeight: 'bold',
  },
  timeValue: {
    color: '#FFFFFF',
    fontFamily: FONTS.bebasNeue.regular,
    fontSize: 24,
    width: 40,
    textAlign: 'center',
  },
  timeSeparator: {
    color: '#FFFFFF',
    fontFamily: FONTS.bebasNeue.regular,
    fontSize: 24,
    marginHorizontal: 5,
  },
  // 滚动版时间选择器样式
  timePickerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    height: 120,
    marginVertical: 20,
    position: 'relative',
    zIndex: 10, // 提高 z-index，确保它在其他元素之上
  },
  timePickerHighlight: {
    position: 'absolute',
    left: 0,
    right: 0,
    height: 40,
    top: '50%',
    marginTop: -20,
    borderTopWidth: 1,
    borderBottomWidth: 1,
    borderColor: '#333333',
    zIndex: 1,
  },
  timeColumn: {
    width: 60,
    height: 120,
    overflow: 'hidden',
    marginHorizontal: 5,
    zIndex: 2, // 确保它在高亮区域之上
  },
  timeScrollContent: {
    paddingVertical: 40,
  },
  timeItem: {
    height: 40,
    alignItems: 'center',
    justifyContent: 'center',
    width: '100%',
  },
  timeItemText: {
    color: '#666666',
    fontFamily: FONTS.bebasNeue.regular,
    fontSize: 18,
    textAlign: 'center',
  },
  selectedTimeItemText: {
    color: '#FFFFFF',
    fontSize: 24,
    fontWeight: 'bold',
  },

  weekContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    flexWrap: 'wrap',
  },
  dayButton: {
    width: (width - 40) / 7 - 5,
    paddingVertical: 10,
    alignItems: 'center',
    borderRadius: 5,
    marginHorizontal: 2,
  },
  selectedDay: {
    backgroundColor: '#D9C091',
  },
  dayText: {
    color: '#FFFFFF',
    fontFamily: FONTS.pretendard.light,
    fontSize: 14,
  },
  sundayText: {
    color: '#D60000', // 红色
  },
  saturdayText: {
    color: '#8C673A', // 棕色
  },
  selectedDayText: {
    color: '#000000',
  },
  messageInput: {
    color: '#FFFFFF',
    fontFamily: FONTS.pretendard.light,
    fontSize: 16,
    borderWidth: 1,
    borderColor: '#333333',
    borderRadius: 5,
    padding: 10,
    height: 130,
    textAlignVertical: 'top',
    width: '100%',
    alignSelf: 'center',
  },
  disabledMessageInput: {
    color: '#FFFFFF',
    fontFamily: FONTS.pretendard.light,
    fontSize: 16,
    borderWidth: 1,
    borderColor: '#333333',
    borderRadius: 5,
    padding: 10,
    height: 130,
    textAlignVertical: 'top',
    width: '100%',
    alignSelf: 'center',
    backgroundColor: '#4d4d4d',
  },
  backgroundContainer: {
    marginBottom: 10,
    alignItems: 'center',
  },
  carouselWrapper: {
    width: width - 40,
    height: 250,
    position: 'relative',
    borderRadius: 0, // 确保没有圆角
    overflow: 'hidden',
  },
  backgroundArrow: {
    position: 'absolute',
    padding: 8,
    // 移除背景色
    zIndex: 10,
    top: '50%',
    marginTop: -20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  leftArrow: {
    left: 10,
  },
  rightArrow: {
    right: 10,
  },
  arrowImage: {
    width: 15,
    height: 15,
    resizeMode: 'contain',
    tintColor: '#FFFFFF',
  },
  backgroundImage: {
    width: width - 40,
    height: 250,
    borderRadius: 0, // 移除圆角
  },
  backgroundHint: {
    color: '#666666',
    fontFamily: FONTS.pretendard.light,
    fontSize: 12,
    textAlign: 'center',
    marginBottom: 10,
  },
  indicatorContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginTop: 10,
  },
  indicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#333333',
    marginHorizontal: 5,
  },
  activeIndicator: {
    backgroundColor: '#FFFFFF',
  },
  optionContainer: {
    borderWidth: 1,
    borderColor: '#333333',
    borderRadius: 5,
    width: '100%',
    alignSelf: 'center',
  },
  optionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 15,
  },
  optionTextContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  optionIcon: {
    width: 20,
    height: 20,
    tintColor: '#FFFFFF',
    marginRight: 10,
  },
  optionText: {
    color: '#FFFFFF',
    fontFamily: FONTS.pretendard.light,
    fontSize: 16,
  },
  optionDivider: {
    height: 1,
    backgroundColor: '#333333',
  },
  bottomSpace: {
    height: 20,
  },
  createLabelContainer: {
    alignItems: 'center',
    marginBottom: 10,
    paddingHorizontal: 20,
  },
  createLabelText: {
    color: '#FFFFFF',
    fontFamily: FONTS.pretendard.light,
    fontSize: 14,
    marginBottom: 5,
  },
  createButton: {
    marginHorizontal: 20,
    marginBottom: 20,
    backgroundColor: '#6A4BFF',
    paddingVertical: 15,
    borderRadius: 5,
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'row',
  },
  createButtonIcon: {
    width: 20,
    height: 20,
    marginRight: 10,
    tintColor: '#FFFFFF',
  },
  createButtonText: {
    color: '#FFFFFF',
    fontFamily: FONTS.bebasNeue.regular,
    fontSize: 18,
    letterSpacing: 1,
  },

  // 编辑页面按钮样式
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginHorizontal: 20,
    marginBottom: 20,
  },
  actionButton: {
    flex: 1,
    paddingVertical: 15,
    borderRadius: 5,
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'row',
  },
  deleteButton: {
    backgroundColor: '#FF3B30', // 按设计稿修改为 #FF3B30
    marginRight: 10,
  },
  editButton: {
    backgroundColor: '#7246BB', // 按设计稿修改为 #7246BB
    marginLeft: 10,
  },
  actionButtonText: {
    color: '#FFFFFF',
    fontFamily: FONTS.bebasNeue.regular,
    fontSize: 18,
    letterSpacing: 1,
  },
  actionButtonIcon: {
    width: 15,
    height: 15,
    marginRight: 10,
    resizeMode:'contain',
    tintColor: '#FFFFFF',
  },



  // 验证弹窗样式
  alertOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 9999,
    elevation: 10,
  },
  alertContainer: {
    width: width * 0.85,
    backgroundColor: '#222222',
    borderRadius: 10,
    overflow: 'hidden',
  },
  alertMessage: {
    color: '#FF0066',
    fontSize: 16,
    fontFamily: FONTS.pretendard.light,
    textAlign: 'center',
    paddingVertical: 30,
    paddingHorizontal: 20,
  },
  alertDivider: {
    height: 1,
    backgroundColor: '#444444',
    marginHorizontal: 0,
  },
  alertButton: {
    backgroundColor: '#007AFF',
    paddingVertical: 15,
    alignItems: 'center',
    margin: 15,
    borderRadius: 10,
  },
  alertButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontFamily: FONTS.pretendard.light,
    fontWeight: '500',
  },

  // 弹窗样式
  modalOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1000,
  },
  modalContainer: {
    width: '90%',
    backgroundColor: '#1A1A1A',
    borderRadius: 10,
    padding: 20,
    alignItems: 'center',
    maxWidth: 400,
  },
  modalHandle: {
    width: 40,
    height: 4,
    backgroundColor: '#666666',
    borderRadius: 2,
    marginBottom: 20,
  },
  modalTitle: {
    color: '#D9C091',
    fontFamily: FONTS.bebasNeue.regular,
    fontSize: 28,
    letterSpacing: 2,
    marginBottom: 15,
  },
  deleteModalTitle: {
    color: '#D9C091',
    fontFamily: FONTS.bebasNeue.regular,
    fontSize: 28,
    letterSpacing: 2,
    marginBottom: 15,
  },
  modalDivider: {
    width: '100%',
    height: 1,
    backgroundColor: '#333333',
    marginBottom: 20,
  },
  modalText: {
    color: '#FFFFFF',
    fontFamily: FONTS.pretendard.light,
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 30,
    lineHeight: 24,
  },
  modalButtonsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
  },
  modalButton: {
    borderRadius: 10,
    paddingVertical: 12,
    paddingHorizontal: 20,
    width: '48%',
    alignItems: 'center',
  },
  modalButtonNo: {
    backgroundColor: '#FF4D4D',
  },
  modalButtonYes: {
    backgroundColor: '#007AFF',
  },
  deleteModalButtonNo: {
    backgroundColor: '#FF3B30',
  },
  deleteModalButtonYes: {
    backgroundColor: '#007AFF',
  },
  modalButtonText: {
    color: '#FFFFFF',
    fontFamily: FONTS.bebasNeue.regular,
    fontSize: 18,
    letterSpacing: 1,
  },
});

export default EditVoiceAlarmScreen;
