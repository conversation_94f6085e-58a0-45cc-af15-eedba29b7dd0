import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  SafeAreaView,
  ImageBackground,
  Dimensions,
  Image,
  StatusBar,
  Alert,
} from 'react-native';
import {
  GoogleSignin,
  statusCodes,
} from '@react-native-google-signin/google-signin';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { useTranslation } from 'react-i18next';

// 导入图片资源
import { IMAGES } from '../assets/images';

// 导入API服务
import { post } from '../services/api/apiService';
// 导入用户存储服务
import { saveToken, saveUserInfo } from '../services/storage/userStorage';
// 导入Toast服务
import { showSuccess, showError } from '../services/toast';

// 定义导航类型
type AuthStackParamList = {
  LoginEntry: undefined;
  Login: undefined;
  Agreement: undefined;
  SignUpForm: undefined;
  SignUpProfile: undefined;
};

type LoginScreenNavigationProp = StackNavigationProp<AuthStackParamList, 'LoginEntry'>;

const LoginEntryScreen = () => {
  const navigation = useNavigation<LoginScreenNavigationProp>();
  const { t } = useTranslation();
  const [isGoogleSigninInProgress, setIsGoogleSigninInProgress] = useState(false);

  // 初始化Google登录
  useEffect(() => {
    GoogleSignin.configure({
      // 使用 Web 客户端 ID，而不是 Android 客户端 ID
      webClientId: '198554363944-d2hhf86gbps63h7k1ojo83t5etjjgkim.apps.googleusercontent.com', // 从 Google Cloud Console 获取
      // iosClientId: '198554363944-o9fj1gktvoninmd2p558l9rogmvlodr2.apps.googleusercontent.com', // 为iOS应用添加客户端ID
      offlineAccess: true, // 如果你需要访问 Google API
      forceCodeForRefreshToken: true, // 强制获取新的刷新令牌
      scopes: ['profile', 'email'], // 请求的权限范围
    });

    // 打印当前配置，用于调试
    console.log('Google Sign-In configured with:', {
      webClientId: '198554363944-d2hhf86gbps63h7k1ojo83t5etjjgkim.apps.googleusercontent.com',
      offlineAccess: true,
      forceCodeForRefreshToken: true,
      scopes: ['profile', 'email'],
    });
  }, []);

  // 处理Google登录成功后的操作
  const handleGoogleSignInSuccess = async (userInfo: any) => {
    try {
      console.log('Google Sign-In success:', userInfo);

      // 确保 userInfo 有正确的结构
      if (!userInfo || !userInfo.user) {
        // 检查是否是新的数据结构 (type: "success", data: {...})
        if (userInfo && userInfo.type === 'success' && userInfo.data && userInfo.data.user) {
          // 使用新的数据结构
          userInfo = {
            user: userInfo.data.user,
          };
        } else {
          console.error('Invalid userInfo structure:', userInfo);
          throw new Error('无效的用户信息结构');
        }
      }

      // 构建要发送给后端的数据
      // 获取设备ID，用于登录
      const { getDeviceId } = await import('../services/api/authService');
      const cid = await getDeviceId();

      // 从 userInfo 中提取用户数据
      const user = userInfo.user;

      // 添加设备ID到用户数据中
      user.cid = cid;

      console.log('Sending Google user data to backend:', user);
      const _googleInfo = { ...user, ...{
          "nickname": user.name,
          "unionid":user.id,
          "openid": user.id,
          "openId": user.id,
          "nickName": user.name,
      }}
      // 调用后端 API 进行 Google 登录
      // 直接使用 /three/googleLogin 接口，传递 user 数据
      const response = await post('/three/googleLogin', _googleInfo);
      console.log('Google login response:', JSON.stringify(response.data));
      if (response.data && response.data.code === '200') {
        // 登录成功，保存用户信息和令牌
        if (response.data.data && response.data.data.token) {
          await saveToken(response.data.data.token);
          await saveUserInfo(response.data.data);

          showSuccess(t('auth.loginSuccess'));

          // 导航到主页
          navigation.reset({
            index: 0,
            routes: [{ name: 'Home' as never }],
          });
        }
      } else {
        // 登录失败
        showError(response.data.msg || t('auth.loginFailed'));
      }
    } catch (error) {
      console.error('Error processing Google sign-in:', error);
      showError(t('auth.errorProcessingGoogleLogin'));
    }
  };

  // Google登录处理函数
  const handleGoogleSignIn = async () => {
    if (isGoogleSigninInProgress) {
      return;
    }
    try {
      setIsGoogleSigninInProgress(true);
      console.log('Google Sign-In started');

      // 打印当前配置信息
      try {
        const tokens = await GoogleSignin.getTokens();
        console.log('Current Google Sign-In configuration:', tokens);

        // 如果能获取到令牌，说明用户已经登录
        console.log('User already signed in, signing out first...');
        await GoogleSignin.signOut();
        console.log('Successfully signed out');
      } catch (e) {
        // 如果获取令牌失败，说明用户未登录
        console.log('User not signed in');
      }

      // 检查Play服务
      console.log('Checking Google Play Services...');
      const res = await GoogleSignin.hasPlayServices({
        showPlayServicesUpdateDialog: true,
      });
      console.log('Google Play Services check result:', res);

      // 获取应用签名信息（仅用于调试）
      console.log('App package name:', 'com.shinest.aivgen');

      // 执行登录
      console.log('Starting Google Sign-In flow...');
      const userInfo = await GoogleSignin.signIn();
      console.log('Google Sign-In successful, user info:', JSON.stringify(userInfo));

      // 处理登录成功
      await handleGoogleSignInSuccess(userInfo);
    } catch (error: any) {
      console.error('Google Sign-In Error:', error);
      console.error('Error code:', error.code);
      console.error('Error message:', error.message);

      if (error.code === statusCodes.SIGN_IN_CANCELLED) {
        console.log('User cancelled the login flow');
      } else if (error.code === statusCodes.IN_PROGRESS) {
        console.log('Sign in is in progress already');
      } else if (error.code === statusCodes.PLAY_SERVICES_NOT_AVAILABLE) {
        console.error('Google Play Services not available');
        Alert.alert('错误', 'Google Play 服务不可用，请确保已安装最新版本的 Google Play 服务。');
      } else if (error.code === 10) { // 10 是 DEVELOPER_ERROR 的代码
        console.error('DEVELOPER_ERROR details:', JSON.stringify(error));
        console.error('Error message:', error.message);
        console.error('Error stack:', error.stack);

        // 打印当前配置
        console.error('Current configuration:', {
          webClientId: '198554363944-d2hhf86gbps63h7k1ojo83t5etjjgkim.apps.googleusercontent.com',
          packageName: 'com.shinest.aivgen',
        });

        // 检查是否需要创建 Web 客户端 ID
        console.log('提示：DEVELOPER_ERROR 可能是因为需要创建 Web 客户端 ID，而不是 Android 客户端 ID');
        console.log('请在 Google Cloud Console 中创建一个 Web 应用类型的 OAuth 客户端 ID');
        console.log('或者检查 SHA-1 证书指纹是否正确配置');

        // 显示更详细的错误信息
        Alert.alert(
          '开发者错误',
          '请检查 Google 登录配置是否正确。\n\n' +
          '错误详情：' + error.message + '\n\n' +
          '可能的解决方案：\n' +
          '1. 在 Google Cloud Console 中创建一个 Web 应用类型的 OAuth 客户端 ID\n' +
          '2. 确保已启用 Google Sign-In API\n' +
          '3. 确保应用包名与 Google Cloud Console 中配置的一致\n' +
          '4. 确保 SHA-1 证书指纹与 Google Cloud Console 中配置的一致\n' +
          '5. 确保已在 Google Cloud Console 中添加 Google Play 签名的 SHA-1 指纹'
        );
      } else {
        console.error('Unknown error during Google Sign-In:', error);
        Alert.alert('错误', '登录失败：' + (error.message || '未知错误'));
      }
    } finally {
      setIsGoogleSigninInProgress(false);
    }
  };

  // 登录处理函数
  const handleSignIn = (provider: 'aivgen' | 'google' | 'facebook') => {
     console.log(`Sign in with ${provider}`);

    if (provider === 'aivgen') {
      // 如果是 AIVGEN 登录，跳转到登录页面
      navigation.navigate('Login');
    } else if (provider === 'google') {
      // 处理Google登录
      handleGoogleSignIn();
    } else if (provider === 'facebook') {
      // Facebook登录逻辑
      Alert.alert('提示', 'Facebook 登录功能正在开发中');
    }
  };

  // 注册处理函数
  const handleSignUp = () => {
    console.log('Navigate to agreement screen');
    navigation.navigate('Agreement');
  };

  return (
    <>
      <StatusBar
        barStyle="light-content"
        backgroundColor="transparent"
        translucent={true}
      />
      <ImageBackground
        source={IMAGES.loginBackground}
        style={styles.backgroundImage}
        resizeMode="cover"
      >
      <SafeAreaView style={styles.container}>
        {/* 登录按钮 */}
        <View style={styles.buttonsContainer}>
          {/* AIVGEN登录按钮 */}
          <TouchableOpacity
            style={styles.signInButton}
            onPress={() => handleSignIn('aivgen')}
          >
            <Image source={IMAGES.loginIcons.aivgen} style={styles.iconImage} />
            <Text style={styles.signInText}>{t('auth.signInWithAivgen')}</Text>
          </TouchableOpacity>

          {/* Google登录按钮 */}
          <TouchableOpacity
            style={styles.signInButton}
            onPress={() => handleSignIn('google')}
          >
            <Image source={IMAGES.loginIcons.google} style={styles.iconImage} />
            <Text style={styles.signInText}>{t('auth.signInWithGoogle')}</Text>
          </TouchableOpacity>

          {/* Facebook登录按钮 */}
          <TouchableOpacity
            style={styles.signInButton}
            onPress={() => handleSignIn('facebook')}
          >
            <Image source={IMAGES.loginIcons.facebook} style={styles.iconImage} />
            <Text style={styles.signInText}>{t('auth.signInWithFacebook')}</Text>
          </TouchableOpacity>
        </View>

        {/* 注册链接 */}
        <TouchableOpacity
          style={styles.signUpContainer}
          onPress={handleSignUp}
        >
          <Text style={styles.signUpText}>{t('auth.signUp')}</Text>
        </TouchableOpacity>


      </SafeAreaView>
    </ImageBackground>
    </>
  );
};

const styles = StyleSheet.create({
  backgroundImage: {
    flex: 1,
    width: '100%',
    height: '100%',
  },
  container: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'flex-end', // 将内容向底部对齐
    paddingBottom: 80, // 增加底部空间
  },
  buttonsContainer: {
    width: '100%',
    alignItems: 'center',
    marginBottom: 20,
  },
  signInButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#EEEEEE',
    borderRadius: 25,
    paddingVertical: 12,
    paddingHorizontal: 20,
    marginBottom: 15,
    // 根据设计稿的比例计算宽度，设计稿宽750px，按钮宽450px，比例为0.6
    width: Dimensions.get('window').width * 0.6,
  },
  iconImage: {
    width: 24,
    height: 24,
    marginRight: 10,
  },
  signInText: {
    color: '#333333',
    fontSize: 14,
    fontWeight: '500',
  },
  signUpContainer: {
    marginBottom: 20,
  },
  signUpText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '500',
    textDecorationLine: 'underline',
  },
  copyrightContainer: {
    marginBottom: 10,
  },
  copyrightText: {
    color: '#FFFFFF',
    fontSize: 12,
    opacity: 0.6,
  },
});

export default LoginEntryScreen;
