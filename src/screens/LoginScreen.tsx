import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  TextInput,
  Image,
  ActivityIndicator,
  Dimensions,
  SafeAreaView,
  Platform,
  StatusBar,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';

// 导入图片资源
import { IMAGES } from '../assets/images';
import { FONTS } from '../assets/fonts';
import { Header, ForgotPasswordModal } from '../components';
import { useAlert } from '../context/AlertContext';
import { useTranslation } from 'react-i18next';

// 导入API服务
import { login } from '../services/api/authService';
import { saveToken, saveUserInfo, UserInfo } from '../services/storage/userStorage';

// 定义导航类型
type AuthStackParamList = {
  Login: undefined;
  LoginEntry: undefined;
  Home: undefined;
};

type LoginScreenNavigationProp = StackNavigationProp<AuthStackParamList, 'Login'>;

const LoginScreen = () => {
  const navigation = useNavigation<LoginScreenNavigationProp>();
  const { showAlert } = useAlert();
  const { t } = useTranslation();
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [forgotPasswordModalVisible, setForgotPasswordModalVisible] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  // 处理登录
  const handleSignIn = async () => {
    console.log('是否点击====')
    // 验证表单
    if (!email || !email.includes('@')) {
      showAlert(t('auth.invalidEmail'));
      return;
    }

    if (!password || password.length < 6) {
      showAlert(t('auth.passwordTooShort'));
      return;
    }

    try {
      setIsLoading(true);
      console.log('Signing in with:', email, password);

      // 调用登录API
      const response = await login(email, password);
      console.log('Login response:', JSON.stringify(response.data));

      // 处理登录成功响应
      if (response.data && response.data.code === '200' && response.data.data) {
        const userData = response.data.data;

        // 保存token
        if (userData.token) {
          await saveToken(userData.token);
          console.log('Token saved successfully');
        }

        // 保存用户信息
        await saveUserInfo(userData as UserInfo);
        console.log('User info saved successfully');

        // 登录成功后跳转到首页
        navigation.navigate('Home');
      } else {
        // 服务器返回了成功状态码，但数据不符合预期
        showAlert(response.data.msg);
      }
    } catch (error: any) {
      // 处理登录错误
      console.error('Login error:', error);
      showAlert(error.message);
    } finally {
      setIsLoading(false);
    }
  };

  // 处理忘记密码
  const handleForgotPassword = () => {
    console.log('Forgot password for:', email);
    if (email.trim() === '') {
      // 如果邮箱为空，提示用户输入邮箱
      showAlert(t('auth.enterEmailFirst'));
      return;
    }
    // 显示忘记密码弹窗
    setForgotPasswordModalVisible(true);
  };

  // 关闭忘记密码弹窗
  const handleCloseForgotPasswordModal = () => {
    setForgotPasswordModalVisible(false);
  };

  return (
    <SafeAreaView style={styles.safeArea}>
      <View style={styles.container}>
        {/* 背景图片 */}
        <Image
          source={IMAGES.signupBackground}
          style={styles.backgroundImage}
          resizeMode="contain"
        />

        {/* 顶部导航 */}
        <Header showBack={true} showLogo={true} />

        {/* 登录标题 */}
        <View style={styles.titleContainer}>
          <View style={styles.titleIndicator} />
          <Text style={styles.title}>{t('auth.signIn')}</Text>
        </View>

        {/* 邮箱输入 */}
        <View style={styles.inputSection}>
          <Text style={styles.inputLabel}>{t('auth.enterEmail')}</Text>
          <TextInput
            style={styles.input}
            value={email}
            onChangeText={setEmail}
            placeholder=""
            placeholderTextColor="#666"
            keyboardType="email-address"
            autoCapitalize="none"
          />
        </View>

        {/* 密码输入 */}
        <View style={styles.inputSection}>
          <Text style={styles.inputLabel}>{t('auth.enterPassword')}</Text>
          <View style={styles.passwordContainer}>
            <TextInput
              style={styles.input}
              value={password}
              onChangeText={setPassword}
              placeholder=""
              placeholderTextColor="#666"
              secureTextEntry={!showPassword}
              autoCapitalize="none"
            />
            <TouchableOpacity
              style={styles.eyeIcon}
              onPress={() => setShowPassword(!showPassword)}
            >
              <Image
                source={showPassword ? IMAGES.eyeOpen : IMAGES.eyeClosed}
                style={styles.eyeIconImage}
                resizeMode="contain"
                width={24}
                height={24}
              />
            </TouchableOpacity>
          </View>
        </View>
        {/* 忘记密码弹窗 */}
        <ForgotPasswordModal
          visible={forgotPasswordModalVisible}
          email={email}
          onClose={handleCloseForgotPasswordModal}
        />
      </View>
      {/* 登录按钮和忘记密码链接 - 底部容器 */}
      <View style={styles.bottomContainer}>
          {/* 忘记密码链接 */}
          <TouchableOpacity
            style={styles.forgotPasswordContainer}
            onPress={handleForgotPassword}
          >
            <Text style={styles.forgotPasswordText}>{t('auth.forgotPassword')}</Text>
          </TouchableOpacity>

          {/* 登录按钮 */}
          <TouchableOpacity
            style={styles.signInButton}
            onPress={handleSignIn}
            disabled={isLoading}
          >
            {isLoading ? (
              <ActivityIndicator color="#FFFFFF" size="small" />
            ) : (
              <Text style={styles.signInButtonText}>{t('auth.signIn')}</Text>
            )}
          </TouchableOpacity>
        </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: '#000',
  },
  container: {
    height: Dimensions.get('window').height - 90,
    backgroundColor: '#000',
    position: 'relative',
  },
  backgroundImage: {
    position: 'absolute',
    top: '50%',
    transform: [{ translateY: -150 }],
    right: -30,
    width: 300,
    height: 300,
    zIndex: 0,
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    marginTop: 20,
    marginBottom: 30,
    zIndex: 1,
  },
  titleIndicator: {
    width: 4,
    height: 24,
    backgroundColor: '#FF0066',
    marginRight: 10,
  },
  title: {
    color: '#FFFFFF',
    fontSize: 24,
    fontFamily: FONTS.bebasNeue.regular,
    letterSpacing: 1,
  },
  inputSection: {
    paddingHorizontal: 20,
    marginBottom: 20,
    zIndex: 1,
  },
  inputLabel: {
    color: '#FFFFFF',
    fontSize: 16,
    fontFamily: FONTS.bebasNeue.regular,
    marginBottom: 10,
    letterSpacing: 1,
  },
  input: {
    borderBottomWidth: 1,
    borderBottomColor: '#FFFFFF',
    color: '#FFFFFF',
    fontSize: 16,
    fontFamily: FONTS.pretendard.light,
    paddingVertical: 10,
    paddingRight: 40, // 为眼睛图标留出空间
  },
  passwordContainer: {
    position: 'relative',
  },
  eyeIcon: {
    position: 'absolute',
    right: 0,
    bottom: 10,
    padding: 5,
    width: 34, // 给图标足够的空间
    height: 34, // 给图标足够的空间
    justifyContent: 'center',
    alignItems: 'center',
  },
  eyeIconImage: {
    tintColor: '#FFFFFF',
  },
  forgotPasswordContainer: {
    alignItems: 'center',
    marginBottom: 20,
    zIndex: 1,
    width: '100%',
  },
  forgotPasswordText: {
    color: '#FF0066',
    fontSize: 14,
    fontFamily: FONTS.bebasNeue.regular,
    letterSpacing: 1,
    textDecorationLine: 'underline',
  },
  bottomContainer: {
    justifyContent: 'flex-end',
    alignItems: 'center',
    paddingBottom: 40,
    zIndex: 1,
    width: '100%',
  },
  signInButton: {
    backgroundColor: '#FF0066',
    paddingVertical: 15,
    borderRadius: 5,
    alignItems: 'center',
    justifyContent: 'center',
    width: '90%',
    zIndex: 1,
  },
  signInButtonText: {
    color: '#FFFFFF',
    fontSize: 18,
    fontFamily: FONTS.bebasNeue.regular,
    letterSpacing: 1,
  },
});

export default LoginScreen;
