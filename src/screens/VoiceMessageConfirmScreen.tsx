import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Image,
  TouchableOpacity,
  StatusBar,
  ActivityIndicator,
} from 'react-native';
import { FONTS } from '../assets/fonts';
import { get } from '../services/api/apiService';
import { useNavigation, useRoute, RouteProp } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { useTranslation } from 'react-i18next';

// 定义导航参数类型
type RootStackParamList = {
  Home: undefined;
  Inbox: {
    activeTab?: string;
  };
  MainTabs: {
    screen: string;
    params?: any;
  };
  VoiceMessageConfirm: {
    id?: string;
    source?: 'voiceMessage' | 'alarm'; // 添加来源页面标识
  };
  Alarm: {
    refresh?: boolean;
    timestamp?: number;
    id?: number;
    name?: string;
    type?: string;
    image?: any;
    nm?: string;
  };
};

type VoiceMessageConfirmScreenNavigationProp = StackNavigationProp<RootStackParamList, 'VoiceMessageConfirm'>;
type VoiceMessageConfirmScreenRouteProp = RouteProp<RootStackParamList, 'VoiceMessageConfirm'>;

const VoiceMessageConfirmScreen = () => {
  const navigation = useNavigation<VoiceMessageConfirmScreenNavigationProp>();
  const route = useRoute<VoiceMessageConfirmScreenRouteProp>();
  const { t } = useTranslation();

  // 获取从路由参数中传递的 id 和来源页面标识
  const { id, source = 'voiceMessage' } = route.params || {};

  // 语音消息数据状态
  const [voiceMessageData, setVoiceMessageData] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // 倒计时状态
  const [countdown, setCountdown] = useState({
    hours: 0,
    minutes: 0,
    seconds: 0,
  });

  // 处理底部按钮点击
  const handleBottomButtonPress = () => {
    if (source === 'alarm') {
      console.log('Go to Alarm list pressed, id:', id);
      console.log('Alarm data:', voiceMessageData);
      // 导航到 Alarm 页面
      navigation.navigate('MainTabs', {
        screen: 'Inbox',
        params: { activeTab: 'alarm' },
      });
    } else {
      console.log('Go to Inbox pressed with Voice Message tab, id:', id);
      console.log('Voice message data:', voiceMessageData);
      // 导航到底部标签栏中的 Inbox 页面并选中 Voice Message 选项卡
      navigation.navigate('MainTabs', {
        screen: 'Inbox',
        params: { activeTab: 'message' },
      });
    }
  };

  // 获取消息详情
  useEffect(() => {
    const fetchMessageDetail = async () => {
      if (!id) {
        console.log('No message ID provided');
        return;
      }

      try {
        setIsLoading(true);
        setError(null);

        let response;

        // 根据来源页面调用不同的接口
        if (source === 'alarm') {
          // 如果是从闹钟页面跳转过来的，调用闹钟接口
          console.log('Fetching alarm detail for ID:', id);
          response = await get(`/morningSchedule/remaining/${id}`);
          console.log('Alarm detail response:', JSON.stringify(response.data));
        } else {
          // 如果是从语音消息页面跳转过来的，调用语音消息接口
          console.log('Fetching voice message detail for ID:', id);
          response = await get(`/voiceMessage/${id}`);
          console.log('Voice message detail response:', response.data);
        }

        if (response.data && (response.data.code === '200' || response.data.code === 200)) {
          if (source === 'alarm') {
            // 对于闹钟接口，data 直接是倒计时字符串，如 "00:58:31"
            console.log('Setting alarm countdown directly from data:', response.data.data);

            // 创建一个简单的 voiceMessageData 对象，包含 generationTime
            setVoiceMessageData({
              generationTime: response.data.data,
              messageText: t('voiceMessage.celebVoiceAlarmGenerating'),
            });
          } else {
            // 对于语音消息接口，data 是一个对象
            setVoiceMessageData(response.data.data);
          }
        } else {
          setError(response.data?.msg || t('voiceMessage.failedToFetchMessageDetails'));
          console.error('Failed to fetch message details:', response.data);
        }
      } catch (err) {
        console.error('Error fetching message details:', err);
        setError(t('voiceMessage.errorFetchingMessageDetails'));
      } finally {
        setIsLoading(false);
      }
    };

    fetchMessageDetail();
  }, [id, source]);

  // 根据 generationTime 设置倒计时
  useEffect(() => {
    if (voiceMessageData && voiceMessageData.generationTime) {
      try {
        // 解析 generationTime 格式，例如 "23:35:24"
        const timeParts = voiceMessageData.generationTime.split(':');
        if (timeParts.length === 3) {
          const hours = parseInt(timeParts[0], 10);
          const minutes = parseInt(timeParts[1], 10);
          const seconds = parseInt(timeParts[2], 10);

          console.log(`Setting countdown from generationTime: ${hours}:${minutes}:${seconds}`);

          setCountdown({
            hours,
            minutes,
            seconds,
          });
        }
      } catch (err) {
        console.error('Error parsing generationTime:', err);
      }
    }
  }, [voiceMessageData]);

  // 倒计时效果
  useEffect(() => {
    // 只有当倒计时间大于 0 时才开始倒计时
    if (countdown.hours <= 0 && countdown.minutes <= 0 && countdown.seconds <= 0) {
      return;
    }

    const timer = setInterval(() => {
      setCountdown(prevState => {
        const newSeconds = prevState.seconds - 1;
        const newMinutes = newSeconds < 0 ? prevState.minutes - 1 : prevState.minutes;
        const newHours = newMinutes < 0 ? prevState.hours - 1 : prevState.hours;

        return {
          hours: newHours < 0 ? 0 : newHours,
          minutes: newMinutes < 0 ? 59 : newMinutes,
          seconds: newSeconds < 0 ? 59 : newSeconds,
        };
      });
    }, 1000);

    return () => clearInterval(timer);
  }, [countdown.hours, countdown.minutes, countdown.seconds]);

  // 格式化时间为两位数
  const formatTime = (value: number) => {
    return value < 10 ? `0${value}` : `${value}`;
  };

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="transparent" translucent={true} />

      {isLoading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#6A4BFF" />
          <Text style={styles.loadingText}>{t('voiceMessage.loadingVoiceMessage')}</Text>
        </View>
      ) : error ? (
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>{error}</Text>
        </View>
      ) : (
        <View style={styles.content}>
        {/* 公文包图标 */}
        <View style={styles.iconContainer}>
          <Image
            source={require('../assets/images/home/<USER>')}
            style={styles.briefcaseIcon}
          />
        </View>

        {/* 消息文本 */}
        <Text style={styles.messageText}>
          {voiceMessageData?.messageText || (source === 'alarm' ? t('voiceMessage.celebVoiceAlarmGenerating') : t('voiceMessage.createVoiceMessage'))}
        </Text>
        <Text style={styles.subMessageText}>
          {source === 'alarm'
            ? t('voiceMessage.alarmReadySoon')
            : t('voiceMessage.voiceMessagesInInbox')
          }
        </Text>

        {/* 倒计时部分 */}
        <View style={styles.countdownContainer}>
          <Text style={styles.afterText}>{t('voiceMessage.after')}</Text>
          <Text style={styles.countdownText}>
            {formatTime(countdown.hours)}:{formatTime(countdown.minutes)}:{formatTime(countdown.seconds)}
          </Text>
        </View>
      </View>
      )}

      {/* 底部按钮 */}
      <TouchableOpacity
        style={styles.goToInboxButton}
        onPress={handleBottomButtonPress}
      >
        <Text style={styles.goToInboxText}>
          {source === 'alarm' ? t('voiceMessage.goToAlarmList') : t('voiceMessage.goToInbox')}
        </Text>
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    color: '#FFFFFF',
    fontFamily: FONTS.pretendard.light,
    fontSize: 16,
    marginTop: 10,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  errorText: {
    color: '#FF004A',
    fontFamily: FONTS.pretendard.light,
    fontSize: 16,
    textAlign: 'center',
  },
  container: {
    flex: 1,
    backgroundColor: '#000000',
    justifyContent: 'space-between',
    paddingBottom: 30,
  },
  content: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  iconContainer: {
    marginBottom: 30,
  },
  briefcaseIcon: {
    width: 80,
    height: 80,
    tintColor: '#FFFFFF',
  },
  messageText: {
    color: '#FFFFFF',
    fontFamily: FONTS.pretendard.light,
    fontSize: 20,
    textAlign: 'center',
    marginBottom: 10,
  },
  subMessageText: {
    color: '#FFFFFF',
    fontFamily: FONTS.pretendard.light,
    fontSize: 18,
    textAlign: 'center',
    marginBottom: 20,
  },
  countdownContainer: {
    alignItems: 'center',
  },
  afterText: {
    color: '#FF004A',
    fontFamily: FONTS.pretendard.light,
    fontSize: 18,
    marginBottom: 5,
  },
  countdownText: {
    color: '#FF004A',
    fontFamily: FONTS.bebasNeue.regular,
    fontSize: 40,
    letterSpacing: 2,
  },
  goToInboxButton: {
    backgroundColor: '#6A4BFF',
    paddingVertical: 15,
    marginHorizontal: 20,
    borderRadius: 5,
    alignItems: 'center',
    justifyContent: 'center',
  },
  goToInboxText: {
    color: '#FFFFFF',
    fontFamily: FONTS.pretendard.light,
    fontSize: 18,
  },
});

export default VoiceMessageConfirmScreen;
