import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Image,
  ImageBackground,
  Dimensions,
  ActivityIndicator,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';

// 导入图片资源
import { IMAGES } from '../assets/images';
import { Header, SimpleFullTextModal } from '../components';
import { post } from '../services/api/apiService';
import { useTranslation } from 'react-i18next';

// 定义导航类型
type AuthStackParamList = {
  Login: undefined;
  Agreement: undefined;
  SignUpForm: undefined;
  SignUpProfile: undefined;
};

type AgreementScreenNavigationProp = StackNavigationProp<AuthStackParamList, 'Agreement'>;

// 默认协议内容
const defaultContent = `Loading agreement content...`;

const AgreementScreen = () => {
  const navigation = useNavigation<AgreementScreenNavigationProp>();
  const { t } = useTranslation();

  // 协议勾选状态
  const [termsAgreed, setTermsAgreed] = useState(false);
  const [privacyAgreed, setPrivacyAgreed] = useState(false);

  // 模态窗口状态
  const [termsModalVisible, setTermsModalVisible] = useState(false);
  const [privacyModalVisible, setPrivacyModalVisible] = useState(false);

  // 协议内容
  const [termsContent, setTermsContent] = useState(defaultContent);
  const [privacyContent, setPrivacyContent] = useState(defaultContent);

  // 加载状态
  const [isLoadingTerms, setIsLoadingTerms] = useState(false);
  const [isLoadingPrivacy, setIsLoadingPrivacy] = useState(false);

  // 获取协议内容
  const fetchTermsContent = async () => {
    setIsLoadingTerms(true);
    try {
      console.log('Fetching terms content...');
      const response = await post('/appSettting/info', { code: 'agreement' });
      console.log('Terms API response:', JSON.stringify(response));

      if (response && response.data) {
        console.log('Terms data structure:', JSON.stringify(response.data));

        // 处理嵌套的数据结构
        if (response.data.code === '200' && response.data.data && response.data.data.content) {
          console.log('Setting terms content from data.data.content');
          setTermsContent(response.data.data.content);
        } else if (response.data.code === 200 && response.data.data && response.data.data.content) {
          console.log('Setting terms content from data.data.content (numeric code)');
          setTermsContent(response.data.data.content);
        } else if (response.data.content) {
          console.log('Setting terms content from data.content');
          setTermsContent(response.data.content);
        } else {
          console.log('Invalid terms response format, using default content');
          setTermsContent(defaultContent);
        }
      } else {
        console.log('No data in terms response, using default content');
        setTermsContent(defaultContent);
      }
    } catch (error) {
      console.error('Failed to fetch terms content:', error);
      setTermsContent(defaultContent);
    } finally {
      setIsLoadingTerms(false);
    }
  };

  const fetchPrivacyContent = async () => {
    setIsLoadingPrivacy(true);
    try {
      console.log('Fetching privacy content...');
      const response = await post('/appSettting/info', { code: 'product_policy' });
      console.log('Privacy API response:', JSON.stringify(response));

      if (response && response.data) {
        console.log('Privacy data structure:', JSON.stringify(response.data));

        // 处理嵌套的数据结构
        if (response.data.code === '200' && response.data.data && response.data.data.content) {
          console.log('Setting privacy content from data.data.content');
          setPrivacyContent(response.data.data.content);
        } else if (response.data.code === 200 && response.data.data && response.data.data.content) {
          console.log('Setting privacy content from data.data.content (numeric code)');
          setPrivacyContent(response.data.data.content);
        } else if (response.data.content) {
          console.log('Setting privacy content from data.content');
          setPrivacyContent(response.data.content);
        } else {
          console.log('Invalid privacy response format, using default content');
          setPrivacyContent(defaultContent);
        }
      } else {
        console.log('No data in privacy response, using default content');
        setPrivacyContent(defaultContent);
      }
    } catch (error) {
      console.error('Failed to fetch privacy content:', error);
      setPrivacyContent(defaultContent);
    } finally {
      setIsLoadingPrivacy(false);
    }
  };

  // 处理查看完整协议
  const handleViewTerms = async () => {
    console.log('View terms of use button clicked');
    try {
      // 无论如何都重新获取内容
      console.log('Fetching terms content...');
      await fetchTermsContent();

      console.log('Setting terms modal visible to true');
      setTermsModalVisible(true);

      // 添加延时检查
      setTimeout(() => {
        console.log('Terms modal visible state after timeout:', termsModalVisible);
      }, 500);
    } catch (error) {
      console.error('Error in handleViewTerms:', error);
    }
  };

  const handleViewPrivacy = async () => {
    console.log('View privacy agreement button clicked');
    try {
      // 无论如何都重新获取内容
      console.log('Fetching privacy content...');
      await fetchPrivacyContent();

      console.log('Setting privacy modal visible to true');
      setPrivacyModalVisible(true);

      // 添加延时检查
      setTimeout(() => {
        console.log('Privacy modal visible state after timeout:', privacyModalVisible);
      }, 500);
    } catch (error) {
      console.error('Error in handleViewPrivacy:', error);
    }
  };

  // 处理下一步
  const handleNext = () => {
    if (termsAgreed && privacyAgreed) {
      console.log('Navigate to sign up form');
      navigation.navigate('SignUpForm');
    } else {
      console.log('Please agree to all terms');
      // 这里可以添加提示用户同意所有协议的逻辑
    }
  };

  // 返回登录页面的逻辑已由Header组件处理

  return (
    <View style={styles.container}>
      {/* 顶部导航 */}
      <Header showLogo={true} />

      {/* 使用条款协议 */}
      <View style={styles.agreementWrapper}>
        <View style={styles.agreementContainer}>
          <ImageBackground
            source={IMAGES.agreement1}
            style={styles.agreementSection}
            imageStyle={styles.agreementBackground}
          >
          <Text style={styles.agreementTitle}>{t('agreement.termsOfUse')}</Text>

          <TouchableOpacity
            style={styles.checkboxRow}
            onPress={() => setTermsAgreed(!termsAgreed)}
            activeOpacity={0.7}
          >
            <Image
              source={termsAgreed ? IMAGES.checkboxOn : IMAGES.checkboxOff}
              style={styles.checkboxImage}
            />
            <Text style={styles.agreementText}>{t('agreement.agree')}</Text>
          </TouchableOpacity>
          </ImageBackground>
        </View>

        <TouchableOpacity
          style={styles.viewTextButton}
          onPress={handleViewTerms}
          activeOpacity={0.8}
        >
          <View style={styles.iconContainer}>
            <Image source={IMAGES.aivgenIcon} style={styles.buttonIcon} />
          </View>
          <Text style={styles.viewTextButtonText}>{t('agreement.viewFullText')}</Text>
        </TouchableOpacity>
      </View>

      {/* 个人信息收集协议 */}
      <View style={styles.agreementWrapper}>
        <View style={styles.agreementContainer}>
          <ImageBackground
            source={IMAGES.agreement2}
            style={[styles.agreementSection, styles.privacySection]}
            imageStyle={styles.agreementBackground}
          >
          <View style={styles.agreementHeader}>
           <View style={styles.agreementHeaders}>
           <Text style={styles.privacyTitle}>{t('agreement.personalInfoCollection')}</Text>
           <Text style={styles.privacyTitle}>{t('agreement.andUsageAgreement')}</Text>
           </View>
          </View>

          <TouchableOpacity
            style={[styles.checkboxRow, styles.secondCheckboxRow]}
            onPress={() => setPrivacyAgreed(!privacyAgreed)}
            activeOpacity={0.7}
          >
            <Image
              source={privacyAgreed ? IMAGES.checkboxOn : IMAGES.checkboxOff}
              style={styles.checkboxImage}
            />
            <Text style={styles.agreementText}>{t('agreement.agree')}</Text>
          </TouchableOpacity>
          </ImageBackground>
        </View>

        <TouchableOpacity
          style={[styles.viewTextButton, styles.blueButton]}
          onPress={handleViewPrivacy}
          activeOpacity={0.8}
        >
          <View style={styles.iconContainer}>
            <Image source={IMAGES.aivgenIcon} style={[styles.buttonIcon, styles.blueIcon]} />
          </View>
          <Text style={styles.viewTextButtonText}>{t('agreement.viewFullText')}</Text>
        </TouchableOpacity>
      </View>

      {/* 下一步按钮 */}
      <View style={styles.bottomButtonContainer}>
        <TouchableOpacity
          style={[
            styles.nextButton,
            !(termsAgreed && privacyAgreed) && styles.nextButtonDisabled,
          ]}
          onPress={handleNext}
          disabled={!(termsAgreed && privacyAgreed)}
        >
          <Text style={styles.nextButtonText}>{t('common.next')}</Text>
          <View style={styles.arrowContainer}>
            <Image source={IMAGES.arrowRight} style={styles.arrowIcon} />
          </View>
        </TouchableOpacity>
      </View>

      {/* 使用条款全文模态窗口 */}
      <SimpleFullTextModal
        visible={termsModalVisible}
        onClose={() => setTermsModalVisible(false)}
        title={t('agreement.termsOfUse')}
        content={isLoadingTerms ? t('common.loading') : termsContent}
        onAgree={() => setTermsAgreed(!termsAgreed)}
        isAgreed={termsAgreed}
      />

      {/* 隐私协议全文模态窗口 */}
      <SimpleFullTextModal
        visible={privacyModalVisible}
        onClose={() => setPrivacyModalVisible(false)}
        title={`${t('agreement.personalInfoCollection')} ${t('agreement.andUsageAgreement')}`}
        content={isLoadingPrivacy ? t('common.loading') : privacyContent}
        onAgree={() => setPrivacyAgreed(!privacyAgreed)}
        isAgreed={privacyAgreed}
      />
    </View>
  );
};

// 使用Dimensions获取屏幕尺寸
const { width: screenWidth } = Dimensions.get('window');

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000000',
  },

  agreementWrapper: {
    width: '100%',
    marginBottom: 20,
    position: 'relative',
    paddingTop: 60,
    alignItems: 'center',
  },

  agreementContainer: {
    width: screenWidth,
  },
  agreementSection: {
    padding: 20,
    paddingTop: 30,
    paddingBottom: 20,
    marginRight: 20,
    position: 'relative',
    overflow: 'hidden',
    height: 190,
    resizeMode: 'contain',
  },
  agreementBackground: {
  },
  privacySection: {
    marginRight: 0,
    marginLeft: 20,
  },
  agreementHeader: {
    alignItems: 'center',
    marginBottom: 20,
  },
  agreementHeaders: {
    justifyContent: 'center',
  },
  agreementTitle: {
    color: '#FFFFFF',
    fontSize: 22,
    fontFamily: 'BebasNeue-Regular',
    textAlign: 'center',
    letterSpacing: 1,
    position: 'absolute',
    width: '100%',
    right:0,
    top: 25,
    left: 30,
  },
  secondCheckboxRow: {
    top: 80,
    left: 0,
  },
  privacyTitle: {
    color: '#FFFFFF',
    fontSize: 16,
    fontFamily: 'BebasNeue-Regular',
    letterSpacing: 1,
    marginBottom: 5,
    top: 0,
    left: -20,
  },
  checkboxRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 20,
    position: 'absolute',
    top: 70,
    left: 20,
    width: '100%',
    paddingVertical: 10, // 增加垂直方向的点击区域
  },
  checkboxImage: {
    width: 18,
    height: 18,
    resizeMode: 'contain',
    marginRight: 8,
  },
  agreementText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontFamily: 'Pretendard-Light',
    marginLeft: 5,
  },
  viewTextButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#DDDDDD',
    borderRadius: 30,
    paddingVertical: 10,
    paddingHorizontal: 20,
    width: 400 * screenWidth / 750,
    alignSelf: 'center',
    marginTop: -20,
  },
  blueButton: {
    // 如果需要为第二个按钮添加特殊样式
  },
  iconContainer: {
    width: 20,
    height: 20,
    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
  },
  buttonIcon: {
    width: 20,
    height: 20,
    tintColor: '#FF004A',
  },
  blueIcon: {
    width: 20,
    height: 20,
    tintColor: '#1E5EFA',
  },
  viewTextButtonText: {
    color: '#000',
    fontSize: 14,
    fontFamily: 'Pretendard-Light',
  },

  bottomButtonContainer: {
    position: 'absolute',
    bottom: 30,
    width: '100%',
    alignItems: 'center',
  },
  nextButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    color: '#FF0066',
    borderRadius: 5,
    paddingVertical: 8,
    paddingHorizontal: 15,
    width: 100,
  },
  nextButtonDisabled: {
    color: '#FF0066'
  },

  nextButtonText: {
   color: '#FF0066',
    fontSize: 16,
    fontFamily: 'BebasNeue-Regular',
    marginRight: 5,
    letterSpacing: 1,
  },
  arrowContainer: {
    width: 16,
    height: 16,
    justifyContent: 'center',
    alignItems: 'center',

  },
  arrowIcon: {
    width: 12,
    height: 12,
    tintColor: '#FF0066',
  },
});

export default AgreementScreen;
