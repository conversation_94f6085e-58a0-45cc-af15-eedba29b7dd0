import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Image,
  TouchableOpacity,
  ScrollView,
  StatusBar,
  ActivityIndicator,
} from 'react-native';
import { FONTS } from '../assets/fonts';
import { IMAGES } from '../assets/images';
import { Header, SortingBottomSheet } from '../components';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { useTranslation } from 'react-i18next';

// 导入API服务
import {
  getTrendTypes,
  getSubscriptionList,
  getSortingOptions,
} from '../services/api/homeService';

// 定义导航参数类型
type RootStackParamList = {
  Home: undefined;
  Celebrity: undefined;
  Subscription: undefined;
  CelebrityDetail: {
    id: number;
    name: string;
    type: string;
    image: any;
    nm?: string; // 添加nm字段，可选
  };
};

type SubscriptionScreenNavigationProp = StackNavigationProp<RootStackParamList, 'Subscription'>;

// 默认分类
const defaultCategories = [
  { id: 'total', name: 'TOTAL', icon: IMAGES.categoryIcons.all, nm: 'total' },
  { id: 'musician', name: 'MUSICIAN', icon: IMAGES.categoryIcons.musician, nm: 'musician' },
  { id: 'actor', name: 'ACTOR', icon: IMAGES.categoryIcons.actor, nm: 'actor' },
  { id: 'broadcaster', name: 'BROADCASTER', icon: IMAGES.categoryIcons.broadcaster, nm: 'broadcaster' },
  { id: 'sports', name: 'SPORTS', icon: IMAGES.categoryIcons.sports, nm: 'sports' },
  { id: 'director', name: 'DIRECTOR', icon: IMAGES.categoryIcons.director, nm: 'director' },
  { id: 'producer', name: 'PRODUCER', icon: IMAGES.categoryIcons.producer, nm: 'producer' },
  { id: 'dancer', name: 'DANCER', icon: IMAGES.categoryIcons.dancer, nm: 'dancer' },
];

// 默认排序选项
const defaultSortingOptions = [
  { id: 'category', label: 'CATEGORY', value: 'category_id' },
  { id: 'name', label: 'NAME', value: 'name asc' },
  { id: 'favorite', label: 'FAVORITE', value: 'collection_number desc' },
  { id: 'subscription', label: 'SUBSCRIPTION', value: 'numberofsubscriptions asc' },
  { id: 'voice_message', label: 'VOICE MESSAGE', value: 'generate_quantity asc' },
];

const SubscriptionScreen = () => {
  const navigation = useNavigation<SubscriptionScreenNavigationProp>();
  const { t } = useTranslation();
  const [selectedCategory, setSelectedCategory] = useState('total');
  const [sortingVisible, setSortingVisible] = useState(false);
  const [sortBy, setSortBy] = useState('');

  // 订阅列表数据
  const [subscriptionListData, setSubscriptionListData] = useState<any[]>([]);
  // 订阅列表加载状态
  const [isLoading, setIsLoading] = useState(false);

  // 分类数据
  const [categories, setCategories] = useState<Array<{
    id: string;
    name: string;
    icon: any;
    nm: string; // 添加nm字段
    originalData?: any;
  }>>(defaultCategories);
  // 分类加载状态
  const [isCategoriesLoading, setIsCategoriesLoading] = useState(false);

  // 排序选项数据
  const [sortingOptions, setSortingOptions] = useState<Array<{
    id: string;
    label: string;
    value: string;
  }>>(defaultSortingOptions);
  // 排序选项加载状态
  const [isSortingOptionsLoading, setIsSortingOptionsLoading] = useState(false);

  // 获取趋势类型数据
  useEffect(() => {
    const fetchTrendTypes = async () => {
      try {
        setIsCategoriesLoading(true);
        // 获取趋势类型数据
        const data = await getTrendTypes();

        if (data && data.length > 0) {
          // 处理获取到的数据
          const allCategories = data.map((item) => ({
            id: item.category.toLowerCase(),
            name: item.categoryExplain || item.category || 'Unknown',
            // 使用reserve3字段作为图标URL，如果没有则使用默认图标
            icon: item.reserve3 ? { uri: item.reserve3 } :
                  item.category.toLowerCase() === 'total' ? IMAGES.categoryIcons.all :
                  IMAGES.categoryIcons[item.category.toLowerCase() as keyof typeof IMAGES.categoryIcons] ||
                  IMAGES.categoryIcons.all,
            // 使用nm字段作为分类编码
            nm: item.nm || item.category,
            // 保存原始数据以便后续使用
            originalData: item,
          }));

          // 确保有TOTAL选项，如果没有则添加
          if (!allCategories.some(cat => cat.id === 'total')) {
            allCategories.unshift({
              id: 'total',
              name: 'TOTAL',
              icon: IMAGES.categoryIcons.all,
              nm: 'total',
              originalData: null,
            });
          }

          setCategories(allCategories);
        } else {
          // 如果没有数据，使用默认分类
          setCategories(defaultCategories);
        }
      } catch (error) {
        console.error('Failed to fetch trend types:', error);
        // 出错时使用默认分类
        setCategories(defaultCategories);
      } finally {
        setIsCategoriesLoading(false);
      }
    };

    fetchTrendTypes();
  }, []);

  // 获取排序选项数据
  useEffect(() => {
    const fetchSortingOptions = async () => {
      try {
        setIsSortingOptionsLoading(true);
        // 获取排序选项数据
        const data = await getSortingOptions();
        console.log('Sorting options data:', JSON.stringify(data));

        if (data && data.length > 0) {
          // 如果成功获取数据，更新排序选项数据
          setSortingOptions(data.map(item => ({
            id: String(item.id),
            label: item.name,
            value: item.code,
          })));
        } else {
          // 如果没有数据，使用默认排序选项
          setSortingOptions(defaultSortingOptions);
        }
      } catch (error) {
        console.error('Failed to fetch sorting options:', error);
        // 出错时使用默认排序选项
        setSortingOptions(defaultSortingOptions);
      } finally {
        setIsSortingOptionsLoading(false);
      }
    };

    fetchSortingOptions();
  }, []);

  // 获取订阅列表数据
  const fetchSubscriptionList = async (category = selectedCategory, sortOption = sortBy) => {
    try {
      setIsLoading(true);
      console.log('Fetching subscription list with category:', category, 'and sort:', sortOption);

      // 获取订阅列表数据，传递分类和排序参数
      const response = await getSubscriptionList(category, sortOption);
      console.log('Subscription list response=====:', response);

      if (response && response.length > 0) {
        // 如果成功获取数据，更新订阅列表数据
        setSubscriptionListData(response.map(item => ({
          id: item.id,
          type: item.category,
          name: item.name,
          image: item.headImage ? { uri: item.headImage } : IMAGES.defaultAvatar,
          categoryExplain: item.categoryExplain || '',
          headImage: item.headImage || '',
          category: item.category || 'UNKNOWN',
          nm: item.nm || '',
        })));
      } else {
        // 如果没有数据，设置空数组
        setSubscriptionListData([]);
      }
    } catch (error) {
      console.error('Failed to fetch subscription list:', error);
      // 出错时设置空数组
      setSubscriptionListData([]);
    } finally {
      setIsLoading(false);
    }
  };

  // 初始加载订阅列表数据
  useEffect(() => {
    fetchSubscriptionList();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // 当分类或排序选项变化时，重新获取订阅列表数据
  useEffect(() => {
    // 找到选中分类的nm
    const selectedCategoryObj = categories.find(cat => cat.id === selectedCategory);
    const categoryNm = selectedCategoryObj ? selectedCategoryObj.nm : selectedCategory;

    // 如果选中的是'total'，则不传递categoryId参数
    if (selectedCategory === 'total' || categoryNm === 'total') {
      fetchSubscriptionList('', sortBy);
    } else {
      // 否则使用nm字段获取数据
      fetchSubscriptionList(categoryNm, sortBy);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selectedCategory, sortBy, categories]);

  // 根据选择的分类过滤数据
  const filteredData = selectedCategory === 'total'
    ? subscriptionListData
    : subscriptionListData.filter(item =>
        item.type.toLowerCase() === selectedCategory.toLowerCase()
      );

  // 处理排序选项选择
  const handleSortSelect = (optionId: string) => {
    // 找到选中的排序选项
    const selectedOption = sortingOptions.find(option => option.id === optionId);
    if (selectedOption) {
      // 设置排序值（使用value字段）
      setSortBy(selectedOption.value);
      console.log(`Sorting by: ${selectedOption.label} (${selectedOption.value})`);
    }
  };

  // 打开排序底部弹窗
  const openSortingSheet = () => {
    setSortingVisible(true);
  };

  // 处理搜索按钮点击
  const handleSearchPress = () => {
    console.log('Search pressed in SubscriptionScreen');
    // 这里添加搜索功能的逻辑
  };

  // 处理菜单按钮点击
  const handleMenuPress = () => {
    console.log('Menu pressed in SubscriptionScreen');
    // 导航到菜单页面
    navigation.navigate('Menu' as never);
  };

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="transparent" translucent={true} />

      {/* 使用全局 Header 组件 */}
      <Header
        title={t('navigation.subscription')}
        rightComponentType="menuAndSearch"
        onSearchPress={handleSearchPress}
        onMenuPress={handleMenuPress}
        showBack={false}
      />

      {/* 音乐节广告 */}
      <View style={styles.festivalBanner}>
        <Image
          source={IMAGES.bannerIcon}
          style={styles.festivalImage}
          resizeMode="cover"
        />
      </View>

      {/* 分类选项卡 - 可水平滚动 */}
      <View style={styles.categoriesContainer}>
        {isCategoriesLoading ? (
          <View style={styles.categoriesLoadingContainer}>
            <ActivityIndicator size="small" color="#00FF66" />
          </View>
        ) : (
          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.categoriesScrollContent}
          >
            {categories.map(category => (
              <TouchableOpacity
                key={category.id}
                style={[
                  styles.categoryTab,
                  selectedCategory === category.id && styles.selectedCategoryTab,
                ]}
                onPress={() => {
                  setSelectedCategory(category.id);
                  // 如果选中的是'total'，则不传递categoryId参数
                  if (category.id === 'total' || category.nm === 'total') {
                    fetchSubscriptionList('', sortBy);
                  } else {
                    // 否则使用nm字段获取数据
                    fetchSubscriptionList(category.nm, sortBy);
                  }
                }}
              >
                <Image
                  source={category.icon}
                  style={[
                    styles.categoryIcon,
                    selectedCategory === category.id && styles.selectedCategoryIcon,
                  ]}
                  resizeMode="contain"
                />
                <Text
                  style={[
                    styles.categoryText,
                    selectedCategory === category.id && styles.selectedCategoryText,
                  ]}
                >
                  {category.name}
                </Text>
              </TouchableOpacity>
            ))}
          </ScrollView>
        )}
      </View>

      {/* 分类图标 */}
      <View style={styles.categoryHeaderContainer}>
        <View style={styles.categoryHeaderSpacer} />
        <TouchableOpacity onPress={openSortingSheet}>
          <Image
            source={IMAGES.categoryIcons.all}
            style={styles.categoryHeaderIcon}
          />
        </TouchableOpacity>
      </View>

      {/* 排序底部弹窗 */}
      <SortingBottomSheet
        visible={sortingVisible}
        onClose={() => setSortingVisible(false)}
        onSelect={handleSortSelect}
        options={sortingOptions}
        isLoading={isSortingOptionsLoading}
      />

      {/* 订阅列表 */}
      <ScrollView style={styles.scrollView}>
        {isLoading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color="#00FF66" />
            <Text style={styles.loadingText}>{t('common.loading')}</Text>
          </View>
        ) : filteredData.length > 0 ? (
          filteredData.map(item => (
            <TouchableOpacity
              key={item.id}
              style={styles.celebrityItem}
              onPress={() => navigation.navigate('CelebrityDetail', {
                id: item.id,
                name: item.name,
                type: item.type,
                image: item.image,
                nm: item.nm || '',
              })}
            >
              <Image source={item.image} style={styles.celebrityImage} />
              <View style={styles.celebrityInfo}>
                <Text style={styles.celebrityType}>{item.type}</Text>
                <Text style={styles.celebrityName}>{item.name}</Text>
              </View>
            </TouchableOpacity>
          ))
        ) : (
          <View style={styles.noDataContainer}>
            <Text style={styles.noDataText}>{t('common.noData')}</Text>
          </View>
        )}

        {/* 底部间距 */}
        <View style={styles.bottomPadding} />
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000000',
  },
  headerRight: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  searchButton: {
    marginRight: 15,
  },
  menuButton: {},
  icon: {
    width: 24,
    height: 24,
    tintColor: '#FFFFFF',
  },
  festivalBanner: {
    width: '100%',
    height: 180,
    position: 'relative',
  },
  festivalImage: {
    width: '100%',
    height: '100%',
  },
  categoriesContainer: {
    borderBottomWidth: 1,
    borderBottomColor: '#333333',
  },
  categoryHeaderContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 15,
    paddingVertical: 10,
    justifyContent: 'space-between',
  },
  categoryHeaderSpacer: {
    flex: 1,
  },
  categoryHeaderIcon: {
    width: 24,
    height: 24,
  },
  categoriesScrollContent: {
    paddingVertical: 15,
    paddingHorizontal: 10,
  },
  categoryTab: {
    paddingHorizontal: 10,
    paddingVertical: 5,
    alignItems: 'center',
    flexDirection: 'row',
    marginHorizontal: 5,
  },
  selectedCategoryTab: {
    borderBottomWidth: 2,
    borderBottomColor: '#FF6B4E',
  },
  categoryIcon: {
    height: 20,
    width: 20,
    marginRight: 5,
    tintColor: '#999999',
  },
  selectedCategoryIcon: {
    tintColor: '#FFFFFF',
  },
  categoryText: {
    color: '#999999',
    fontFamily: FONTS.bebasNeue.regular,
    fontSize: 16,
    letterSpacing: 1,
  },
  selectedCategoryText: {
    color: '#FFFFFF',
  },
  scrollView: {
    flex: 1,
  },
  celebrityItem: {
    flexDirection: 'row',
    padding: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#333333',
  },
  celebrityImage: {
    width: 80,
    height: 80,
    borderRadius: 5,
  },
  celebrityInfo: {
    marginLeft: 15,
    justifyContent: 'center',
  },
  celebrityType: {
    color: '#999999',
    fontFamily: FONTS.bebasNeue.regular,
    fontSize: 14,
    marginBottom: 5,
  },
  celebrityName: {
    color: '#FFFFFF',
    fontFamily: FONTS.bebasNeue.regular,
    fontSize: 22,
    letterSpacing: 1,
  },
  bottomPadding: {
    height: 60,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 50,
  },
  loadingText: {
    color: '#FFFFFF',
    fontFamily: FONTS.pretendard.light,
    fontSize: 14,
    marginTop: 10,
  },
  noDataContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 50,
  },
  noDataText: {
    color: '#FFFFFF',
    fontFamily: FONTS.pretendard.light,
    fontSize: 16,
  },
  categoriesLoadingContainer: {
    height: 50,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 15,
  },
});

export default SubscriptionScreen;
