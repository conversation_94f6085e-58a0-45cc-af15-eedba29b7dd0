import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Image,
  TouchableOpacity,
  ScrollView,
  StatusBar,
  Dimensions,
  TextInput,
  Platform,
  Alert,
  Linking,
} from 'react-native';
import { launchImageLibrary, ImageLibraryOptions, launchCamera } from 'react-native-image-picker';

import { FONTS } from '../assets/fonts';
import { IMAGES } from '../assets/images';
import { Header, DatePickerModal } from '../components';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import LinearGradient from 'react-native-linear-gradient';
// AsyncStorage 已经在 userStorage 中导入了

// 导入用户存储服务
import { getUserInfo, getToken } from '../services/storage/userStorage';
// 导入API服务
import { post } from '../services/api/apiService';
// 导入Toast服务
import { showSuccess, showError } from '../services/toast';
// 导入事件发射器
import eventEmitter from '../services/events/eventEmitter';
// 导入i18n
import { useTranslation } from 'react-i18next';

// 定义导航参数类型
type RootStackParamList = {
  Home: undefined;
  MyProfile: undefined;
  ChangePassword: undefined;
};

type MyProfileScreenNavigationProp = StackNavigationProp<RootStackParamList, 'MyProfile'>;

const MyProfileScreen = () => {
  const navigation = useNavigation<MyProfileScreenNavigationProp>();
  const { t } = useTranslation();
  const [birthYear, setBirthYear] = useState('2023');
  const [birthMonth, setBirthMonth] = useState('01');
  const [birthDay, setBirthDay] = useState('01');

  // 用户名输入框状态
  const [nickname, setNickname] = useState('');
  const [originalNickname, setOriginalNickname] = useState('');

  // 用户信息状态
  const [userInfo, setUserInfo] = useState<any>(null);
  const [_isLoading, setIsLoading] = useState(false);
  const [_error, setError] = useState<string | null>(null);

  // 防抖定时器
  const nicknameTimerRef = useRef<NodeJS.Timeout | null>(null);

  // 在组件挂载时获取用户信息
  useEffect(() => {
    const fetchUserInfo = async () => {
      try {
        setIsLoading(true);
        setError(null);

        // 检查是否有 token
        const token = await getToken();

        if (token) {
          // 从本地存储获取用户信息
          const userInfoData = await getUserInfo();

          if (userInfoData && userInfoData.nm) {
            console.log('userInfoData', userInfoData);
            // 调用接口获取最新的用户信息
            const response = await post('/account/info', {
              nm: userInfoData.nm,
            });

            console.log('User info response:', JSON.stringify(response.data));

            if (response.data && response.data.code === '200' && response.data.data) {
              setUserInfo(response.data.data);

              // 如果有生日信息，更新生日字段
              // 设置昵称
              if (response.data.data.nickname) {
                setNickname(response.data.data.nickname);
                setOriginalNickname(response.data.data.nickname);
              } else if (response.data.data.email) {
                setNickname(response.data.data.email);
                setOriginalNickname(response.data.data.email);
              }

              // 设置生日
              if (response.data.data.birthday) {
                const birthday = response.data.data.birthday;
                // 假设生日格式为 'YYYY-MM-DD'
                const parts = birthday.split('-');
                if (parts.length === 3) {
                  setBirthYear(parts[0]);
                  setBirthMonth(parts[1]);
                  setBirthDay(parts[2]);
                }
              }
            } else {
              setError(response.data.msg || t('profile.failedToGetUserInfo'));
            }
          }
        }
      } catch (err) {
        console.error('Error fetching user info:', err);
        setError(t('profile.errorFetchingUserInfo'));
      } finally {
        setIsLoading(false);
      }
    };

    fetchUserInfo();
  }, [t]);

  // 日期选择器模态框状态
  const [datePickerVisible, setDatePickerVisible] = useState(false);


  // 打开日期选择器
  const openDatePicker = () => {
    console.log('openDatePicker called');
    setDatePickerVisible(true);
  };

  // 更新用户信息到服务器
  const updateUserInfo = async (data: any) => {
    try {
      if (!userInfo || !userInfo.id) {
        console.error('User info not available');
        return;
      }

      setIsLoading(true);
      setError(null);

      // 调用 API 更新用户信息
      const response = await post('/account/save', {
        id: userInfo.id,
        ...data,
      });

      console.log('Update user info response:', JSON.stringify(response.data));

      if (response.data && response.data.code === '200') {
        console.log('User info updated successfully');
        // 更新本地用户信息对象
        setUserInfo((prev: any) => ({
          ...prev,
          ...data,
        }));
        // 显示成功提示
        showSuccess(t('profile.updateSuccess'));
        return true;
      } else {
        const errorMsg = response.data.msg || t('profile.updateFailed');
        setError(errorMsg);
        console.error('Failed to update user info:', response.data.msg);
        // 显示错误提示
        showError(errorMsg);
        return false;
      }
    } catch (err) {
      console.error('Error updating user info:', err);
      const errorMsg = t('profile.errorUpdating');
      setError(errorMsg);
      // 显示错误提示
      showError(errorMsg);
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  // 更新生日信息
  const updateBirthday = async (year: string, month: string, day: string) => {
    // 构建生日日期字符串
    const birthday = `${year}-${month}-${day}`;

    // 调用通用的更新函数
    return await updateUserInfo({ birthday });
  };

  // 处理昵称变化
  const handleNicknameChange = (text: string) => {
    // 检查昵称长度不超过15个字符
    if (text.length > 15) {
      showError(t('profile.nicknameTooLong') || '昵称不能超过15个字符');
      // 截取前15个字符
      text = text.slice(0, 15);
    }
    
    setNickname(text);

    // 清除之前的定时器
    if (nicknameTimerRef.current) {
      clearTimeout(nicknameTimerRef.current);
    }

    // 设置新的定时器，延迟1秒后更新
    nicknameTimerRef.current = setTimeout(() => {
      updateNickname(text);
    }, 1000);
  };

  // 更新昵称
  const updateNickname = async (newNickname: string) => {
    // 如果昵称没有变化，不需要更新
    if (newNickname === originalNickname) {
      console.log('昵称没有变化，不需要更新');
      return true;
    }

    // 如果昵称为空，不更新
    if (!newNickname.trim()) {
      showError(t('profile.nicknameCannotBeEmpty'));
      setNickname(originalNickname); // 恢复原始昵称
      return false;
    }
    
    // 检查昵称长度不超过15个字符
    if (newNickname.length > 15) {
      showError(t('profile.nicknameTooLong') || '昵称不能超过15个字符');
      // 截取前15个字符
      newNickname = newNickname.slice(0, 15);
      setNickname(newNickname);
    }

    console.log('昵称已变化，从', originalNickname, '变为', newNickname);
    // 调用通用的更新函数
    const success = await updateUserInfo({ nickname: newNickname });
    if (success) {
      setOriginalNickname(newNickname); // 更新原始昵称为新昵称
    }
    return success;
  };

  // 确认日期选择
  const handleConfirmDate = (year: string, month: string, day: string) => {
    setBirthYear(year);
    setBirthMonth(month);
    setBirthDay(day);
    setDatePickerVisible(false);

    // 调用更新生日的函数
    updateBirthday(year, month, day);
  };

  // 取消日期选择
  const cancelDateSelection = () => {
    setDatePickerVisible(false);
  };

  // 处理修改密码
  const handleChangePassword = () => {
    console.log('Change password');
    // 导航到修改密码页面
    navigation.navigate('ChangePassword' as never);
  };

  // 处理搜索按钮点击
  const handleSearchPress = () => {
    console.log('Search pressed in MyProfileScreen');
    // 这里添加搜索功能的逻辑
  };

  // 处理菜单按钮点击
  const handleMenuPress = () => {
    console.log('Menu pressed in MyProfileScreen');
    // 导航到菜单页面
    navigation.navigate('Menu' as never);
  };

  // 处理选择图片
  const handleSelectImage = () => {
    Alert.alert(
      t('profile.selectImageSource'),
      t('profile.chooseSource'),
      [
        {
          text: t('common.cancel'),
          style: 'cancel'
        },
        {
          text: t('profile.camera'),
          onPress: launchCameraHandler
        },
        {
          text: t('profile.gallery'),
          onPress: launchImageLibraryHandler
        }
      ]
    );
  };

  // 启动相机
  const launchCameraHandler = async () => {
    try {
      console.log('Opening camera...');

      const options: ImageLibraryOptions = {
        mediaType: 'photo',
        quality: 0.8,
        maxWidth: 800,
        maxHeight: 800,
        includeBase64: false,
        selectionLimit: 1,
        presentationStyle: 'fullScreen',
      };

      const result = await launchCamera(options);
      handleImagePickerResult(result);
    } catch (error) {
      console.error('Error launching camera:', error);
      handleImagePickerError(error);
    }
  };

  // 启动图片库
  const launchImageLibraryHandler = async () => {
    try {
      console.log('Opening image library...');

      const options: ImageLibraryOptions = {
        mediaType: 'photo',
        quality: 0.8,
        maxWidth: 800,
        maxHeight: 800,
        includeBase64: false,
        selectionLimit: 1,
        presentationStyle: 'fullScreen',
      };

      const result = await launchImageLibrary(options);
      handleImagePickerResult(result);
    } catch (error) {
      console.error('Error launching image library:', error);
      handleImagePickerError(error);
    }
  };

  // 处理图片选择结果
  const handleImagePickerResult = (result: any) => {
    if (result.didCancel) {
      console.log('用户取消选择图片');
      return;
    }

    if (result.assets && result.assets.length > 0) {
      const selectedImage = result.assets[0];
      console.log('选择的图片:', selectedImage);
      uploadImage(selectedImage);
    }
  };

  // 处理图片选择错误
  const handleImagePickerError = (error: any) => {
    console.error('选择图片时发生错误:', error);
    
    if (error.code === 'permission' || error.code === 'camera_unavailable' || error.code === 'others') {
      Alert.alert(
        t('profile.permissionDenied'),
        t('profile.photoLibraryPermissionMessage'),
        [
          { text: t('common.cancel'), style: 'cancel' },
          {
            text: t('profile.goToSettings'),
            onPress: () => {
              Linking.openSettings();
            },
          },
        ]
      );
    } else {
      Alert.alert(t('common.error'), `${t('profile.errorSelectingImage')}: ${error.message}`);
    }
  };

  // 上传图片
  const uploadImage = async (imageFile: any) => {
    try {
      if (!userInfo || !userInfo.id) {
        console.error('User info not available');
        return;
      }

      setIsLoading(true);
      setError(null);

      // 获取文件名和类型
      const uri = Platform.OS === 'ios' ? imageFile.uri.replace('file://', '') : imageFile.uri;
      const fileType = imageFile.type || 'image/jpeg';
      const fileName = imageFile.fileName || 'photo.jpg';

      // 创建 FormData 对象
      const formData = new FormData();
      formData.append('file', {
        uri: uri,
        type: fileType,
        transformRequest: (_data: any, _headers: any) => {
          // 返回原始的 FormData 对象
          return formData;
        },
      });

      console.log('Upload response:', JSON.stringify(uploadResponse.data));

      if (uploadResponse.data && uploadResponse.data.code === '200' && uploadResponse.data.data) {
        // 上传成功，更新用户头像
        const imageUrl = uploadResponse.data.data;
        updateUserAvatar(imageUrl);
      } else {
        const errorMsg = uploadResponse.data?.msg || t('profile.failedToUploadImage');
        setError(errorMsg);
        console.error('Failed to upload image:', uploadResponse.data?.msg);
        showError(errorMsg);
        setIsLoading(false);
      }
    } catch (error) {
      console.error('Error uploading image:', error);
      const errorMsg = t('profile.errorUploadingImage');
      setError(errorMsg);
      showError(errorMsg);
      setIsLoading(false);
    }
  };

  // 更新用户头像
  const updateUserAvatar = async (imageUrl: string) => {
    try {
      // 调用更新用户信息接口
      const success = await updateUserInfo({
        headimage: imageUrl,
      });

      if (success) {
        showSuccess(t('profile.avatarUpdateSuccess'));
        // 触发头像更新事件
        eventEmitter.emit('AVATAR_UPDATED', imageUrl);
      }
    } catch (error) {
      console.error('Error updating user avatar:', error);
      showError(t('profile.failedToUpdateAvatar'));
    }
  };

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="transparent" translucent={true} />

      {/* 使用全局 Header 组件 */}
      <Header
        showBack={false}
        title={t('profile.myProfile')}
        rightComponentType="menu"
      />

      {/* 日期选择器模态框 */}
      <DatePickerModal
        visible={datePickerVisible}
        onClose={cancelDateSelection}
        onConfirm={handleConfirmDate}
        initialYear={birthYear}
        initialMonth={birthMonth}
        initialDay={birthDay}
      />

      <ScrollView style={styles.scrollView}>
        {/* 用户头像部分 */}
        <View style={styles.profileSection}>
          <TouchableOpacity
            style={styles.avatarContainer}
            onPress={handleSelectImage}
          >
            <LinearGradient
              colors={['#00FF00', '#0000FF']}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 1 }}
              style={styles.avatarBorder}
            >
              <Image
                source={userInfo?.headimage ? { uri: userInfo.headimage } : IMAGES.defaultAvatar}
                style={styles.avatarImage}
                resizeMode="cover"
              />
            </LinearGradient>

          </TouchableOpacity>

          {/* 用户名和编辑按钮 */}
          <View style={styles.nameContainer}>
            <TextInput
              style={styles.nameInput}
              value={nickname}
              onChangeText={handleNicknameChange}
              placeholder={t('profile.enterYourNickname')}
              placeholderTextColor="#AAAAAA"
              onBlur={() => {
                // 在失去焦点时立即更新，不等待定时器
                if (nicknameTimerRef.current) {
                  clearTimeout(nicknameTimerRef.current);
                  nicknameTimerRef.current = null;
                }
                updateNickname(nickname);
              }}
            />
            <TouchableOpacity
              style={styles.editButton}
              onPress={() => {
                // 在点击编辑按钮时立即更新，不等待定时器
                if (nicknameTimerRef.current) {
                  clearTimeout(nicknameTimerRef.current);
                  nicknameTimerRef.current = null;
                }
                updateNickname(nickname);
              }}
            >
              <Image
                source={IMAGES.editIcon}
                style={styles.editIcon}
              />
            </TouchableOpacity>
          </View>

          {/* 密码要求提示 */}
          <View style={styles.passwordRequirementContainer}>
            <Image
              source={IMAGES.warningIcon}
              style={styles.warningIcon}
            />
            <Text style={styles.passwordRequirementText}>
              {t('profile.passwordRequirement')}
            </Text>
          </View>
        </View>

        {/* 生日部分 */}
        <View style={styles.birthdaySection}>
          <Text style={styles.sectionTitle}>{t('profile.birthday')}</Text>

          <View style={styles.dateContainer}>
            {/* 年份 */}
            <View style={styles.dateColumn}>
              <Text style={styles.dateLabel}>{t('profile.year')}</Text>
              <TouchableOpacity
                style={styles.dateValueContainer}
                onPress={openDatePicker}
              >
                <Text style={styles.dateValue}>{birthYear}</Text>
                <Image
                  source={IMAGES.arrowRight}
                  style={[styles.dropdownIcon, { transform: [{ rotate: '90deg' }] }]}
                />
              </TouchableOpacity>
            </View>

            {/* 月份 */}
            <View style={styles.dateColumn}>
              <Text style={styles.dateLabel}>{t('profile.month')}</Text>
              <TouchableOpacity
                style={styles.dateValueContainer}
                onPress={openDatePicker}
              >
                <Text style={styles.dateValue}>{birthMonth}</Text>
                <Image
                  source={IMAGES.arrowRight}
                  style={[styles.dropdownIcon, { transform: [{ rotate: '90deg' }] }]}
                />
              </TouchableOpacity>
            </View>

            {/* 日期 */}
            <View style={styles.dateColumn}>
              <Text style={styles.dateLabel}>{t('profile.day')}</Text>
              <TouchableOpacity
                style={styles.dateValueContainer}
                onPress={openDatePicker}
              >
                <Text style={styles.dateValue}>{birthDay}</Text>
                <Image
                  source={IMAGES.arrowRight}
                  style={[styles.dropdownIcon, { transform: [{ rotate: '90deg' }] }]}
                />
              </TouchableOpacity>
            </View>
          </View>
        </View>

        {/* 修改密码按钮 */}
        <TouchableOpacity
          style={styles.changePasswordButton}
          onPress={handleChangePassword}
        >
          <Text style={styles.changePasswordText}>{t('profile.changePassword')}</Text>
          <Image
            source={IMAGES.arrowRight}
            style={styles.arrowIcon}
          />
        </TouchableOpacity>
      </ScrollView>
    </View>
  );
};

const { width } = Dimensions.get('window');

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000000',
  },
  scrollView: {
    flex: 1,
  },
  profileSection: {
    alignItems: 'center',
    paddingVertical: 30,
  },
  avatarContainer: {
    width: 180,
    height: 180,
    borderRadius: 90,
    overflow: 'hidden',
    marginBottom: 20,
    position: 'relative',
  },
  avatarBorder: {
    width: '100%',
    height: '100%',
    borderRadius: 90,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 3,
  },
  avatarImage: {
    width: '100%',
    height: '100%',
    borderRadius: 90,
  },
  editAvatarButton: {
    position: 'absolute',
    bottom: 10,
    right: 10,
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  editAvatarIcon: {
    width: 20,
    height: 20,
    tintColor: '#FFFFFF',
  },
  nameContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
  },
  nameText: {
    color: '#FFFFFF',
    fontSize: 24,
    fontFamily: FONTS.bebasNeue.regular,
    textAlign: 'center',
  },
  nameInput: {
    color: '#FFFFFF',
    fontSize: 24,
    marginLeft: 30,
    fontFamily: FONTS.bebasNeue.regular,
    borderBottomWidth: 1,
    borderBottomColor: '#AAAAAA',
    paddingBottom: 5,
    minWidth: 150,
    textAlign: 'center',
  },
  editButton: {
    padding: 5,
  },
  editIcon: {
    width: 20,
    height: 20,
    tintColor: '#FFFFFF',
  },
  passwordRequirementContainer: {
    flexDirection: 'column',
    alignItems: 'center',
    width: width * 0.8,
    marginTop: 10,
  },
  warningIcon: {
    width: 30,
    height: 30,
    marginBottom: 10,
    resizeMode: 'contain' 
  },
  passwordRequirementText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontFamily: FONTS.pretendard.light,
    flex: 1,
    lineHeight: 20,
    textAlign: 'center',
  },
  birthdaySection: {
    paddingHorizontal: 20,
    marginBottom: 30,
  },
  sectionTitle: {
    color: '#FFFFFF',
    fontSize: 24,
    fontFamily: FONTS.bebasNeue.regular,
    marginBottom: 20,
    textAlign: 'center',
  },
  dateContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 10,
  },
  dateColumn: {
    flex: 1,
    alignItems: 'center',
  },
  dateLabel: {
    color: '#AAAAAA',
    fontSize: 16,
    fontFamily: FONTS.pretendard.light,
    marginBottom: 10,
  },
  dateValueContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  dateValue: {
    color: '#FFFFFF',
    fontSize: 20,
    fontFamily: FONTS.bebasNeue.regular,
    marginRight: 5,
  },
  dropdownIcon: {
    width: 12,
    height: 8,
    resizeMode: 'contain',
    tintColor: '#FFFFFF',
  },
  changePasswordButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-around',
    marginHorizontal: 20,
    marginBottom: 40,
    height: 60,
    borderRadius: 30,
    borderWidth: 1,
    borderColor: '#D9C091',
  },
  changePasswordText: {
    color: '#FFFFFF',
    fontSize: 18,
    fontFamily: FONTS.pretendard.light,
    marginRight: 10,
  },
  arrowIcon: {
    width: 16,
    height: 16,
    tintColor: '#FFFFFF',
    resizeMode: 'contain'
  },

});

export default MyProfileScreen;
