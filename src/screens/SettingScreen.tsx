import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Image,
  StatusBar,
  ScrollView,
  ActivityIndicator,
} from 'react-native';
import { FONTS } from '../assets/fonts';
import { Header, LanguageBottomSheet, CustomSwitch } from '../components';
import { LanguageOption } from '../components/LanguageBottomSheet';
import { post } from '../services/api/apiService';
import { getUserInfo } from '../services/storage/userStorage';
import { useAlert } from '../context/AlertContext';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../context/LanguageContext';

// 语言选项已在 LanguageModal 中定义

const SettingScreen = () => {
  // 使用Alert上下文
  const { showAlert } = useAlert();

  // 使用翻译
  const { t } = useTranslation();

  // 使用语言上下文
  const { currentLanguageOption, setLanguageByOption } = useLanguage();

  // 加载状态
  const [isLoading, setIsLoading] = useState(true);

  // 开关状态
  const [soundEnabled, setSoundEnabled] = useState(true);
  const [alarmEnabled, setAlarmEnabled] = useState(false);
  const [renewSubscription, setRenewSubscription] = useState(false);
  const [completeVoiceMessage, setCompleteVoiceMessage] = useState(false);
  const [eventEnabled, setEventEnabled] = useState(false);
  const [noLateNightAlarms, setNoLateNightAlarms] = useState(false);

  // 当前选择的语言
  const [selectedLanguage, setSelectedLanguage] = useState<LanguageOption>(currentLanguageOption);

  // 语言底部弹窗是否显示
  const [languageBottomSheetVisible, setLanguageBottomSheetVisible] = useState(false);

  // 处理语言选择
  const handleLanguageSelect = async (language: LanguageOption) => {
    setSelectedLanguage(language);
    setLanguageBottomSheetVisible(false);

    // 更新全局语言设置
    await setLanguageByOption(language);
  };

  // 加载设置信息
  const loadSettingInfo = useCallback(async () => {
    try {
      setIsLoading(true);

      // 获取用户信息
      const userInfo = await getUserInfo();

      if (!userInfo || !userInfo.nm) {
        console.error('User info not available');
        setIsLoading(false);
        return;
      }

      // 调用设置信息接口
      const response = await post('/setting/info', {
        account: userInfo.nm,
      });

      console.log('Setting info response:', response.data);

      if (response.data && response.data.code === '200' && response.data.data) {
        const settingData = response.data.data;

        // 更新开关状态
        setSoundEnabled(!!settingData.soundSwitch);
        setAlarmEnabled(!!settingData.voiceAlarm);
        setRenewSubscription(!!settingData.subscribeReminder);
        setCompleteVoiceMessage(!!settingData.voiceMessageReminder);
        setEventEnabled(!!settingData.eventReminder);
        setNoLateNightAlarms(!!settingData.latenightReminder);

        console.log('Setting info loaded successfully');
      } else {
        // 显示错误信息
        const errorMsg = response.data?.msg || t('settings.failedToLoadSettings');
        console.error('Failed to load setting info:', errorMsg);
        showAlert(errorMsg);
      }
    } catch (error) {
      console.error('Error loading setting info:', error);
      showAlert(t('settings.errorLoadingSettingsTryAgain'));
    } finally {
      setIsLoading(false);
    }
  }, [showAlert]);

  // 一次性更新所有闹钟相关设置
  const updateAllAlarmSettings = async (value: boolean) => {
    try {
      // 获取用户信息
      const userInfo = await getUserInfo();

      if (!userInfo || !userInfo.nm) {
        console.error('User info not available');
        return;
      }

      // 创建请求参数，包含所有闹钟相关设置
      const params: Record<string, any> = {
        account: userInfo.nm,
        voiceAlarm: value,
        subscribeReminder: value,
        voiceMessageReminder: value,
        eventReminder: value,
        latenightReminder: value,
      };

      // 调用更改开关接口
      const response = await post('/setting/changeSwitch', params);

      console.log('Change all alarm settings response:', response.data);

      if (response.data && response.data.code !== '200') {
        // 显示错误信息
        const errorMsg = response.data?.msg || t('settings.failedToChangeSettings');
        console.error('Failed to change all alarm settings:', errorMsg);
        showAlert(errorMsg);

        // 如果失败，回滚开关状态
        setAlarmEnabled(!value);
        setRenewSubscription(!value);
        setCompleteVoiceMessage(!value);
        setEventEnabled(!value);
        setNoLateNightAlarms(!value);
      }
    } catch (error) {
      console.error('Error changing all alarm settings:', error);
      showAlert(t('settings.errorChangingSettingsTryAgain'));
    }
  };

  // 使用useCallback包裹updateAllAlarmSettings函数
  const memoizedUpdateAllAlarmSettings = useCallback(updateAllAlarmSettings, [showAlert]);

  // 处理开关变化
  const handleSwitchChange = async (switchName: string, value: boolean) => {
    try {
      // 获取用户信息
      const userInfo = await getUserInfo();

      if (!userInfo || !userInfo.nm) {
        console.error('User info not available');
        return;
      }

      // 创建请求参数
      const params: Record<string, any> = {
        account: userInfo.nm,
      };

      // 根据开关名称设置相应的字段
      params[switchName] = value;

      // 调用更改开关接口
      const response = await post('/setting/changeSwitch', params);

      console.log('Change switch response:', response.data);

      if (response.data && response.data.code !== '200') {
        // 显示错误信息
        const errorMsg = response.data?.msg || t('settings.failedToChangeSettings');
        console.error('Failed to change switch:', errorMsg);
        showAlert(errorMsg);

        // 如果失败，回滚开关状态
        switch (switchName) {
          case 'soundSwitch':
            setSoundEnabled(!value);
            break;
          case 'voiceAlarm':
            setAlarmEnabled(!value);
            break;
          case 'subscribeReminder':
            setRenewSubscription(!value);
            break;
          case 'voiceMessageReminder':
            setCompleteVoiceMessage(!value);
            break;
          case 'eventReminder':
            setEventEnabled(!value);
            break;
          case 'latenightReminder':
            setNoLateNightAlarms(!value);
            break;
        }
      }
    } catch (error) {
      console.error('Error changing switch:', error);
      showAlert(t('settings.errorChangingSettingsTryAgain'));
    }
  };

  // 在组件挂载时加载设置信息
  useEffect(() => {
    loadSettingInfo();
  }, [loadSettingInfo, memoizedUpdateAllAlarmSettings]);

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="transparent" translucent={true} />

      {/* 使用全局 Header 组件 */}
      <Header title={t('settings.title')} />

      {isLoading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#D9C091" />
        </View>
      ) : (
        <ScrollView 
          style={styles.scrollView}
          contentContainerStyle={styles.scrollViewContent}
          showsVerticalScrollIndicator={false}
          bounces={false}
          overScrollMode="never"
        >
          {/* 声音设置 */}
          <TouchableOpacity 
            style={styles.settingSection}
            activeOpacity={0.8}
            onPress={() => {
              setSoundEnabled(!soundEnabled);
              handleSwitchChange('soundSwitch', !soundEnabled);
            }}
          >
            <View style={styles.settingItem}>
              <View style={styles.settingLeft}>
                <Image source={require('../assets/images/settings/sound-icon.png')} style={styles.settingIcon} />
                <Text style={styles.settingTitle}>{t('settings.sound')}</Text>
              </View>
              <CustomSwitch
                value={soundEnabled}
                onValueChange={(value) => {
                  setSoundEnabled(value);
                  handleSwitchChange('soundSwitch', value);
                }}
              />
            </View>
          </TouchableOpacity>

          {/* 分隔线 */}
          <View style={styles.separator} />

          {/* 闹钟设置 */}
          <TouchableOpacity 
            style={styles.settingSection}
            activeOpacity={0.8}
            onPress={() => {
              const newValue = !alarmEnabled;
              setAlarmEnabled(newValue);
              if (!newValue) {
                setRenewSubscription(false);
                setCompleteVoiceMessage(false);
                setEventEnabled(false);
                setNoLateNightAlarms(false);
                memoizedUpdateAllAlarmSettings(false);
              } else {
                handleSwitchChange('voiceAlarm', true);
              }
            }}
          >
            <View style={styles.settingItem}>
              <View style={styles.settingLeft}>
                <Image source={require('../assets/images/settings/alarm-icon.png')} style={styles.settingIcon} />
                <Text style={styles.settingTitle}>{t('settings.alarm')}</Text>
              </View>
              <CustomSwitch
                value={alarmEnabled}
                onValueChange={(value) => {
                  setAlarmEnabled(value);
                  if (!value) {
                    setRenewSubscription(false);
                    setCompleteVoiceMessage(false);
                    setEventEnabled(false);
                    setNoLateNightAlarms(false);
                    memoizedUpdateAllAlarmSettings(false);
                  } else {
                    handleSwitchChange('voiceAlarm', true);
                  }
                }}
              />
            </View>
          </TouchableOpacity>

          {/* 子设置项 */}
          <TouchableOpacity 
            style={styles.subSettingItem}
            activeOpacity={0.8}
            disabled={!alarmEnabled}
            onPress={() => {
              if (alarmEnabled) {
                setRenewSubscription(!renewSubscription);
                handleSwitchChange('subscribeReminder', !renewSubscription);
              }
            }}
          >
            <View style={styles.subSettingLeft}>
              <Text style={styles.arrowIcon}>›</Text>
              <Text style={styles.subSettingTitle}>{t('settings.renewSubscription')}</Text>
            </View>
            <CustomSwitch
              value={renewSubscription}
              disabled={!alarmEnabled}
              onValueChange={(value) => {
                setRenewSubscription(value);
                handleSwitchChange('subscribeReminder', value);
              }}
            />
          </TouchableOpacity>

          <TouchableOpacity 
            style={styles.subSettingItem}
            activeOpacity={0.8}
            disabled={!alarmEnabled}
            onPress={() => {
              if (alarmEnabled) {
                setCompleteVoiceMessage(!completeVoiceMessage);
                handleSwitchChange('voiceMessageReminder', !completeVoiceMessage);
              }
            }}
          >
            <View style={styles.subSettingLeft}>
              <Text style={styles.arrowIcon}>›</Text>
              <Text style={styles.subSettingTitle}>{t('settings.completeVoiceMessage')}</Text>
            </View>
            <CustomSwitch
              value={completeVoiceMessage}
              disabled={!alarmEnabled}
              onValueChange={(value) => {
                setCompleteVoiceMessage(value);
                handleSwitchChange('voiceMessageReminder', value);
              }}
            />
          </TouchableOpacity>

          <TouchableOpacity 
            style={styles.subSettingItem}
            activeOpacity={0.8}
            disabled={!alarmEnabled}
            onPress={() => {
              if (alarmEnabled) {
                setEventEnabled(!eventEnabled);
                handleSwitchChange('eventReminder', !eventEnabled);
              }
            }}
          >
            <View style={styles.subSettingLeft}>
              <Text style={styles.arrowIcon}>›</Text>
              <Text style={styles.subSettingTitle}>{t('settings.event')}</Text>
            </View>
            <CustomSwitch
              value={eventEnabled}
              disabled={!alarmEnabled}
              onValueChange={(value) => {
                setEventEnabled(value);
                handleSwitchChange('eventReminder', value);
              }}
            />
          </TouchableOpacity>

          <TouchableOpacity 
            style={styles.subSettingItem}
            activeOpacity={0.8}
            disabled={!alarmEnabled}
            onPress={() => {
              if (alarmEnabled) {
                setNoLateNightAlarms(!noLateNightAlarms);
                handleSwitchChange('latenightReminder', !noLateNightAlarms);
              }
            }}
          >
            <View style={styles.subSettingLeft}>
              <Text style={styles.arrowIcon}>›</Text>
              <Text style={styles.subSettingTitle}>{t('settings.noLateNightAlarms')}</Text>
            </View>
            <CustomSwitch
              value={noLateNightAlarms}
              disabled={!alarmEnabled}
              onValueChange={(value) => {
                setNoLateNightAlarms(value);
                handleSwitchChange('latenightReminder', value);
              }}
            />
          </TouchableOpacity>

          {/* 语言设置 */}
          <TouchableOpacity
            style={styles.settingItem}
            activeOpacity={0.8}
            onPress={() => setLanguageBottomSheetVisible(true)}
          >
            <View style={styles.settingLeft}>
              <Image source={require('../assets/images/settings/language-icon.png')} style={styles.settingIcon} />
              <Text style={styles.settingTitle}>{t('settings.language')}</Text>
            </View>
            <View style={styles.languageSelector}>
              <Text style={styles.languageText}>{selectedLanguage}</Text>
              <Text style={styles.dropdownArrow}>›</Text>
            </View>
          </TouchableOpacity>

          {/* 语言选择底部弹窗 */}
          <LanguageBottomSheet
            visible={languageBottomSheetVisible}
            selectedLanguage={selectedLanguage}
            onSelect={handleLanguageSelect}
            onClose={() => setLanguageBottomSheetVisible(false)}
          />

          {/* 底部间距 */}
          <View style={styles.bottomPadding} />
        </ScrollView>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000000',
  },
  scrollView: {
    flex: 1,
  },
  scrollViewContent: {
    paddingTop: 10,
  },
  settingSection: {
    marginBottom: 10,
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 15,
    paddingHorizontal: 20,
    minHeight: 60,
  },
  settingLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  settingIcon: {
    height: 24,
    width: 24,
    marginRight: 15,
    tintColor: '#FFFFFF',
    resizeMode: 'contain',
  },
  settingTitle: {
    color: '#FFFFFF',
    fontSize: 18,
    fontFamily: FONTS.bebasNeue.regular,
    letterSpacing: 1,
  },
  separator: {
    height: 1,
    backgroundColor: '#333333',
    marginHorizontal: 20,
    marginVertical: 10,
  },
  subSettingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 12,
    paddingHorizontal: 20,
    marginLeft: 20,
    minHeight: 50,
    opacity: 1,
  },
  subSettingLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  arrowIcon: {
    color: '#FFFFFF',
    fontSize: 18,
    marginRight: 10,
    transform: [{ rotate: '90deg' }],
    width: 15,
    textAlign: 'center',
    lineHeight: 18,
  },
  subSettingTitle: {
    color: '#FFFFFF',
    fontSize: 16,
    fontFamily: FONTS.pretendard.light,
    flex: 1,
  },
  languageSelector: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  languageText: {
    color: '#D9C091',
    fontSize: 16,
    fontFamily: FONTS.pretendard.light,
    marginRight: 10,
  },
  dropdownArrow: {
    color: '#FFFFFF',
    fontSize: 18,
    transform: [{ rotate: '90deg' }],
  },
  bottomPadding: {
    height: 40,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default SettingScreen;
