import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  TextInput,
  Image,
  StatusBar,
  Dimensions,
  ActivityIndicator,
} from 'react-native';
import { FONTS } from '../assets/fonts';
import { IMAGES } from '../assets/images';
import { Header } from '../components';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { post } from '../services/api/apiService';
import { showSuccess, showError } from '../services/toast';
import { getUserInfo } from '../services/storage/userStorage';
import { useTranslation } from 'react-i18next';

// 定义导航参数类型
type RootStackParamList = {
  MyProfile: undefined;
  ChangePassword: undefined;
};

type ChangePasswordScreenNavigationProp = StackNavigationProp<RootStackParamList, 'ChangePassword'>;

const ChangePasswordScreen = () => {
  const navigation = useNavigation<ChangePasswordScreenNavigationProp>();
  const { t } = useTranslation();

  // 表单状态
  const [currentPassword, setCurrentPassword] = useState('');
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');

  // 密码可见性状态
  const [currentPasswordVisible, setCurrentPasswordVisible] = useState(false);
  const [newPasswordVisible, setNewPasswordVisible] = useState(false);
  const [confirmPasswordVisible, setConfirmPasswordVisible] = useState(false);

  // 加载状态
  const [isLoading, setIsLoading] = useState(false);

  // 验证密码
  const validatePasswords = () => {
    if (!currentPassword) {
      showError(t('password.enterCurrentPassword'));
      return false;
    }

    if (!newPassword) {
      showError(t('password.enterNewPassword'));
      return false;
    }

    if (!confirmPassword) {
      showError(t('password.confirmNewPassword'));
      return false;
    }

    if (newPassword !== confirmPassword) {
      showError(t('password.passwordsDoNotMatch'));
      return false;
    }

    // 验证密码长度
    if (newPassword.length < 8 || newPassword.length > 24) {
      showError(t('password.lengthRequirement'));
      return false;
    }

    // 验证密码包含小写字母和数字
    const hasLowercase = /[a-z]/.test(newPassword);
    const hasNumber = /[0-9]/.test(newPassword);

    if (!hasLowercase || !hasNumber) {
      showError(t('password.requireLowercaseAndNumbers'));
      return false;
    }

    return true;
  };

  // 处理提交
  const handleSubmit = async () => {
    if (!validatePasswords()) {
      return;
    }

    setIsLoading(true);

    try {
      const userInfo = await getUserInfo();
      if (!userInfo || !userInfo.id) {
        showError(t('password.userInfoNotFound'));
        setIsLoading(false);
        return;
      }
      const params =  {
        id: userInfo.id,
        curpassword: currentPassword,
        password: currentPassword,
        newpassword: newPassword,
      }
      console.log('更改的密码参数', params);
      const response = await post('/account/editPass', params);
      console.log('更改的密码响应', response);
      // 检查响应状态
      if (response && response.data.code === '200') {
        showSuccess(t('password.changeSuccess'));
        navigation.goBack();
      } else {
        // 如果有错误消息，显示错误消息
        const errorMsg = response?.data?.msg || t('password.changeFailed');
        showError(errorMsg);
      }
    } catch (error) {
      console.error('Error changing password:', error);
      showError(t('password.errorOccurred'));
    } finally {
      setIsLoading(false);
    }
  };

  // 切换密码可见性
  const toggleCurrentPasswordVisibility = () => {
    setCurrentPasswordVisible(!currentPasswordVisible);
  };

  const toggleNewPasswordVisibility = () => {
    setNewPasswordVisible(!newPasswordVisible);
  };

  const toggleConfirmPasswordVisibility = () => {
    setConfirmPasswordVisible(!confirmPasswordVisible);
  };

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="transparent" translucent={true} />

      {/* 使用全局 Header 组件 */}
      <Header title={t('profile.myProfile')} />

      <View style={styles.content}>
        {/* 当前密码输入 */}
        <View style={styles.inputSection}>
          <Text style={styles.inputLabel}>{t('password.enterCurrentPasswordLabel')}</Text>
          <View style={styles.inputContainer}>
            <TextInput
              style={styles.textInput}
              placeholder=""
              placeholderTextColor="#666"
              value={currentPassword}
              onChangeText={setCurrentPassword}
              secureTextEntry={!currentPasswordVisible}
              autoCapitalize="none"
            />
            <TouchableOpacity
              style={styles.eyeButton}
              onPress={toggleCurrentPasswordVisibility}
            >
              <Image
                source={currentPasswordVisible ? IMAGES.eyeOpen : IMAGES.eyeClosed}
                style={styles.eyeIcon}
              />
            </TouchableOpacity>
          </View>
        </View>

        {/* 新密码输入 */}
        <View style={styles.inputSection}>
          <Text style={styles.inputLabel}>{t('password.enterPasswordLabel')}</Text>
          <View style={styles.inputContainer}>
            <TextInput
              style={styles.textInput}
              placeholder=""
              placeholderTextColor="#666"
              value={newPassword}
              onChangeText={setNewPassword}
              secureTextEntry={!newPasswordVisible}
              autoCapitalize="none"
            />
            <TouchableOpacity
              style={styles.eyeButton}
              onPress={toggleNewPasswordVisibility}
            >
              <Image
                source={newPasswordVisible ? IMAGES.eyeOpen : IMAGES.eyeClosed}
                style={styles.eyeIcon}
              />
            </TouchableOpacity>
          </View>
        </View>

        {/* 确认新密码输入 */}
        <View style={styles.inputSection}>
          <View style={styles.inputContainer}>
            <TextInput
              style={styles.textInput}
              placeholder=""
              placeholderTextColor="#666"
              value={confirmPassword}
              onChangeText={setConfirmPassword}
              secureTextEntry={!confirmPasswordVisible}
              autoCapitalize="none"
            />
            <TouchableOpacity
              style={styles.eyeButton}
              onPress={toggleConfirmPasswordVisibility}
            >
              <Image
                source={confirmPasswordVisible ? IMAGES.eyeOpen : IMAGES.eyeClosed}
                style={styles.eyeIcon}
              />
            </TouchableOpacity>
          </View>
        </View>

        {/* 密码要求 */}
        <View style={styles.requirementsContainer}>
          <View style={styles.requirementItem}>
            <Text style={styles.bulletPoint}>•</Text>
            <Text style={styles.requirementLabel}>{t('password.length')} :</Text>
            <Text style={styles.requirementValue}>{t('password.lengthValue')}</Text>
          </View>

          <View style={styles.requirementItem}>
            <Text style={styles.bulletPoint}>•</Text>
            <Text style={styles.requirementLabel}>{t('password.requiredCondition')} :</Text>
            <Text style={styles.requirementValue}>{t('password.requiredConditionValue')}</Text>
          </View>

          <View style={styles.requirementItem}>
            <Text style={styles.bulletPoint}>•</Text>
            <Text style={styles.requirementLabel}>{t('password.optionalCondition')} :</Text>
            <Text style={styles.requirementValue}>{t('password.optionalConditionValue')}</Text>
          </View>
        </View>

        {/* 提交按钮 */}
        <TouchableOpacity
          style={[styles.submitButton, isLoading && styles.submitButtonDisabled]}
          onPress={handleSubmit}
          disabled={isLoading}
        >
          {isLoading ? (
            <ActivityIndicator color="#FFFFFF" size="small" />
          ) : (
            <>
              <Image
                source={IMAGES.checkIcon}
                style={styles.checkIcon}
              />
              <Text style={styles.submitButtonText}>{t('password.accept')}</Text>
            </>
          )}
        </TouchableOpacity>
      </View>
    </View>
  );
};

// 获取屏幕尺寸，用于响应式布局
const { width } = Dimensions.get('window');

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000000',
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
    paddingTop: 30,
  },
  inputSection: {
    marginBottom: 20,
  },
  inputLabel: {
    color: '#FFFFFF',
    fontSize: 18,
    fontFamily: FONTS.bebasNeue.regular,
    marginBottom: 10,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderBottomWidth: 1,
    borderBottomColor: '#444',
    paddingBottom: 5,
  },
  textInput: {
    flex: 1,
    color: '#FFFFFF',
    fontSize: 16,
    fontFamily: FONTS.pretendard.light,
    paddingVertical: 8,
  },
  eyeButton: {
    padding: 5,
  },
  eyeIcon: {
    height: 24,
    resizeMode: 'contain',
    tintColor: '#FFFFFF',
  },
  requirementsContainer: {
    marginTop: 30,
  },
  requirementItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 10,
  },
  bulletPoint: {
    color: '#FF004A',
    fontSize: 16,
    marginRight: 5,
  },
  requirementLabel: {
    color: '#FF004A',
    fontSize: 14,
    fontFamily: FONTS.pretendard.light,
    marginRight: 5,
  },
  requirementValue: {
    color: '#AAAAAA',
    fontSize: 14,
    fontFamily: FONTS.pretendard.light,
    flex: 1,
  },
  submitButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#FF004A',
    borderRadius: 30,
    height: 60,
    width: width * 0.9,
    alignSelf: 'center',
    marginTop: 'auto',
    marginBottom: 30,
  },
  submitButtonDisabled: {
    backgroundColor: '#666666',
    opacity: 0.7,
  },
  checkIcon: {
    width: 20,
    height: 20,
    tintColor: '#FFFFFF',
    marginRight: 10,
  },
  submitButtonText: {
    color: '#FFFFFF',
    fontSize: 18,
    fontFamily: FONTS.pretendard.light,
  },
});

export default ChangePasswordScreen;
