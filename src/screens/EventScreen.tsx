import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  StatusBar,
  ScrollView,
  ActivityIndicator,
} from 'react-native';
import { FONTS } from '../assets/fonts';
import { Header, NoticeDetailModal } from '../components';
import { get } from '../services/api/apiService';
import { showError } from '../services/toast';
import { useTranslation } from 'react-i18next';



const EventScreen = () => {
  const { t } = useTranslation();

  // 状态管理
  const [events, setEvents] = useState<any[]>([]);
  const [selectedEvent, setSelectedEvent] = useState<any>(null);
  const [modalVisible, setModalVisible] = useState(false);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // 获取活动数据
  const fetchEvents = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      const response = await get('/event/all');
      console.log('responseeventeventevent', response);
      if (response && response.data && response.data.code === '200') {
        // 处理API响应数据
        const eventData = response.data.data.map((item: any) => ({
          id: item.id || '',
          title: item.title || 'Event',
          date: formatDate(item.createDate),
          writer: item.author || 'Admin',
          content: item.content || '',
          highlighted: false, // 可以根据需要设置高亮项
        }));

        setEvents(eventData);
      } else {
        setError(t('event.failedToLoad'));
      }
    } catch (err) {
      console.error('Error fetching events:', err);
      setError(t('event.failedToLoad'));
      showError(t('event.failedToLoad'));
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchEvents();
  }, [fetchEvents]);

  // 格式化日期
  const formatDate = (dateString: string) => {
    try {
      const date = new Date(dateString);
      const year = date.getFullYear().toString().slice(2, 4); // 取年份的后两位
      const month = (date.getMonth() + 1).toString().padStart(2, '0');
      const day = date.getDate().toString().padStart(2, '0');
      return `${year}-${month}-${day}`;
    } catch (e) {
      return dateString;
    }
  };

  // 处理活动项点击
  const handleEventPress = (event: any) => {
    console.log(`Event ${event.id} pressed`);
    setSelectedEvent(event);
    setModalVisible(true);
  };

  // 刷新数据
  const handleRefresh = () => {
    fetchEvents();
  };

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="transparent" translucent={true} />

      {/* 使用全局 Header 组件 */}
      <Header title={t('menu.event')} />

      {loading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#D9C091" />
        </View>
      ) : error ? (
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>{error}</Text>
          <TouchableOpacity style={styles.refreshButton} onPress={handleRefresh}>
            <Text style={styles.refreshButtonText}>{t('common.retry')}</Text>
          </TouchableOpacity>
        </View>
      ) : (
        <ScrollView style={styles.scrollView}>
          {/* 活动列表 */}
          {events.length > 0 ? (
            events.map((event, index) => (
              <TouchableOpacity
                key={event.id || index}
                style={[
                  styles.eventItem,
                  event.highlighted && styles.highlightedEventItem,
                ]}
                onPress={() => handleEventPress(event)}
              >
                <View style={styles.eventContent}>
                  <Text style={styles.eventNumber}>[{index + 1}]</Text>
                  <Text style={styles.eventTitle} numberOfLines={1} ellipsizeMode="tail">[{event.title}]</Text>
                </View>
                <Text style={styles.eventDate}>{event.date}</Text>
              </TouchableOpacity>
            ))
          ) : (
            <View style={styles.emptyContainer}>
              <Text style={styles.emptyText}>{t('event.noEvents')}</Text>
            </View>
          )}
        </ScrollView>
      )}

      {/* 活动详情弹窗 */}
      {selectedEvent && (
        <NoticeDetailModal
          visible={modalVisible}
          onClose={() => setModalVisible(false)}
          notice={selectedEvent}
        />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000000',
  },
  scrollView: {
    flex: 1,
    paddingHorizontal: 20,
    paddingTop: 20,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingTop: 50,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingTop: 50,
  },
  errorText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontFamily: FONTS.pretendard.light,
    marginBottom: 20,
  },
  refreshButton: {
    backgroundColor: '#D9C091',
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 5,
  },
  refreshButtonText: {
    color: '#000000',
    fontSize: 16,
    fontFamily: FONTS.pretendard.light,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingTop: 50,
  },
  emptyText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontFamily: FONTS.pretendard.light,
  },
  eventItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 8,
    paddingHorizontal: 20,
    marginBottom: 15,
    backgroundColor: '#222222',
    borderRadius: 10,
  },
  highlightedEventItem: {
    borderWidth: 1,
    borderColor: '#9932CC', // 紫色边框
    backgroundColor: '#1A1A1A', // 稍深一点的背景色
  },
  eventContent: {
    flexDirection: 'column',
    justifyContent: 'center',
    flex: 1, // 添加flex属性以确保有足够的空间
    marginRight: 10, // 与日期保持一定距离
  },
  eventNumber: {
    color: '#FFFFFF',
    fontSize: 14,
    fontFamily: FONTS.pretendard.light,
  },
  eventTitle: {
    color: '#D9C091', // 金色文字
    fontSize: 14,
    fontFamily: FONTS.pretendard.light,
    marginTop: 5,
    width: '90%', // 限制宽度以确保省略号正常显示
  },
  eventDate: {
    color: '#FFFFFF',
    fontSize: 14,
    fontFamily: FONTS.pretendard.light,
  },
});

export default EventScreen;
