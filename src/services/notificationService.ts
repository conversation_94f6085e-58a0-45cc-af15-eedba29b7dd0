import notifee, {
  AndroidImportance,
  AndroidStyle,
  EventType,
  TriggerType,
  TimestampTrigger,
  AndroidCategory,
  AndroidVisibility,
  AndroidLaunchActivityFlag
} from '@notifee/react-native';
import { Platform } from 'react-native';

// 创建通知渠道（仅Android需要）
export const createChannel = async () => {
  if (Platform.OS === 'android') {
    const channelId = await notifee.createChannel({
      id: 'alarm_channel',
      name: 'Alarm Notifications',
      lights: true,
      vibration: true,
      importance: AndroidImportance.HIGH,
      sound: 'alarm_sound', // 确保在android/app/src/main/res/raw/目录下有此文件
      bypassDnd: true, // 绕过勿扰模式
      visibility: AndroidVisibility.PUBLIC,
    });
    return channelId;
  }
  return 'default';
};

// 设置闹钟
export const scheduleAlarm = async (
  alarmId: string | number,
  title: string,
  body: string,
  timestamp: number,
  data: any = {},
  sound?: string
) => {
  // 确保数据中包含alarmId
  data.alarmId = alarmId.toString();
  try {
    // 确保有通知渠道
    const channelId = await createChannel();

    // 创建触发器
    const trigger: TimestampTrigger = {
      type: TriggerType.TIMESTAMP,
      timestamp: timestamp,
    };

    // 构建通知内容
    const notification = {
      id: `alarm_${alarmId}`,
      title: title,
      body: body,
      android: {
        channelId,
        importance: AndroidImportance.HIGH,
        pressAction: {
          id: 'default',
          launchActivity: 'default',
          launchActivityFlags: [
            AndroidLaunchActivityFlag.SINGLE_TOP,
            AndroidLaunchActivityFlag.NEW_TASK,
            AndroidLaunchActivityFlag.CLEAR_TOP
          ],
        },
        fullScreenAction: {
          id: 'full_screen',
          launchActivity: 'default',
        },
        actions: [
          {
            title: '查看闹钟',
            pressAction: {
              id: 'view_alarm',
              launchActivity: 'default',
            },
          }
        ],
        category: AndroidCategory.ALARM,
        sound: sound || 'alarm_sound',
        autoCancel: true, // 点击后自动取消通知
        ongoing: true,
        style: {
          type: AndroidStyle.BIGTEXT,
          text: body,
        } as any,
        visibility: AndroidVisibility.PUBLIC,
        vibrationPattern: [300, 500, 300, 500],
        lights: ['#FF0000', 300, 600] as [string, number, number],
        showTimestamp: true,
        timestamp: timestamp,
        subText: null,
      },
      ios: {
        critical: true,
        sound: sound || 'alarm_sound.wav',
        interruptionLevel: 'critical' as any,
        foregroundPresentationOptions: {
          badge: true,
          sound: true,
          banner: true,
          list: true,
        },
      },
      data: {
        ...data,
        alarmId: alarmId.toString(),
        screen: 'AlarmTrigger',
        type: 'alarm',
        navigationType: 'reset', // 添加导航类型，指示应该使用 reset 而不是 navigate
        timestamp: Date.now(), // 添加时间戳，用于验证通知的新鲜度
      },
    };

    // 创建触发通知
    const notificationId = await notifee.createTriggerNotification(
      notification,
      trigger
    );

    console.log(`Alarm scheduled with ID: ${notificationId}`);
    return notificationId;
  } catch (error) {
    console.error('Error scheduling alarm:', error);
    throw error;
  }
};

// 取消闹钟
export const cancelAlarm = async (alarmId: string) => {
  try {
    await notifee.cancelNotification(`alarm_${alarmId}`);
    console.log(`Alarm with ID ${alarmId} cancelled`);
    return true;
  } catch (error) {
    console.error('Error cancelling alarm:', error);
    throw error;
  }
};

// 取消所有闹钟
export const cancelAllAlarms = async () => {
  try {
    await notifee.cancelAllNotifications();
    console.log('All alarms cancelled');
    return true;
  } catch (error) {
    console.error('Error cancelling all alarms:', error);
    throw error;
  }
};

// 获取所有待处理的通知
export const getPendingAlarms = async () => {
  try {
    const notifications = await notifee.getTriggerNotifications();
    return notifications.filter(item => item.notification.id?.startsWith('alarm_'));
  } catch (error) {
    console.error('Error getting pending alarms:', error);
    throw error;
  }
};

// 处理通知事件
export const setupNotificationListeners = (
  onForegroundEvent?: (event: any) => void
) => {
  // 前台事件监听器
  const unsubscribeForeground = notifee.onForegroundEvent(event => {
    if (onForegroundEvent) {
      onForegroundEvent(event);
    } else {
      handleNotificationEvent(event);
    }
  });

  // 后台事件监听器在 index.js 中设置

  return unsubscribeForeground;
};

// 处理通知事件
export const handleNotificationEvent = async (event: any) => {
  const { type, detail } = event;

  // 处理通知点击事件
  if (type === EventType.PRESS) {
    console.log('User pressed notification in foreground', detail.notification);

    // 获取通知数据
    const { notification } = detail;

    // 检查是否有导航数据
    if (notification?.data?.screen === 'AlarmTrigger' && notification?.data?.alarmId) {
      console.log('Notification contains navigation data to AlarmTrigger screen');
      console.log('Alarm ID:', notification.data.alarmId);

      try {
        // 导入所需模块
        const AsyncStorage = require('@react-native-async-storage/async-storage').default;
        const { reset } = require('../navigation');

        // 保存通知点击信息到 AsyncStorage
        await AsyncStorage.setItem('@aivgen:notification_clicked', 'true');
        await AsyncStorage.setItem('@aivgen:target_screen', 'AlarmTrigger');
        await AsyncStorage.setItem('@aivgen:pending_alarm_id', notification.data.alarmId.toString());
        await AsyncStorage.setItem('@aivgen:notification_timestamp', Date.now().toString());

        console.log('Saved notification click data to AsyncStorage');

        // 取消当前通知，避免重复
        if (notification.id) {
          await notifee.cancelNotification(notification.id);
        }

        // 使用 setTimeout 确保导航已准备好
        setTimeout(() => {
          try {
            console.log('Executing navigation reset to AlarmTrigger with ID:', notification.data.alarmId);
            // 使用 try-catch 包裹导航调用
            reset('AlarmTrigger', { alarmId: notification.data.alarmId });
          } catch (navError) {
            console.error('Navigation error:', navError);
            // 如果导航失败，至少我们已经保存了数据到 AsyncStorage
            console.log('Navigation failed, but data is saved to AsyncStorage for next app launch');
          }
        }, 1000);
      } catch (error) {
        console.error('Error handling notification press:', error);
      }
    }
  }

  // 处理通知触发事件
  if (type === EventType.DELIVERED) {
    console.log('Notification delivered', detail.notification);
  }

  // 处理通知关闭事件
  if (type === EventType.DISMISSED) {
    console.log('User dismissed notification', detail.notification);
  }
};

// 请求通知权限
export const requestNotificationPermissions = async () => {
  try {
    const settings = await notifee.requestPermission({
      sound: true,
      announcement: true,
      criticalAlert: true,
      alert: true,
      badge: true,
      carPlay: true,
      provisional: false,
    });

    return settings.authorizationStatus >= 1;
  } catch (error) {
    console.error('Error requesting notification permissions:', error);
    return false;
  }
};

export default {
  createChannel,
  scheduleAlarm,
  cancelAlarm,
  cancelAllAlarms,
  getPendingAlarms,
  setupNotificationListeners,
  handleNotificationEvent,
  requestNotificationPermissions,
};
