/**
 * Toast服务
 * 提供全局Toast消息显示功能
 */
import Toast from 'react-native-toast-message';
import i18n from 'i18next';

/**
 * 显示成功Toast
 * @param message 消息内容
 * @param options 其他选项
 */
export const showSuccess = (message: string, options = {}) => {
  Toast.show({
    type: 'success',
    text1: i18n.t('toast.success'),
    text2: message,
    position: 'bottom',
    visibilityTime: 2000,
    ...options,
  });
};

/**
 * 显示错误Toast
 * @param message 消息内容
 * @param options 其他选项
 */
export const showError = (message: string, options = {}) => {
  Toast.show({
    type: 'error',
    text1: i18n.t('toast.error'),
    text2: message,
    position: 'bottom',
    visibilityTime: 3000,
    ...options,
  });
};

/**
 * 显示信息Toast
 * @param message 消息内容
 * @param options 其他选项
 */
export const showInfo = (message: string, options = {}) => {
  Toast.show({
    type: 'info',
    text1: i18n.t('toast.info'),
    text2: message,
    position: 'bottom',
    visibilityTime: 2000,
    ...options,
  });
};

/**
 * 显示警告Toast
 * @param message 消息内容
 * @param options 其他选项
 */
export const showWarning = (message: string, options = {}) => {
  Toast.show({
    type: 'warning',
    text1: i18n.t('toast.warning'),
    text2: message,
    position: 'bottom',
    visibilityTime: 2500,
    ...options,
  });
};

/**
 * 显示自定义Toast
 * @param options Toast选项
 */
export const showToast = (options: any) => {
  Toast.show(options);
};

/**
 * 隐藏当前Toast
 */
export const hideToast = () => {
  Toast.hide();
};

// 导出Toast组件，用于在应用根组件中渲染
export { Toast };

// 默认导出所有Toast方法
export default {
  showSuccess,
  showError,
  showInfo,
  showWarning,
  showToast,
  hideToast,
};
