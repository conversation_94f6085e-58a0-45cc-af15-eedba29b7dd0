/**
 * Toast配置
 * 自定义Toast样式和行为
 */
import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { BaseToast, ErrorToast, InfoToast, ToastConfig } from 'react-native-toast-message';
import { FONTS } from '../../assets/fonts';

// 基础Toast样式
const baseStyle = {
  borderLeftColor: '#FF0066',
  backgroundColor: '#1A1A1A',
  height: 'auto',
  minHeight: 60,
  paddingVertical: 10,
};

// 文本样式
const textStyle = {
  text1: {
    fontSize: 16,
    fontFamily: FONTS.bebasNeue.regular,
    color: '#FFFFFF',
  },
  text2: {
    fontSize: 14,
    fontFamily: FONTS.pretendard.light,
    color: '#CCCCCC',
  },
};

// 自定义Toast配置
export const toastConfig: ToastConfig = {
  // 成功Toast
  success: (props) => (
    <BaseToast
      {...props}
      style={{ ...baseStyle, borderLeftColor: '#00C851' }}
      contentContainerStyle={{ paddingHorizontal: 15 }}
      text1Style={textStyle.text1}
      text2Style={textStyle.text2}
      text1NumberOfLines={1}
      text2NumberOfLines={2}
    />
  ),

  // 错误Toast
  error: (props) => (
    <ErrorToast
      {...props}
      style={{ ...baseStyle, borderLeftColor: '#FF4444' }}
      contentContainerStyle={{ paddingHorizontal: 15 }}
      text1Style={textStyle.text1}
      text2Style={textStyle.text2}
      text1NumberOfLines={1}
      text2NumberOfLines={2}
    />
  ),

  // 信息Toast
  info: (props) => (
    <InfoToast
      {...props}
      style={{ ...baseStyle, borderLeftColor: '#33B5E5' }}
      contentContainerStyle={{ paddingHorizontal: 15 }}
      text1Style={textStyle.text1}
      text2Style={textStyle.text2}
      text1NumberOfLines={1}
      text2NumberOfLines={2}
    />
  ),

  // 警告Toast
  warning: (props) => (
    <BaseToast
      {...props}
      style={{ ...baseStyle, borderLeftColor: '#FFBB33' }}
      contentContainerStyle={{ paddingHorizontal: 15 }}
      text1Style={textStyle.text1}
      text2Style={textStyle.text2}
      text1NumberOfLines={1}
      text2NumberOfLines={2}
    />
  ),

  // 简单Toast（只显示一行文本）
  simple: ({ text1, props }) => (
    <View style={styles.simpleToast}>
      <Text style={styles.simpleText}>{text1}</Text>
    </View>
  ),
};

// 样式
const styles = StyleSheet.create({
  simpleToast: {
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 25,
    marginHorizontal: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  simpleText: {
    color: '#FFFFFF',
    fontFamily: FONTS.pretendard.light,
    fontSize: 14,
  },
});

export default toastConfig;
