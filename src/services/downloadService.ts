import RNFS from 'react-native-fs';
import { Platform } from 'react-native';

/**
 * 文件下载服务
 * 用于下载音频文件到本地存储
 */

// 获取音频文件存储目录
const getAudioDirectory = () => {
  if (Platform.OS === 'ios') {
    return `${RNFS.DocumentDirectoryPath}/audio`;
  } else {
    return `${RNFS.DocumentDirectoryPath}/audio`;
  }
};

// 确保音频目录存在
const ensureAudioDirectoryExists = async () => {
  const audioDir = getAudioDirectory();
  const exists = await RNFS.exists(audioDir);
  if (!exists) {
    await RNFS.mkdir(audioDir);
  }
  return audioDir;
};

// 生成本地文件名
const generateLocalFileName = (url: string, alarmId: string | number) => {
  const urlParts = url.split('/');
  const originalFileName = urlParts[urlParts.length - 1];
  const extension = originalFileName.includes('.') 
    ? originalFileName.split('.').pop() 
    : 'wav';
  
  return `alarm_${alarmId}_${Date.now()}.${extension}`;
};

// 下载音频文件
export const downloadAudioFile = async (
  url: string, 
  alarmId: string | number,
  onProgress?: (progress: number) => void
): Promise<string> => {
  try {
    console.log('Starting audio download:', url);
    
    // 确保音频目录存在
    const audioDir = await ensureAudioDirectoryExists();
    
    // 生成本地文件名
    const fileName = generateLocalFileName(url, alarmId);
    const localPath = `${audioDir}/${fileName}`;
    
    // 检查文件是否已存在
    const fileExists = await RNFS.exists(localPath);
    if (fileExists) {
      console.log('Audio file already exists:', localPath);
      return localPath;
    }
    
    // 下载文件
    const downloadResult = await RNFS.downloadFile({
      fromUrl: url,
      toFile: localPath,
      progress: (res) => {
        if (onProgress) {
          const progress = (res.bytesWritten / res.contentLength) * 100;
          onProgress(progress);
        }
      },
    }).promise;
    
    if (downloadResult.statusCode === 200) {
      console.log('Audio file downloaded successfully:', localPath);
      return localPath;
    } else {
      throw new Error(`Download failed with status code: ${downloadResult.statusCode}`);
    }
  } catch (error) {
    console.error('Error downloading audio file:', error);
    throw error;
  }
};

// 检查本地音频文件是否存在
export const checkLocalAudioFile = async (alarmId: string | number): Promise<string | null> => {
  try {
    const audioDir = getAudioDirectory();
    const exists = await RNFS.exists(audioDir);
    if (!exists) {
      return null;
    }
    
    // 查找匹配的音频文件
    const files = await RNFS.readDir(audioDir);
    const audioFile = files.find(file => 
      file.name.startsWith(`alarm_${alarmId}_`) && 
      (file.name.endsWith('.wav') || file.name.endsWith('.mp3') || file.name.endsWith('.m4a'))
    );
    
    return audioFile ? audioFile.path : null;
  } catch (error) {
    console.error('Error checking local audio file:', error);
    return null;
  }
};

// 删除本地音频文件
export const deleteLocalAudioFile = async (filePath: string): Promise<boolean> => {
  try {
    const exists = await RNFS.exists(filePath);
    if (exists) {
      await RNFS.unlink(filePath);
      console.log('Audio file deleted:', filePath);
      return true;
    }
    return false;
  } catch (error) {
    console.error('Error deleting audio file:', error);
    return false;
  }
};

// 清理旧的音频文件（可选，用于清理存储空间）
export const cleanupOldAudioFiles = async (maxAgeInDays: number = 30): Promise<void> => {
  try {
    const audioDir = getAudioDirectory();
    const exists = await RNFS.exists(audioDir);
    if (!exists) {
      return;
    }
    
    const files = await RNFS.readDir(audioDir);
    const now = Date.now();
    const maxAge = maxAgeInDays * 24 * 60 * 60 * 1000; // 转换为毫秒
    
    for (const file of files) {
      const fileAge = now - new Date(file.mtime || 0).getTime();
      if (fileAge > maxAge) {
        await RNFS.unlink(file.path);
        console.log('Cleaned up old audio file:', file.path);
      }
    }
  } catch (error) {
    console.error('Error cleaning up old audio files:', error);
  }
};

// 获取音频文件信息
export const getAudioFileInfo = async (filePath: string) => {
  try {
    const exists = await RNFS.exists(filePath);
    if (!exists) {
      return null;
    }
    
    const stat = await RNFS.stat(filePath);
    return {
      path: filePath,
      size: stat.size,
      modificationTime: stat.mtime,
      isFile: stat.isFile(),
    };
  } catch (error) {
    console.error('Error getting audio file info:', error);
    return null;
  }
};

export default {
  downloadAudioFile,
  checkLocalAudioFile,
  deleteLocalAudioFile,
  cleanupOldAudioFiles,
  getAudioFileInfo,
};
