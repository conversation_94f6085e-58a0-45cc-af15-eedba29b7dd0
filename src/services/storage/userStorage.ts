/**
 * 用户信息存储服务
 */
import AsyncStorage from '@react-native-async-storage/async-storage';

// 存储键
const TOKEN_KEY = '@aivgen:user_token';
const USER_INFO_KEY = '@aivgen:user_info';

// 用户信息类型
export interface UserInfo {
  id: number;
  nm: string;
  cid: string;
  createDate: string;
  status: string;
  email: string;
  password: string;
  nickname: string;
  birthday: string;
  headimage: string;
  lang: string;
  // 其他可能的用户信息字段
}

/**
 * 保存用户令牌
 * @param token 用户令牌
 */
export const saveToken = async (token: string): Promise<void> => {
  try {
    await AsyncStorage.setItem(TOKEN_KEY, token);
  } catch (error) {
    console.error('保存用户令牌失败:', error);
  }
};

/**
 * 获取用户令牌
 * @returns 用户令牌，如果不存在则返回null
 */
export const getToken = async (): Promise<string | null> => {
  try {
    return await AsyncStorage.getItem(TOKEN_KEY);
  } catch (error) {
    console.error('获取用户令牌失败:', error);
    return null;
  }
};

/**
 * 保存用户信息
 * @param userInfo 用户信息对象
 */
export const saveUserInfo = async (userInfo: UserInfo): Promise<void> => {
  try {
    await AsyncStorage.setItem(USER_INFO_KEY, JSON.stringify(userInfo));
  } catch (error) {
    console.error('保存用户信息失败:', error);
  }
};

/**
 * 获取用户信息
 * @returns 用户信息对象，如果不存在则返回null
 */
export const getUserInfo = async (): Promise<UserInfo | null> => {
  try {
    const userInfoStr = await AsyncStorage.getItem(USER_INFO_KEY);
    if (userInfoStr) {
      return JSON.parse(userInfoStr);
    }
    return null;
  } catch (error) {
    console.error('获取用户信息失败:', error);
    return null;
  }
};

/**
 * 清除用户信息和令牌（登出时使用）
 */
export const clearUserData = async (): Promise<void> => {
  try {
    await AsyncStorage.multiRemove([TOKEN_KEY, USER_INFO_KEY]);
  } catch (error) {
    console.error('清除用户数据失败:', error);
  }
};

/**
 * 检查用户是否已登录
 * @returns 如果用户已登录则返回true，否则返回false
 */
export const isLoggedIn = async (): Promise<boolean> => {
  const token = await getToken();
  return !!token;
};
