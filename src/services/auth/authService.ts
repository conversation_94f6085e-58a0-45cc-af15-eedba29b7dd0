/**
 * 认证服务
 * 提供登录、登出等认证相关功能
 */
import { clearUserData } from '../storage/userStorage';

/**
 * 用户登出
 * 清除用户数据并显示成功消息
 */
export const logout = async (): Promise<void> => {
  try {
    // 清除用户数据（token和用户信息）
    await clearUserData();

    // 注释掉显示登出成功消息，因为Toast服务可能还没有准备好
    // showSuccess('登出成功');

    console.log('User logged out successfully');
  } catch (error) {
    console.error('Logout error:', error);
    throw error;
  }
};
