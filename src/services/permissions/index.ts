/**
 * 权限服务
 * 提供权限请求和检查功能
 */
import { Platform, Linking, Alert, PermissionsAndroid } from 'react-native';
import React, { useState } from 'react';
import { AppState } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';

// 权限状态存储键
const NOTIFICATION_PERMISSION_KEY = 'notification_permission_granted';

// 自定义通知权限请求对话框
let showCustomNotificationPermissionModal: ((callback: (result: boolean) => void) => void) | null = null;

/**
 * 设置自定义通知权限请求对话框显示函数
 * @param showModalFn 显示模态框的函数
 */
export const setNotificationPermissionModalHandler = (
  showModalFn: (callback: (result: boolean) => void) => void
) => {
  showCustomNotificationPermissionModal = showModalFn;
};

/**
 * 检查通知权限状态
 * @returns Promise<boolean> 是否已授权
 */
export const checkNotificationPermission = async (): Promise<boolean> => {
  try {
    if (Platform.OS === 'android') {
      // Android 13+ (API 33+) 需要运行时请求通知权限
      if (Platform.Version >= 33) {
        const granted = await PermissionsAndroid.check(
          PermissionsAndroid.PERMISSIONS.POST_NOTIFICATIONS
        );
        return granted;
      }
      // Android 13以下默认允许通知
      return true;
    } else if (Platform.OS === 'ios') {
      // iOS需要使用UNUserNotificationCenter，但这里我们简化处理
      // 从AsyncStorage中获取之前保存的状态
      const permissionGranted = await AsyncStorage.getItem(NOTIFICATION_PERMISSION_KEY);
      return permissionGranted === 'true';
    }
    return false;
  } catch (error) {
    console.error('Error checking notification permission:', error);
    return false;
  }
};

/**
 * 保存通知权限状态
 * @param granted 是否授权
 */
export const saveNotificationPermissionStatus = async (granted: boolean): Promise<void> => {
  try {
    await AsyncStorage.setItem(NOTIFICATION_PERMISSION_KEY, granted ? 'true' : 'false');
  } catch (error) {
    console.error('Error saving notification permission status:', error);
  }
};

/**
 * 请求系统通知权限
 * @returns Promise<boolean> 是否获得权限
 */
const requestSystemNotificationPermission = async (): Promise<boolean> => {
  try {
    if (Platform.OS === 'android') {
      // Android 13+ (API 33+) 需要运行时请求通知权限
      if (Platform.Version >= 33) {
        const granted = await PermissionsAndroid.request(
          PermissionsAndroid.PERMISSIONS.POST_NOTIFICATIONS,
          {
            title: "AIVGEN Notification Permission",
            message: "AIVGEN needs access to your notifications to send voice alarms.",
            buttonNeutral: "Ask Me Later",
            buttonNegative: "Cancel",
            buttonPositive: "OK"
          }
        );
        return granted === PermissionsAndroid.RESULTS.GRANTED;
      }
      // Android 13以下默认允许通知
      return true;
    } else if (Platform.OS === 'ios') {
      // iOS需要使用UNUserNotificationCenter
      // 在真实环境中，我们需要使用原生模块来请求权限
      // 这里我们使用一个模拟的系统对话框，并在用户点击"Allow"后，
      // 引导用户去系统设置中手动开启通知权限
      return new Promise((resolve) => {
        Alert.alert(
          "Allow AIVGEN to send you notifications?",
          "AIVGEN needs to send you notifications for voice alarms.",
          [
            {
              text: "Don't Allow",
              onPress: () => resolve(false),
              style: 'cancel',
            },
            {
              text: "Allow",
              onPress: () => {
                // 在真实环境中，这里会触发系统通知权限请求
                // 由于我们没有原生模块，所以引导用户去系统设置
                Alert.alert(
                  "Enable Notifications",
                  "Please go to Settings and enable notifications for AIVGEN.",
                  [
                    {
                      text: "Cancel",
                      onPress: () => resolve(false),
                      style: 'cancel',
                    },
                    {
                      text: "Open Settings",
                      onPress: () => {
                        Linking.openSettings();
                        resolve(true);
                      },
                    },
                  ]
                );
              },
            },
          ]
        );
      });
    }
    return false;
  } catch (error) {
    console.error('Error requesting system notification permission:', error);
    return false;
  }
};

/**
 * 请求通知权限
 * @returns Promise<boolean> 是否获得权限
 */
export const requestNotificationPermission = async (): Promise<boolean> => {
  try {
    // 先检查是否已经授权
    const isGranted = await checkNotificationPermission();
    if (isGranted) {
      return true;
    }

    // 显示自定义UI
    if (showCustomNotificationPermissionModal) {
      return new Promise((resolve) => {
        showCustomNotificationPermissionModal(async (result) => {
          if (result) {
            // 用户在自定义UI中点击了允许，请求系统权限
            const systemGranted = await requestSystemNotificationPermission();
            // 保存系统权限状态
            saveNotificationPermissionStatus(systemGranted);
            resolve(systemGranted);
          } else {
            // 用户拒绝了权限
            saveNotificationPermissionStatus(false);
            resolve(false);
          }
        });
      });
    } else {
      // 如果没有设置自定义对话框，直接请求系统权限
      const systemGranted = await requestSystemNotificationPermission();
      saveNotificationPermissionStatus(systemGranted);
      return systemGranted;
    }
  } catch (error) {
    console.error('Error requesting notification permission:', error);
    return false;
  }
};

/**
 * 打开应用设置
 */
export const openAppSettings = async (): Promise<void> => {
  try {
    await Linking.openSettings();
  } catch (error) {
    console.error('Error opening app settings:', error);
  }
};
