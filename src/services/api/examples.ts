/**
 * API 使用示例
 * 
 * 这个文件仅用于展示如何使用封装的API服务，不会在实际项目中使用
 */
import { get, post, put, del, patch } from './apiService';
import { addRequestInterceptor, addResponseInterceptor } from './httpClient';

// 示例：添加认证令牌到请求头
addRequestInterceptor({
  onFulfilled: (config) => {
    // 从本地存储获取令牌
    const token = localStorage.getItem('auth_token');
    
    // 如果有令牌，添加到请求头
    if (token) {
      config.headers = {
        ...config.headers,
        'Authorization': `Bearer ${token}`
      };
    }
    
    return config;
  }
});

// 示例：处理401未授权响应
addResponseInterceptor({
  onFulfilled: (response) => {
    return response;
  },
  onRejected: (error) => {
    // 如果是401错误，可能需要刷新令牌或重定向到登录页面
    if (error.status === 401) {
      // 重定向到登录页面或刷新令牌
      // window.location.href = '/login';
    }
    
    return Promise.reject(error);
  }
});

// 示例：获取用户信息
export const getUserInfo = async (userId: string) => {
  try {
    const response = await get(`/users/${userId}`);
    return response.data;
  } catch (error) {
    console.error('获取用户信息失败', error);
    throw error;
  }
};

// 示例：用户登录
export const login = async (email: string, password: string) => {
  try {
    const response = await post('/account/login', { email, password });
    
    // 保存令牌到本地存储
    if (response.data.token) {
      localStorage.setItem('auth_token', response.data.token);
    }
    
    return response.data;
  } catch (error) {
    console.error('登录失败', error);
    throw error;
  }
};

// 示例：更新用户资料
export const updateUserProfile = async (userId: string, profileData: any) => {
  try {
    const response = await put(`/users/${userId}`, profileData);
    return response.data;
  } catch (error) {
    console.error('更新用户资料失败', error);
    throw error;
  }
};

// 示例：删除用户
export const deleteUser = async (userId: string) => {
  try {
    const response = await del(`/users/${userId}`);
    return response.data;
  } catch (error) {
    console.error('删除用户失败', error);
    throw error;
  }
};

// 示例：部分更新用户资料
export const updatePartialUserProfile = async (userId: string, partialData: any) => {
  try {
    const response = await patch(`/users/${userId}`, partialData);
    return response.data;
  } catch (error) {
    console.error('部分更新用户资料失败', error);
    throw error;
  }
};
