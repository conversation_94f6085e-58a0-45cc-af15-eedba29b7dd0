/**
 * API 服务
 */
import { AxiosRequestConfig, AxiosResponse } from 'axios';
import httpClient from './httpClient';
import { RequestConfig, ApiResponse, HttpMethod, ApiError } from './types';
import { handleApiError } from './errorHandler';

/**
 * 发送HTTP请求
 * @param config 请求配置
 * @returns Promise<ApiResponse<T>>
 */
export const request = async <T = any>(config: RequestConfig): Promise<ApiResponse<T>> => {
  try {
    const axiosConfig: AxiosRequestConfig = {
      ...config,
      method: config.method || 'GET',
    };

    const response: AxiosResponse<T> = await httpClient(axiosConfig);

    return {
      data: response.data,
      status: response.status,
      statusText: response.statusText,
      headers: response.headers as Record<string, string>,
    };
  } catch (error: any) {
    throw handleApiError(error);
  }
};

/**
 * 发送GET请求
 * @param url 请求URL
 * @param params 请求参数
 * @param config 其他配置
 * @returns Promise<ApiResponse<T>>
 */
export const get = <T = any>(
  url: string,
  params?: any,
  config?: Omit<RequestConfig, 'url' | 'method' | 'params'>
): Promise<ApiResponse<T>> => {
  return request<T>({
    url,
    method: 'GET',
    params,
    ...config,
  });
};

/**
 * 发送POST请求
 * @param url 请求URL
 * @param data 请求数据
 * @param config 其他配置
 * @returns Promise<ApiResponse<T>>
 */
export const post = <T = any>(
  url: string,
  data?: any,
  config?: Omit<RequestConfig, 'url' | 'method' | 'data'>
): Promise<ApiResponse<T>> => {
  return request<T>({
    url,
    method: 'POST',
    data,
    ...config,
  });
};

/**
 * 发送PUT请求
 * @param url 请求URL
 * @param data 请求数据
 * @param config 其他配置
 * @returns Promise<ApiResponse<T>>
 */
export const put = <T = any>(
  url: string,
  data?: any,
  config?: Omit<RequestConfig, 'url' | 'method' | 'data'>
): Promise<ApiResponse<T>> => {
  return request<T>({
    url,
    method: 'PUT',
    data,
    ...config,
  });
};

/**
 * 发送DELETE请求
 * @param url 请求URL
 * @param params 请求参数
 * @param config 其他配置
 * @returns Promise<ApiResponse<T>>
 */
export const del = <T = any>(
  url: string,
  params?: any,
  config?: Omit<RequestConfig, 'url' | 'method' | 'params'>
): Promise<ApiResponse<T>> => {
  return request<T>({
    url,
    method: 'DELETE',
    params,
    ...config,
  });
};

/**
 * 发送PATCH请求
 * @param url 请求URL
 * @param data 请求数据
 * @param config 其他配置
 * @returns Promise<ApiResponse<T>>
 */
export const patch = <T = any>(
  url: string,
  data?: any,
  config?: Omit<RequestConfig, 'url' | 'method' | 'data'>
): Promise<ApiResponse<T>> => {
  return request<T>({
    url,
    method: 'PATCH',
    data,
    ...config,
  });
};
