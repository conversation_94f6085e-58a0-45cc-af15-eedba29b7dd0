/**
 * HTTP 客户端
 */
import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import { API_URL, API_TIMEOUT, DEFAULT_HEADERS, IS_DEV } from './config';
import { RequestConfig, ApiResponse, RequestInterceptor, ResponseInterceptor } from './types';
import { handleApiError } from './errorHandler';

/**
 * 创建一个Axios实例
 */
const axiosInstance: AxiosInstance = axios.create({
  baseURL: API_URL,
  timeout: API_TIMEOUT,
  headers: DEFAULT_HEADERS,
});

// 请求拦截器数组
const requestInterceptors: RequestInterceptor[] = [];

// 响应拦截器数组
const responseInterceptors: ResponseInterceptor[] = [];

/**
 * 添加请求拦截器
 * @param interceptor 请求拦截器
 */
export const addRequestInterceptor = (interceptor: RequestInterceptor): void => {
  requestInterceptors.push(interceptor);

  axiosInstance.interceptors.request.use(
    interceptor.onFulfilled,
    interceptor.onRejected
  );
};

/**
 * 添加响应拦截器
 * @param interceptor 响应拦截器
 */
export const addResponseInterceptor = (interceptor: ResponseInterceptor): void => {
  responseInterceptors.push(interceptor);

  axiosInstance.interceptors.response.use(
    interceptor.onFulfilled,
    interceptor.onRejected
  );
};

// 默认请求拦截器
addRequestInterceptor({
  onFulfilled: async (config: RequestConfig) => {
    // 在发送请求之前做些什么
    if (IS_DEV) {
      console.log('[API Request]', config.method?.toUpperCase(), config.url, config.data || config.params);
    }

    try {
      // 动态导入getToken函数，避免循环依赖
      const { getToken } = await import('../storage/userStorage');
      const token = await getToken();

      // 如果有token，添加到请求头
      if (token) {
        config.headers = {
          ...config.headers,
          'token': token
        };
      }
    } catch (error) {
      console.error('Error adding token to request:', error);
    }

    return config;
  },
  onRejected: (error: any) => {
    // 对请求错误做些什么
    return Promise.reject(error);
  }
});

// 默认响应拦截器
addResponseInterceptor({
  onFulfilled: (response: AxiosResponse): ApiResponse => {
    // 对响应数据做些什么
    if (IS_DEV) {
      console.log('[API Response]', response.config.method?.toUpperCase(), response.config.url, response.data);
    }

    return {
      data: response.data,
      status: response.status,
      statusText: response.statusText,
      headers: response.headers as Record<string, string>,
    };
  },
  onRejected: (error: any) => {
    // 对响应错误做些什么
    const apiError = handleApiError(error);
    return Promise.reject(apiError);
  }
});

// 导出Axios实例
export default axiosInstance;
