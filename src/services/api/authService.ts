/**
 * 认证相关的API服务
 */
import { post } from './apiService';
import { ApiResponse } from './types';
import AsyncStorage from '@react-native-async-storage/async-storage';

// 设备ID存储键
const DEVICE_ID_KEY = '@aivgen:device_id';

// 设备ID获取函数
// 在实际项目中，应该使用设备特定的ID或安装react-native-device-info库
export const getDeviceId = async (): Promise<string> => {
  try {
    // 尝试从 AsyncStorage 中获取设备ID
    const storedDeviceId = await AsyncStorage.getItem(DEVICE_ID_KEY);

    if (storedDeviceId) {
      return storedDeviceId;
    }

    // 如果没有存储的设备ID，生成一个新的
    const randomId = `device_${Math.random().toString(36).substring(2, 15)}`;

    // 将新生成的设备ID存储到 AsyncStorage
    await AsyncStorage.setItem(DEVICE_ID_KEY, randomId);

    return randomId;
  } catch (error) {
    console.error('Error getting or setting device ID:', error);
    // 如果出错，返回一个临时ID
    return `device_${Date.now()}`;
  }
};

// 登录接口响应类型
export interface LoginResponse {
  token?: string;
  user?: {
    id: string;
    email: string;
    name?: string;
    // 其他用户信息字段
  };
  // 其他响应字段
}

/**
 * 用户登录
 * @param email 用户邮箱
 * @param password 用户密码
 * @returns Promise<ApiResponse<LoginResponse>>
 */
export const login = async (email: string, password: string): Promise<ApiResponse<LoginResponse>> => {
  // 获取设备ID
  const cid = await getDeviceId();

  return post<LoginResponse>('/account/login', {
    email,
    password,
    cid,
  });
};

/**
 * 忘记密码
 * @param email 用户邮箱
 * @returns Promise<ApiResponse<any>>
 */
export const forgotPassword = async (email: string): Promise<ApiResponse<any>> => {
  // 获取设备ID，如果忘记密码API需要设备ID
  const cid = await getDeviceId();

  return post('/account/forgot-password', {
    email,
    cid
  });
};
