/**
 * 首页相关的API服务
 */
import { get } from './apiService';
import { ApiResponse } from './types';

// 通用数据类型
export interface CommonItem {
  name: string;
  headImage: string;
  category: string;
  collectStatus: boolean;
  subscriptStatus: boolean;
  nm?: string; // 添加nm字段，可选
  // 其他可能的字段
}

// 轮播图数据类型
export interface CarouselItem extends CommonItem {}

// 趋势和名人数据类型
export interface CelebrityItem {
  id: number;
  name: string;
  headImage: string;
  category: string;
  categoryExplain: string;
  code: string;
  createDate: string;
  flag: boolean;
  indonsia: boolean;
  nm: string;
  reserve1: string;
  reserve2: string;
  reserve3: string;
  reserve4: string;
  reserve5: string;
  singapore: boolean;
  status: string;
  thailand: boolean;
  vietnam: boolean;
  // 下面是可能不存在的字段，我们将其设置为可选
  collectStatus?: boolean;
  subscriptStatus?: boolean;
}

// 趋势数据类型
export interface TrendItem extends CelebrityItem {}

// 通用响应类型
export interface CommonResponse<T> {
  code: string;
  msg: string;
  data?: T[];
}

// 轮播图响应类型
export interface CarouselResponse extends CommonResponse<CarouselItem> {}

// 趋势响应类型
export interface TrendResponse extends CommonResponse<TrendItem> {}

// 名人响应类型
export interface CelebrityResponse extends CommonResponse<CelebrityItem> {}

/**
 * 获取首页轮播图数据
 * @returns Promise<ApiResponse<CarouselResponse>>
 */
export const getCarouselData = (): Promise<ApiResponse<CarouselResponse>> => {
  return get<CarouselResponse>('/home/<USER>');
};

/**
 * 获取首页轮播图数据（简化版）
 * @returns Promise<CarouselItem[]> 成功时返回轮播图数据，失败时返回空数组
 */
export const getCarouselDataSimple = async (): Promise<CarouselItem[]> => {
  try {
    // 发送请求
    const response = await get<CarouselResponse>('/home/<USER>');

    // 处理响应
    if (response.data && response.data.code === '200' && response.data.data) {
      return response.data.data;
    }
    return [];
  } catch (error) {
    console.error('Failed to fetch carousel data:', error);
    return [];
  }
};

/**
 * 获取首页趋势数据
 * @returns Promise<TrendItem[]> 成功时返回趋势数据，失败时返回空数组
 */
export const getTrendData = async (): Promise<TrendItem[]> => {
  try {
    // 发送请求
    const response = await get<TrendResponse>('/home/<USER>');

    // 处理响应
    if (response.data && response.data.code === '200' && response.data.data) {
      return response.data.data;
    }
    return [];
  } catch (error) {
    console.error('Failed to fetch trend data:', error);
    return [];
  }
};

/**
 * 获取首页名人数据
 * @returns Promise<CelebrityItem[]> 成功时返回名人数据，失败时返回空数组
 */
export const getCelebrityData = async (): Promise<CelebrityItem[]> => {
  try {
    // 发送请求
    const response = await get<CelebrityResponse>('/home/<USER>');

    // 处理响应
    if (response.data && response.data.code === '200' && response.data.data) {
      return response.data.data;
    }
    return [];
  } catch (error) {
    console.error('Failed to fetch celebrity data:', error);
    return [];
  }
};

/**
 * 获取名人列表数据
 * @param categoryId 分类 ID，用于过滤名人列表
 * @param sort 排序方式，例如 'name asc'
 * @returns Promise<CelebrityItem[]> 成功时返回名人列表数据，失败时返回空数组
 */
export const getCelebrityList = async (categoryId?: string, sort?: string): Promise<CelebrityItem[]> => {
  try {
    // 构建查询参数
    const params: Record<string, string> = {};
    if (categoryId && categoryId !== 'total') {
      params.categoryId = categoryId;
    }
    if (sort) {
      params.sort = sort;
    }

    // 构建查询字符串
    const queryString = Object.keys(params).length > 0
      ? '?' + Object.entries(params).map(([key, value]) => `${key}=${encodeURIComponent(value)}`).join('&')
      : '';
    console.log('接口过滤',queryString)
    // 发送请求
    const response = await get<CelebrityResponse>(`/celebrity/list${queryString}`);

    // 处理响应
    if (response.data && response.data.code === '200' && response.data.data) {
      return response.data.data;
    }
    return [];
  } catch (error) {
    console.error('Failed to fetch celebrity list:', error);
    return [];
  }
};

// 名人类型数据类型
export interface CelebrityTypeItem {
  id: number;
  category: string;
  categoryExplain: string;
  code: string;
  createDate: string;
  flag: boolean;
  nm: string;
  reserve1: string;
  reserve2: string;
  reserve3: string; // 灰色图标URL
  reserve4: string; // 橙色图标URL
  reserve5: string;
  status: string;
  // 其他可能的字段
}

// 名人类型响应类型
export interface CelebrityTypeResponse extends CommonResponse<CelebrityTypeItem> {}

/**
 * 获取名人类型数据
 * @returns Promise<CelebrityTypeItem[]> 成功时返回名人类型数据，失败时返回空数组
 */
export const getCelebrityTypes = async (): Promise<CelebrityTypeItem[]> => {
  try {
    // 发送请求
    const response = await get<CelebrityTypeResponse>('/celebrity/type');

    // 处理响应
    if (response.data && response.data.code === '200' && response.data.data) {
      return response.data.data;
    }
    return [];
  } catch (error) {
    console.error('Failed to fetch celebrity types:', error);
    return [];
  }
};

// 排序选项数据类型
export interface SortingOptionItem {
  id: number;
  name: string;
  code: string;
  dictCode: string;
  // 其他可能的字段
}

// 排序选项响应类型
export interface SortingOptionResponse {
  code: string;
  msg: string;
  data?: {
    trendSort?: SortingOptionItem[];
    [key: string]: any;
  };
}

/**
 * 获取排序选项数据
 * @returns Promise<SortingOptionItem[]> 成功时返回排序选项数据，失败时返回空数组
 */
export const getSortingOptions = async (): Promise<SortingOptionItem[]> => {
  try {
    // 发送请求
    const response = await get<SortingOptionResponse>('/sysDictDetail/getDictDatas?dicts=trendSort');

    // 处理响应
    if (response.data && response.data.code === '200' && response.data.data && response.data.data.trendSort) {
      return response.data.data.trendSort;
    }
    return [];
  } catch (error) {
    console.error('Failed to fetch sorting options:', error);
    return [];
  }
};

// 趋势类型数据类型
export interface TrendTypeItem {
  id: number;
  category: string;
  categoryExplain: string;
  code: string;
  createDate: string;
  flag: boolean;
  nm: string;
  reserve1: string;
  reserve2: string;
  reserve3: string; // 灰色图标URL
  reserve4: string; // 橙色图标URL
  reserve5: string;
  status: string;
  // 其他可能的字段
}

// 趋势类型响应类型
export interface TrendTypeResponse extends CommonResponse<TrendTypeItem> {}

/**
 * 获取趋势类型数据
 * @returns Promise<TrendTypeItem[]> 成功时返回趋势类型数据，失败时返回空数组
 */
export const getTrendTypes = async (): Promise<TrendTypeItem[]> => {
  try {
    // 发送请求
    const response = await get<TrendTypeResponse>('/trend/type');

    // 处理响应
    if (response.data && response.data.code === '200' && response.data.data) {
      return response.data.data;
    }
    return [];
  } catch (error) {
    console.error('Failed to fetch trend types:', error);
    return [];
  }
};

// 订阅列表数据类型
export interface SubscriptionItem {
  id: number;
  name: string;
  category: string;
  categoryExplain: string;
  headImage: string;
  nm?: string; // 添加nm字段
  // 其他可能的字段
}

// 订阅列表响应类型
export interface SubscriptionResponse extends CommonResponse<SubscriptionItem> {}

/**
 * 获取订阅列表数据
 * @param categoryId 分类 ID，用于过滤订阅列表
 * @param sort 排序方式，例如 'name asc'
 * @returns Promise<SubscriptionItem[]> 成功时返回订阅列表数据，失败时返回空数组
 */
export const getSubscriptionList = async (categoryId?: string, sort?: string): Promise<SubscriptionItem[]> => {
  try {
    // 引入用户存储服务
    const { getUserInfo } = await import('../storage/userStorage');

    // 获取用户信息
    let curAccount = '';
    try {
      const userInfo = await getUserInfo();
      if (userInfo) {
        curAccount = userInfo.nm || '';
      }
    } catch (e) {
      console.error('Failed to get userInfo:', e);
    }

    // 构建查询参数
    const params: Record<string, string> = {
      curAccount: curAccount,
    };
    if (categoryId && categoryId !== 'total') {
      params.categoryId = categoryId;
    }
    if (sort) {
      params.sort = sort;
    }

    // 构建查询字符串
    const queryString = Object.keys(params).length > 0
      ? '?' + Object.entries(params).map(([key, value]) => `${key}=${encodeURIComponent(value)}`).join('&')
      : '';

    console.log('Subscription list query string:', queryString);

    // 发送请求
    const response = await get<SubscriptionResponse>(`/subscript/my${queryString}`);

    // 处理响应
    if (response.data && response.data.code === '200' && response.data.data) {
      return response.data.data;
    }
    return [];
  } catch (error) {
    console.error('Failed to fetch subscription list:', error);
    return [];
  }
};
