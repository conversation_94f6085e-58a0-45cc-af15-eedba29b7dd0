/**
 * API服务类型定义
 */

// HTTP方法
export type HttpMethod = 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';

// 请求配置
export interface RequestConfig {
  url: string;
  method?: HttpMethod;
  params?: any;
  data?: any;
  headers?: Record<string, string>;
  timeout?: number;
  [key: string]: any;
}

// API响应类型
export interface ApiResponse<T = any> {
  data: T;
  status: number;
  statusText: string;
  headers: Record<string, string>;
  config?: any;
  request?: any;
}

// API错误类型
export interface ApiError {
  message: string;
  code: string;
  status: number;
  data?: any;
}

// 请求拦截器
export interface RequestInterceptor {
  onFulfilled: (config: RequestConfig) => Promise<RequestConfig> | RequestConfig;
  onRejected?: (error: any) => Promise<any>;
}

// 响应拦截器
export interface ResponseInterceptor {
  onFulfilled: (response: any) => Promise<ApiResponse> | ApiResponse;
  onRejected?: (error: any) => Promise<any>;
}

// 通用响应类型
export interface CommonResponse<T = any> {
  code: string;
  msg: string;
  data?: T[];
}
