/**
 * API 错误处理
 */
import { AxiosError } from 'axios';
import { ApiError } from './types';
import { IS_DEV } from './config';

/**
 * 处理API错误
 * @param error Axios错误对象
 * @returns 标准化的API错误对象
 */
export const handleApiError = (error: AxiosError): ApiError => {
  // 如果是开发环境，打印错误信息
  if (IS_DEV) {
    console.error('[API Error]', error);
  }

  // 默认错误信息
  let errorMessage = '请求失败，请稍后重试';
  let errorCode = 'UNKNOWN_ERROR';
  let statusCode = 500;
  let errorData = undefined;

  if (error.response) {
    // 服务器返回了错误响应
    statusCode = error.response.status;
    errorData = error.response.data;

    // 尝试从响应中获取错误信息
    if (error.response.data) {
      if (typeof error.response.data === 'string') {
        errorMessage = error.response.data;
      } else if (error.response.data.message) {
        errorMessage = error.response.data.message;
      } else if (error.response.data.error) {
        errorMessage = error.response.data.error;
      }

      // 尝试获取错误代码
      if (error.response.data.code) {
        errorCode = error.response.data.code;
      }
    }

    // 根据HTTP状态码设置通用错误信息
    switch (statusCode) {
      case 400:
        errorMessage = errorMessage || '请求参数错误';
        break;
      case 401:
        errorMessage = '未授权，请重新登录';
        break;
      case 403:
        errorMessage = '拒绝访问';
        break;
      case 404:
        errorMessage = '请求的资源不存在';
        break;
      case 500:
        errorMessage = '服务器内部错误';
        break;
      default:
        if (statusCode >= 500) {
          errorMessage = '服务器错误，请稍后重试';
        } else if (statusCode >= 400) {
          errorMessage = '请求错误，请检查输入';
        }
    }
  } else if (error.request) {
    // 请求已发送但没有收到响应
    errorMessage = '无法连接到服务器，请检查网络连接';
    errorCode = 'NETWORK_ERROR';
  } else {
    // 请求设置时发生错误
    errorMessage = error.message || errorMessage;
    errorCode = 'REQUEST_SETUP_ERROR';
  }

  // 返回标准化的错误对象
  return {
    message: errorMessage,
    code: errorCode,
    status: statusCode,
    data: errorData,
  };
};
