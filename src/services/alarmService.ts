import { Platform } from 'react-native';

// 这里我们将使用 react-native-alarm-notification 库
// 由于我们还没有安装这个库，先创建一个接口定义
interface AlarmNotificationData {
  id?: number | string;
  title: string;
  message: string;
  channel: string;
  small_icon: string;
  large_icon?: string;
  play_sound: boolean;
  sound_name?: string;
  vibrate: boolean;
  vibration?: number;
  fire_date: string;
  schedule_type: 'once' | 'repeat';
  repeat_interval?: 'minutely' | 'hourly' | 'daily' | 'weekly';
  data?: any;
  volume?: number;
  has_button?: boolean;
  bypass_dnd?: boolean;
  loop_sound?: boolean;
}

// 这个函数将在安装库后替换为实际的实现
const parseDate = (date: Date): string => {
  // 格式化日期为 'dd-MM-yyyy HH:mm:ss' 格式
  const day = String(date.getDate()).padStart(2, '0');
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const year = date.getFullYear();
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');
  const seconds = String(date.getSeconds()).padStart(2, '0');
  
  return `${day}-${month}-${year} ${hours}:${minutes}:${seconds}`;
};

// 设置闹钟
export const setAlarm = async (alarmData: Omit<AlarmNotificationData, 'channel' | 'small_icon'>) => {
  try {
    // 这里将在安装库后替换为实际的实现
    console.log('Setting alarm with data:', alarmData);
    
    // 返回一个模拟的闹钟ID
    return { id: Date.now() };
  } catch (error) {
    console.error('Error setting alarm:', error);
    throw error;
  }
};

// 取消闹钟
export const cancelAlarm = async (alarmId: number | string) => {
  try {
    // 这里将在安装库后替换为实际的实现
    console.log('Cancelling alarm with ID:', alarmId);
    return true;
  } catch (error) {
    console.error('Error cancelling alarm:', error);
    throw error;
  }
};

// 获取所有已设置的闹钟
export const getAllAlarms = async () => {
  try {
    // 这里将在安装库后替换为实际的实现
    console.log('Getting all alarms');
    return [];
  } catch (error) {
    console.error('Error getting alarms:', error);
    throw error;
  }
};

// 停止当前正在播放的闹钟声音
export const stopAlarmSound = async () => {
  try {
    // 这里将在安装库后替换为实际的实现
    console.log('Stopping alarm sound');
    return true;
  } catch (error) {
    console.error('Error stopping alarm sound:', error);
    throw error;
  }
};

// 检查通知权限（仅iOS）
export const checkNotificationPermissions = async () => {
  if (Platform.OS === 'ios') {
    try {
      // 这里将在安装库后替换为实际的实现
      console.log('Checking notification permissions');
      return { alert: true, badge: true, sound: true };
    } catch (error) {
      console.error('Error checking notification permissions:', error);
      throw error;
    }
  }
  return true; // Android默认返回true
};

// 请求通知权限（仅iOS）
export const requestNotificationPermissions = async () => {
  if (Platform.OS === 'ios') {
    try {
      // 这里将在安装库后替换为实际的实现
      console.log('Requesting notification permissions');
      return { alert: true, badge: true, sound: true };
    } catch (error) {
      console.error('Error requesting notification permissions:', error);
      throw error;
    }
  }
  return true; // Android默认返回true
};

export default {
  parseDate,
  setAlarm,
  cancelAlarm,
  getAllAlarms,
  stopAlarmSound,
  checkNotificationPermissions,
  requestNotificationPermissions,
};
