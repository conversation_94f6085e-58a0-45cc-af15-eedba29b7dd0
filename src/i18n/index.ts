import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { I18nManager } from 'react-native';

// 导入翻译文件
import en from './locales/en';
import zh from './locales/zh';
import th from './locales/th';

// 语言类型
export type LanguageType = 'en' | 'zh' | 'th';

// 语言选项映射到语言代码
export const languageCodeMap: Record<string, LanguageType> = {
  'ENGLISH': 'en',
  'CHINESE': 'zh',
  'THAI': 'th',
  'KOREAN': 'en', // 暂时映射到英文
  'JAPANESE': 'en', // 暂时映射到英文
};

// 语言代码映射到语言选项
export const languageOptionMap: Record<LanguageType, string> = {
  'en': 'ENGLISH',
  'zh': 'CHINESE',
  'th': 'THAI',
};

// 语言显示名称
export const languageDisplayNames: Record<LanguageType, string> = {
  'en': 'English',
  'zh': '中文(Chinese)',
  'th': 'ภาษาไทย(Thai)',
};

// 存储语言设置的键
const LANGUAGE_STORAGE_KEY = 'user_language';

// 获取存储的语言设置
export const getStoredLanguage = async (): Promise<LanguageType | null> => {
  try {
    const storedLanguage = await AsyncStorage.getItem(LANGUAGE_STORAGE_KEY);
    return storedLanguage as LanguageType || null;
  } catch (error) {
    console.error('Error getting stored language:', error);
    return null;
  }
};

// 存储语言设置
export const storeLanguage = async (language: LanguageType): Promise<void> => {
  try {
    await AsyncStorage.setItem(LANGUAGE_STORAGE_KEY, language);
  } catch (error) {
    console.error('Error storing language:', error);
  }
};

// 更改语言
export const changeLanguage = async (language: LanguageType): Promise<void> => {
  await i18n.changeLanguage(language);
  await storeLanguage(language);
};

// 初始化 i18n
i18n
  .use(initReactI18next)
  .init({
    resources: {
      en: { translation: en },
      zh: { translation: zh },
      th: { translation: th },
    },
    lng: 'en', // 默认语言
    fallbackLng: 'en', // 回退语言
    interpolation: {
      escapeValue: false, // 不转义 HTML
    },
    react: {
      useSuspense: false, // 禁用 Suspense
    },
  });

// 初始化语言设置
export const initializeLanguage = async (): Promise<void> => {
  try {
    const storedLanguage = await getStoredLanguage();
    if (storedLanguage) {
      await changeLanguage(storedLanguage);
    }
  } catch (error) {
    console.error('Error initializing language:', error);
  }
};

export default i18n;
