export default {
  // 菜单
  menu: {
    notice: '公告',
    event: '活动',
    faq: '常见问题',
    about: '关于',
    setting: '设置',
  },

  // 通用
  common: {
    ok: '确定',
    cancel: '取消',
    confirm: '确认',
    save: '保存',
    delete: '删除',
    edit: '编辑',
    loading: '加载中...',
    error: '错误',
    success: '成功',
    retry: '重试',
    back: '返回',
    next: '下一步',
    search: '搜索',
    noData: '暂无数据',
    seeAll: '查看全部',
    close: '关闭',
    send: '发送',
  },

  // 忘记密码
  forgotPassword: {
    newPasswordSent: '新密码已发送',
    toEmailBelow: '至以下邮箱地址',
    checkEmail: '请检查您的邮箱',
    codeSentSuccess: '验证码发送成功',
    codeSentFailed: '验证码发送失败',
    codeError: '发送验证码时出错',
  },

  // 导航
  navigation: {
    home: '首页',
    alarm: '闹钟',
    message: '消息',
    inbox: '收件箱',
    menu: '菜单',
    celebrity: '名人',
    subscription: '订阅',
    my: '我的',
    faq: '常见问题',
  },

  // 认证
  auth: {
    login: '登录',
    signup: '注册',
    logout: '退出登录',
    signIn: '登录',
    signOut: '退出登录',
    email: '邮箱',
    password: '密码',
    confirmPassword: '确认密码',
    forgotPassword: '忘记密码',
    resetPassword: '重置密码',
    loginWithGoogle: '使用谷歌账号登录',
    loginWithApple: '使用苹果账号登录',
    orLoginWith: '或者使用以下方式登录',
    dontHaveAccount: '还没有账号？',
    alreadyHaveAccount: '已有账号？',
    signupNow: '立即注册',
    loginNow: '立即登录',
    agreeToTerms: '我同意服务条款和隐私政策',
    passwordMismatch: '两次输入的密码不一致',
    invalidEmail: '无效的邮箱地址',
    passwordTooShort: '密码长度至少为6个字符',
    loginFailed: '登录失败，请检查您的凭据。',
    signupFailed: '注册失败，请重试。',
    logoutConfirm: '确定要退出登录吗？',
    signOutConfirmMessage: '如果您退出登录，再次运行应用时需要重新登录。\n您确定要退出登录吗？',
    enterEmail: '输入您的邮箱',
    enterPassword: '输入密码',
    enterEmailFirst: '请先输入您的邮箱地址',
  },

  // 设置
  settings: {
    title: '设置',
    sound: '声音',
    alarm: '闹钟',
    language: '语言',
    renewSubscription: '续订提醒',
    completeVoiceMessage: '完成语音消息',
    event: '活动提醒',
    noLateNightAlarms: '无深夜闹钟',
    notifications: '通知',
    darkMode: '深色模式',
    about: '关于',
    version: '版本',
    versionInfo: 'AIVgen 版本 {{version}}',
    privacyPolicy: '隐私政策',
    termsOfService: '服务条款',
    contactUs: '联系我们',
    deleteAccount: '删除账号',
    deleteAccountConfirm: '确定要删除您的账号吗？此操作无法撤销。',
    openSettings: '打开设置',
  },

  // 闹钟
  alarm: {
    setAlarm: '设置闹钟',
    editAlarm: '编辑闹钟',
    deleteAlarm: '删除闹钟',
    alarmTime: '闹钟时间',
    alarmSound: '闹钟声音',
    alarmVibration: '震动',
    alarmRepeat: '重复',
    alarmLabel: '标签',
    alarmSnooze: '贪睡',
    alarmVolume: '音量',
    alarmDays: '日期',
    alarmDeleteConfirm: '确定要删除这个闹钟吗？',
    alarmSaved: '闹钟保存成功',
    alarmDeleted: '闹钟删除成功',
    alarmTriggered: '闹钟',
    callIn5Minutes: '5分钟后再叫我',
    addVoiceAlarm: '添加语音闹钟',
    voiceAlarmList: '名人语音闹钟列表',
    noAlarms: '没有设置闹钟',
    notificationPermissionRequired: '需要通知权限',
    notificationPermissionMessage: '要设置闹钟，请在设备设置中允许通知。',
    failedToSetAlarm: '设置闹钟失败，请重试。',
    monday: '星期一',
    tuesday: '星期二',
    wednesday: '星期三',
    thursday: '星期四',
    friday: '星期五',
    saturday: '星期六',
    sunday: '星期日',
    everyday: '每天',
    weekdays: '工作日',
    weekends: '周末',
    never: '从不',
    am: '上午',
    pm: '下午',
    time: '时间',
    stop: '停止',
    noAlarmIdProvided: '未提供闹钟ID',
    alarmDataEmpty: '闹钟数据为空',
    failedToFetchAlarmData: '获取闹钟数据失败',
    fetchErrorTryAgain: '获取闹钟数据失败，请重试。',
    delayedAlarmRinging: '您延迟的闹钟正在响铃！',
  },

  // 收件箱
  inbox: {
    title: '收件箱',
    voiceAlarm: '语音闹钟',
    voiceMessage: '语音消息',
    coupon: '优惠券',
    celebVoiceAlarmList: '名人语音闹钟列表',
    celebVoiceMessageList: '名人语音消息列表',
    celebCouponList: '名人优惠券列表',
    noAlarms: '暂无闹钟',
    noVoiceMessages: '暂无语音消息',
    noCoupons: '暂无优惠券',
    addVoiceAlarm: '添加语音闹钟',
    addVoiceMessage: '添加语音消息',
    after: '制作中',
    download: '下载',
    reject: '拒绝',
  },

  // 消息
  message: {
    newMessage: '新消息',
    replyMessage: '回复',
    forwardMessage: '转发',
    deleteMessage: '删除',
    deleteMessageConfirm: '确定要删除这条消息吗？',
    messageSent: '消息发送成功',
    messageDeleted: '消息删除成功',
    noMessages: '没有消息',
    typeMessage: '输入消息...',
    voiceMessage: '语音消息',
    addVoiceMessage: '添加语音消息',
    voiceMessageList: '语音消息列表',
  },

  // 首页
  home: {
    welcome: '欢迎',
    featuredContent: '精选内容',
    trendingNow: '当前热门',
    newReleases: '最新发布',
    recommended: '为您推荐',
    categories: '分类',
    viewAll: '查看全部',
    celebrity: '名人',
    trend: '趋势',
  },

  // 名人
  celebrity: {
    title: '名人',
    category: '分类',
    all: '全部',
    actor: '演员',
    singer: '歌手',
    model: '模特',
    athlete: '运动员',
    comedian: '喜剧演员',
    influencer: '网红',
    other: '其他',
    trending: '热门',
    popular: '流行',
    new: '最新',
    favorite: '收藏',
    search: '搜索',
    noResults: '未找到结果',
    noFavorites: '暂无收藏',
    addToFavorites: '添加到收藏',
    removeFromFavorites: '从收藏中移除',
    viewProfile: '查看资料',
    viewAll: '查看全部',
    seeMore: '查看更多',
    subscribed: '已订阅',
    subscript: '订阅',
    defaultName: '名人',
    unknown: '未知',
    defaultTier: '等级 1',
    defaultBio: '你好，我正在参与一个使用人工智能复制我的声音的项目。',
    country: '国家',
    voiceAlarm: '名人语音闹钟',
    voiceMessage: '名人语音消息',
  },

  // 订阅
  subscription: {
    title: '订阅',
    currentPlan: '当前方案',
    upgradePlan: '升级方案',
    renewPlan: '续订方案',
    cancelPlan: '取消方案',
    planDetails: '方案详情',
    planExpires: '到期日',
    paymentMethod: '支付方式',
    changePlan: '更改方案',
    subscriptionHistory: '订阅历史',
    subscriptionCancelled: '订阅已成功取消',
    subscriptionRenewed: '订阅已成功续订',
    subscriptionUpgraded: '订阅已成功升级',
    freeTrialEnds: '免费试用结束于',
    subscribe: '订阅',
    subscribeNow: '立即订阅',
    monthly: '月度',
    yearly: '年度',
    lifetime: '终身',
    mostPopular: '最受欢迎',
    bestValue: '最佳价值',
    perMonth: '/月',
    perYear: '/年',
    oneTime: '一次性付款',
    savePercent: '节省 {{percent}}%',
    features: '功能',
    unlimitedAccess: '无限访问',
    noAds: '无广告',
    premiumContent: '高级内容',
    prioritySupport: '优先支持',
    upgrade: '升级',
    downgrade: '降级',
    failedToLoadPackageInfo: '加载套餐信息失败',
    noLowerTiers: '没有比一级套餐更低的套餐。',
    downgradeText1: '所选套餐将从下一个常规付款日期生效。如果您当前的套餐级别高于所选套餐，您必须在',
    downgradeText2: '新套餐开始前删除一些闹钟。',
    downgradeText3: '在',
    downgradeText4: '之后，如果您的闹钟数量超过可用的"闹钟槽"，最旧的闹钟将被自动删除。',
    confirmDowngrade: '您确定要继续降级吗？',
    loadingPackageInfo: '正在加载套餐信息...',
    generationTime: '生成时间',
    seconds: '秒',
    surcharge: '附加费',
    wantToDowngrade: '我想降级。',
    wantToUpgrade: '我想升级。',
    failedToGetPackageDetails: '获取套餐详情失败',
    errorFetchingPackageDetails: '获取套餐详情时发生错误',
    subscriptionSuccess: '订阅成功',
    subscriptionFailed: '订阅失败',
    errorDuringSubscription: '订阅过程中发生错误',
    beingProcessed: '处理中...',
    subscriptionIdEmpty: '订阅ID不能为空',
    productNotFound: '未找到对应的订阅产品',
    userCancelled: '用户取消了购买',
    purchaseError: '购买过程中出现错误: ',
    alreadyOwnedButNotFound: '您已拥有此订阅，但无法找到相关购买记录',
    alreadyOwnedProcessError: '处理已有订阅时出错',
  },

  // FAQ
  faq: {
    membership: '会员',
    voiceAlarm: '语音闹钟',
    voiceMessage: '语音消息',
    noMembershipFAQs: '暂无会员常见问题',
    noVoiceAlarmFAQs: '暂无语音闹钟常见问题',
    noVoiceMessageFAQs: '暂无语音消息常见问题',
    noAnswerAvailable: '暂无回答',
    failedToLoad: '加载常见问题失败',
  },

  // 排序
  sorting: {
    title: '排序',
    byCategory: '分类',
    byName: '名称',
    byFavorite: '收藏',
    bySubscription: '订阅',
    byVoiceMessage: '语音消息',
  },

  // 公告
  notice: {
    failedToLoad: '加载公告失败',
    noNotices: '暂无公告',
  },

  // 活动
  event: {
    failedToLoad: '加载活动失败',
    noEvents: '暂无活动',
  },

  // 关于
  about: {
    aboutAivgen: '关于 AIVgen',
    description: 'AIVgen 是一款语音闹钟和消息应用，允许您设置闹钟并使用名人声音发送消息。我们的应用程序使用先进的人工智能技术创建逼真的声音模拟，以提供更好的用户体验。',
    contactUs: '联系我们',
    email: '邮箱',
    website: '网站',
    copyright: '© 2024 AIVgen. 保留所有权利。',
  },

  // 搜索
  search: {
    placeholder: '搜索',
    celebNameSearch: '搜索名人',
    noResults: '未找到结果',
    searchResults: '搜索结果',
    searchHistory: '搜索历史',
    clearHistory: '清除历史',
    trending: '热门搜索',
  },

  // 通知
  notifications: {
    allowText: '允许',
    toSendNotification: '向您发送通知？',
    allow: '允许',
    dontAllow: '不允许',
    permissionRequired: '需要通知权限',
    permissionMessage: '要设置闹钟，请在设备设置中允许通知。',
  },

  // 错误消息
  errors: {
    somethingWentWrong: '出错了，请重试。',
    networkError: '网络错误，请检查您的连接。',
    serverError: '服务器错误，请稍后重试。',
    unauthorized: '未授权，请重新登录。',
    forbidden: '您没有权限访问此资源。',
    notFound: '资源未找到。',
    timeout: '请求超时，请重试。',
    unknown: '发生未知错误。',
    failedToFetchData: '获取数据失败',
    errorFetchingData: '获取数据时发生错误',
    noResponseFromApi: '未收到API响应',
  },

  // 个人资料
  profile: {
    myProfile: '我的资料',
    birthday: '生日',
    year: '年',
    month: '月',
    day: '日',
    changePassword: '修改密码',
    enterYourNickname: '输入您的昵称',
    passwordRequirement: '请输入最多15个字符，使用大小写字母、数字和特殊字符的组合。',
    failedToGetUserInfo: '获取用户信息失败',
    errorFetchingUserInfo: '获取用户信息时发生错误',
    updateSuccess: '更新成功',
    updateFailed: '更新失败',
    errorUpdating: '更新时发生错误',
    nicknameCannotBeEmpty: '昵称不能为空',
    nicknameTooLong: '昵称不能超过15个字符',
    permissionDenied: '权限被拒绝',
    photoLibraryPermissionMessage: '需要访问相机和相册的权限',
    goToSettings: '去设置',
    errorSelectingImage: '选择图片时出错',
    failedToUploadImage: '上传图片失败',
    errorUploadingImage: '上传图片时发生错误',
    avatarUpdateSuccess: '头像更新成功',
    failedToUpdateAvatar: '更新头像失败',
    selectImageSource: '选择图片来源',
    chooseSource: '请选择图片来源',
    camera: '相机',
    gallery: '相册',
  },

  // 密码
  password: {
    enterCurrentPassword: '请输入当前密码',
    enterNewPassword: '请输入新密码',
    confirmNewPassword: '请确认新密码',
    passwordsDoNotMatch: '新密码不匹配',
    lengthRequirement: '密码长度必须为8至24个字符',
    requireLowercaseAndNumbers: '密码必须包含小写字母和数字',
    userInfoNotFound: '未找到用户信息，请重新登录。',
    changeSuccess: '密码修改成功',
    changeFailed: '密码修改失败',
    errorOccurred: '发生错误，请重试。',
    enterCurrentPasswordLabel: '输入当前密码',
    enterPasswordLabel: '输入密码',
    length: '长度',
    lengthValue: '8至24个字符',
    requiredCondition: '必要条件',
    requiredConditionValue: '小写字母，数字',
    optionalCondition: '可选条件',
    optionalConditionValue: '特殊字符',
    accept: '确认',
  },

  // 注册
  signup: {
    title: '注册',
    enterEmail: '输入您的邮箱',
    enterPassword: '输入密码',
    confirmPasswordPlaceholder: '请再次输入密码',
    enterCorrectEmail: '请输入正确的邮箱地址。',
    passwordMinLength: '密码长度必须至少为8个字符。',
    passwordsDoNotMatch: '密码不匹配。',
    passwordsMatch: '密码匹配成功',
    configuration: '配置',
    configurationValue: '小写字母，大写字母，数字，特殊字符',
    length: '长度',
    lengthValueEmail: '最多15个字符',
    lengthValuePassword: '8至24个字符',
    requiredCondition: '必要条件',
    requiredConditionValue: '小写字母，数字',
    optionalCondition: '可选条件',
    optionalConditionValue: '特殊字符',
    enterNickname: '输入您的昵称',
    enterBirthday: '输入您的生日',
    optional: '(可选)',
    nicknameMinLength: '昵称长度必须至少为3个字符。',
    registrationSuccessful: '注册成功！',
    registrationFailed: '注册失败',
    registrationFailedTryAgain: '注册失败，请重试。',
  },

  // 通用
  common: {
    ok: '确定',
    cancel: '取消',
    confirm: '确认',
    save: '保存',
    delete: '删除',
    edit: '编辑',
    loading: '加载中...',
    error: '错误',
    success: '成功',
    retry: '重试',
    back: '返回',
    next: '下一步',
    search: '搜索',
    noData: '暂无数据',
    seeAll: '查看全部',
    close: '关闭',
    send: '发送',
    returnToHome: '返回首页',
    yes: '是',
    no: '否',
    pleaseLoginFirst: '请先登录',
    home: '首页',
    notice: '提示',
    unknownError: '未知错误',
  },

  // 设置
  settings: {
    title: '设置',
    language: '语言',
    selectLanguage: '选择语言',
    notifications: '通知',
    darkMode: '深色模式',
    account: '账户',
    about: '关于',
    logout: '退出登录',
    version: '版本',
    openSettings: '打开设置',
    sound: '声音',
    alarm: '闹钟',
    renewSubscription: '续订提醒',
    completeVoiceMessage: '完成语音消息',
    event: '活动',
    noLateNightAlarms: '无深夜闹钟',
    failedToLoadSettings: '加载设置信息失败',
    errorLoadingSettingsTryAgain: '加载设置信息失败，请稍后重试',
    failedToChangeSettings: '更改设置失败',
    errorChangingSettingsTryAgain: '更改设置失败，请稍后重试',
    languageUpdatedSuccess: '语言更新成功',
    languageUpdateFailed: '更新语言偏好失败',
  },

  // 个人资料
  profile: {
    myProfile: '我的资料',
    birthday: '生日',
    year: '年',
    month: '月',
    day: '日',
    changePassword: '修改密码',
    enterYourNickname: '输入您的昵称',
    passwordRequirement: '密码必须至少8个字符长，并包含至少一个大写字母、一个小写字母、一个数字和一个特殊字符。',
    failedToGetUserInfo: '获取用户信息失败',
    errorFetchingUserInfo: '获取用户信息时发生错误',
    updateSuccess: '更新成功',
    updateFailed: '更新失败',
    errorUpdating: '更新信息时发生错误',
    nicknameCannotBeEmpty: '昵称不能为空',
    permissionDenied: '权限被拒绝',
    photoLibraryPermissionMessage: '要上传个人资料图片，请在设备设置中允许访问您的照片库。',
    goToSettings: '前往设置',
    errorSelectingImage: '选择图片时出错',
    failedToUploadImage: '上传图片失败',
    errorUploadingImage: '上传图片时发生错误',
    avatarUpdateSuccess: '头像更新成功',
    failedToUpdateAvatar: '更新头像失败',
  },

  // 密码
  password: {
    enterCurrentPassword: '请输入当前密码',
    enterNewPassword: '请输入新密码',
    confirmNewPassword: '请确认新密码',
    passwordsDoNotMatch: '两次输入的密码不匹配',
    lengthRequirement: '密码长度必须在8到24个字符之间',
    requireLowercaseAndNumbers: '密码必须包含小写字母和数字',
    userInfoNotFound: '未找到用户信息',
    changeSuccess: '密码修改成功',
    changeFailed: '密码修改失败',
    errorOccurred: '修改密码时发生错误',
    enterCurrentPasswordLabel: '输入当前密码',
    enterPasswordLabel: '输入密码',
    length: '长度',
    lengthValue: '8-24个字符',
    requiredCondition: '必需',
    requiredConditionValue: '小写字母和数字',
    optionalCondition: '可选',
    optionalConditionValue: '大写字母和特殊字符',
    accept: '确认',
  },

  // 认证
  auth: {
    signIn: '登录',
    enterEmail: '输入邮箱',
    enterPassword: '输入密码',
    forgotPassword: '忘记密码？',
    invalidEmail: '请输入有效的邮箱地址',
    passwordTooShort: '密码长度至少为6个字符',
    enterEmailFirst: '请先输入您的邮箱',
    resetPassword: '重置密码',
    resetPasswordMessage: '我们将发送密码重置链接到您的邮箱',
    send: '发送',
    cancel: '取消',
    resetPasswordSuccess: '密码重置链接已发送到您的邮箱',
    resetPasswordFailed: '发送密码重置链接失败',
    signUp: '注册',
    signInWithAivgen: '使用AIVGEN登录',
    signInWithGoogle: '使用Google登录',
    signInWithFacebook: '使用Facebook登录',
    loginSuccess: '登录成功',
    loginFailed: '登录失败',
    errorProcessingGoogleLogin: '处理Google登录时出错',
    googlePlayServicesNotAvailable: 'Google Play服务不可用',
    loginFailedWithError: '登录失败: {{error}}',
    facebookLoginInDevelopment: 'Facebook登录功能正在开发中',
  },

  // 忘记密码
  forgotPassword: {
    newPasswordSent: '新密码将发送至',
    toEmailBelow: '以下邮箱',
    checkEmail: '请查看您的邮箱获取验证码',
    codeSentSuccess: '验证码发送成功',
    codeSentFailed: '验证码发送失败',
    codeError: '发送验证码时发生错误',
  },

  // 搜索
  search: {
    search: '搜索',
    searchPlaceholder: '搜索名人',
    noResults: '无搜索结果',
    unableToFindCelebrity: '无法搜索到该名人',
    resultsFor: '搜索结果："{{keyword}}"',
    searchHistory: '搜索历史',
    clearHistory: '清除历史',
    recentSearches: '最近搜索',
    trending: '热门搜索',
  },

  // 名人
  celeb: {
    selectCeleb: '选择名人',
    selectCelebForVoiceMessage: '选择名人添加语音消息',
    selectCelebForVoiceAlarm: '选择名人添加语音闹钟',
    total: '全部',
    musician: '音乐家',
    actor: '演员',
    broadcaster: '主播',
    sports: '体育',
    errorFetchingCelebs: '获取名人数据时出错',
    noCelebsFound: '未找到名人',
  },

  // 日期选择器
  datePicker: {
    selectYearOfBirth: '选择出生年份',
    year: '年',
    month: '月',
    day: '日',
    submit: '确认',
  },

  // 全文模态框
  fullTextModal: {
    agreement: '协议',
  },

  // 分享
  share: {
    selectSharingMethod: '请选择分享方式',
    shareToFacebook: '分享到Facebook',
    shareToTwitter: '分享到Twitter',
    shareToInstagram: '分享到Instagram',
    shareToWhatsApp: '分享到WhatsApp',
    shareToLine: '分享到Line',
    shareToWechat: '分享到微信',
    shareToKakaoTalk: '分享到KakaoTalk',
    copyLink: '复制链接',
    linkCopied: '链接已复制到剪贴板',
  },

  // Toast消息
  toast: {
    success: '成功',
    error: '错误',
    info: '提示',
    warning: '警告',
  },

  // 注册
  signup: {
    title: '注册',
    enterEmail: '输入邮箱',
    enterPassword: '输入密码',
    confirmPasswordPlaceholder: '确认密码',
    passwordsMatch: '密码匹配',
    passwordsDoNotMatch: '密码不匹配',
    enterCorrectEmail: '请输入有效的邮箱地址',
    passwordMinLength: '密码长度至少为8个字符',
    configuration: '格式',
    configurationValue: '<EMAIL>',
    length: '长度',
    lengthValueEmail: '最多50个字符',
    lengthValuePassword: '8-24个字符',
    requiredCondition: '必需',
    requiredConditionValue: '小写字母和数字',
    optionalCondition: '可选',
    optionalConditionValue: '大写字母和特殊字符',
    agreement: '协议',
    agreeToTerms: '我同意服务条款和隐私政策',
    termsOfService: '服务条款',
    privacyPolicy: '隐私政策',
    pleaseAgreeToTerms: '请同意服务条款和隐私政策',
    createProfile: '创建个人资料',
    nickname: '昵称',
    birthday: '生日',
    year: '年',
    month: '月',
    day: '日',
    signUpSuccess: '注册成功',
    signUpFailed: '注册失败',
    errorOccurred: '注册过程中发生错误',
  },

  // 底部标签栏
  tabBar: {
    home: '首页',
    subscript: '订阅',
    inbox: '收件箱',
    my: '我的',
  },

  // 订阅
  subscription: {
    title: '订阅',
    upgrade: '升级',
    downgrade: '降级',
    failedToLoadPackageInfo: '加载套餐信息失败',
    noLowerTiers: '没有比一级套餐更低的套餐。',
    downgradeText1: '所选套餐将从下一个常规付款日期生效。如果您当前的套餐级别高于所选套餐，您必须在',
    downgradeText2: '新套餐开始前删除一些闹钟。',
    downgradeText3: '在',
    downgradeText4: '之后，如果您的闹钟数量超过可用的"闹钟槽"，最旧的闹钟将被自动删除。',
    confirmDowngrade: '您确定要继续降级吗？',
    loadingPackageInfo: '正在加载套餐信息...',
    generationTime: '生成时间',
    seconds: '秒',
    surcharge: '附加费',
    wantToDowngrade: '我想降级。',
    wantToUpgrade: '我想升级。',
    failedToGetPackageDetails: '获取套餐详情失败',
    errorFetchingPackageDetails: '获取套餐详情时发生错误',
    subscriptionSuccess: '订阅成功',
    subscriptionFailed: '订阅失败',
    errorDuringSubscription: '订阅过程中发生错误',
    beingProcessed: '处理中...',
    subscriptionIdEmpty: '订阅ID不能为空',
    productNotFound: '未找到对应的订阅产品',
    userCancelled: '用户取消了购买',
    purchaseError: '购买过程中出现错误: ',
    alreadyOwnedButNotFound: '您已拥有此订阅，但无法找到相关购买记录',
    alreadyOwnedProcessError: '处理已有订阅时出错',
  },

  // 协议
  agreement: {
    termsOfUse: '使用条款协议',
    agree: '同意',
    viewFullText: '查看全文',
    personalInfoCollection: '个人信息收集',
    andUsageAgreement: '和使用协议',
    privacyPolicy: '隐私政策',
  },

  // 语音消息
  voiceMessage: {
    celebVoiceAlarmGenerating: '您的名人语音闹钟正在生成中。',
    createVoiceMessage: '创建语音消息。',
    failedToFetchMessageDetails: '获取消息详情失败',
    errorFetchingMessageDetails: '获取消息详情时发生错误',
    loadingVoiceMessage: '正在加载语音消息...',
    alarmReadySoon: '您的闹钟即将准备就绪\n可在闹钟列表中找到',
    voiceMessagesInInbox: '语音消息可在收件箱中找到',
    after: '剩余',
    goToAlarmList: '前往闹钟列表',
    goToInbox: '前往收件箱',
    title: '语音消息',
    enterTitleAtLeastOneLetter: '请至少输入一个字符的标题。',
    apply: '应用',
    backgroundConfirmMessage: '名人语音消息背景已设置为\n所选照片。\n您想应用它吗？',
    messageConfirmMessage: '名人语音消息已设置为\n所选消息列表。\n您想应用它吗？',
    message: '消息',
    random: '随机',
    clickMessageHint: '点击您想要生成名人语音消息的消息。',
    enterMessagePlaceholder: '请输入消息',
    background: '背景',
    clickPhotoHint: '点击您想要与语音消息一起使用的背景照片。',
    timeTypePrice: '时间类型（价格）',
    noTimeTypeData: '暂无时间类型数据',
    failedToGetData: '获取数据失败',
    errorFetchingData: '获取数据时出错',
    failedToLoadPolicy: '加载政策失败',
    errorLoadingPolicy: '加载政策时出错',
    pleaseSelectPackage: '请选择套餐',
    productIdCannotBeEmpty: '产品ID不能为空',
    productNotFound: '未找到产品',
    createSuccess: '创建成功',
    saveFailed: '保存失败',
    userCancelledPurchase: '用户取消购买',
    purchaseError: '购买过程中出错: {{error}}',
    generalPurchaseError: '购买和创建语音消息过程中出错',
    selectPrice: '选择价格',
    productPolicy: '产品政策',
    alreadyOwnedButNotFound: '您已拥有此商品，但无法找到相关购买记录',
    alreadyOwnedProcessError: '处理已有购买时出错',
    failedToGetData: '获取数据失败',
    errorFetchingData: '获取数据时发生错误',
    errorFetchingCelebrityData: '获取名人数据时发生错误',
    failedToLoadPolicy: '加载产品政策内容失败。',
    errorLoadingPolicy: '加载产品政策内容时发生错误。',
    selectVoiceMessage: '选择语音消息',
    writeItYourself: '自己编写',
    makeRecommendMessage: '使用推荐消息',
    enterNameAtLeastOneLetter: '请至少输入一个字符的名称。',
    failedToGetSceneTreeData: '获取场景树数据失败',
    to: '收件人',
    enterNameOrNickname: '输入接收消息的人的\n姓名或昵称',
    selectOccasion: '选择场合',
    messageIdNotExist: '消息ID不存在',
    checkOutVoiceMessage: '查看来自{{name}}的语音消息！',
    failedToShare: '分享失败',
    messageDeletedSuccessfully: '消息已成功删除',
    failedToDeleteMessage: '删除消息失败',
    errorDeletingMessage: '删除消息时发生错误',
    share: '分享',
    deleteConfirmationMessage: '删除此项将永久移除\n语音消息。您确定\n要继续删除吗？',
  },

  // 日志消息
  logs: {
    callingVoiceMessageListAPI: '调用语音消息列表接口，参数:',
    voiceMessageListResponse: '语音消息列表接口响应:',
    alarmListResponse: '闹钟列表响应:',
    stoppingSoundPlayback: '停止音频播放',
    errorStoppingSoundPlayback: '停止音频播放时出错:',
    stoppingVibration: '停止震动',
    pausingSoundPlaybackForDelay: '暂停音频播放以延迟',
    stoppingVibrationForDelay: '停止震动以延迟',
    schedulingAlarmIn5Minutes: '安排闹钟在5分钟后再次触发',
    delayedAlarmScheduledSuccessfully: '延迟闹钟已成功安排',
    errorSchedulingDelayedAlarm: '安排延迟闹钟时出错:',
    errorFetchingCelebrityData: '获取名人数据时出错:',
    errorGettingUserInfo: '获取用户信息时出错:',
  },

  // 闹钟
  alarm: {
    failedToGetData: '获取数据失败',
    errorFetchingData: '获取数据时发生错误',
    notificationPermissionRequired: '需要通知权限',
    notificationPermissionMessage: '闹钟功能需要通知权限才能正常工作。请在设备设置中启用通知。',
    noAlarmIdProvided: '未提供闹钟ID',
    alarmDataEmpty: '闹钟数据为空',
    failedToFetchAlarmData: '获取闹钟数据失败',
    fetchErrorTryAgain: '获取闹钟数据时出错。请重试。',
    time: '时间',
    stop: '停止',
    callIn5Minutes: '5分钟后再叫我',
    alarmTriggered: '闹钟已触发',
    delayedAlarmRinging: '您的延迟闹钟正在响铃',
    enterTitleAtLeastOneLetter: '请至少输入一个字符的标题。',
    enterMessageAtLeastOneLetter: '请至少输入一个字符的消息。',
    selectAtLeastOneDay: '请至少选择一天。',
    alarmCreatedSuccessfully: '闹钟创建成功！',
    failedToCreateAlarm: '创建闹钟失败。请重试。',
    errorCreatingAlarm: '创建闹钟时发生错误。请重试。',
    celebVoiceAlarm: '名人语音闹钟',
    apply: '应用',
    backgroundConfirmMessage: '名人语音闹钟的背景已设置为\n所选照片。\n您想应用它吗？',
    alarmDeletedSuccessfully: '闹钟删除成功！',
    failedToDeleteAlarm: '删除闹钟失败。请重试。',
    errorDeletingAlarm: '删除闹钟时发生错误。请重试。',
    alarmUpdatedSuccessfully: '闹钟更新成功！',
    failedToUpdateAlarm: '更新闹钟失败。请重试。',
    errorUpdatingAlarm: '更新闹钟时发生错误。请重试。',
    editVoiceAlarm: '编辑语音闹钟',
    title: '标题',
    enterTitle: '输入标题',
    week: '星期',
    deleteConfirmationMessage: '删除此项将永久移除闹钟，\n且可创建的闹钟数量将不会恢复。\n您确定要继续删除吗？',
  },
};
