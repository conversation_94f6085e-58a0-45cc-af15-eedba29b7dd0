export default {
  // 菜单
  menu: {
    notice: 'NOTICE',
    event: 'EVENT',
    faq: 'FAQ',
    about: 'ABOUT',
    setting: 'SETTING',
  },

  // 通用
  common: {
    ok: 'OK',
    cancel: 'Cancel',
    confirm: 'Confirm',
    save: 'Save',
    delete: 'Delete',
    edit: 'Edit',
    loading: 'Loading...',
    error: 'Error',
    success: 'Success',
    retry: 'Retry',
    back: 'Back',
    next: 'Next',
    search: 'Search',
    noData: 'No data available',
    seeAll: 'See All',
    close: 'Close',
    send: 'SEND',
    returnToHome: 'Return to Home',
    yes: 'Yes',
    no: 'No',
    pleaseLoginFirst: 'Please login first',
    home: 'HOME',
    notice: 'Notice',
    unknownError: 'Unknown error',
  },


  // 导航
  navigation: {
    home: 'HOME',
    alarm: 'ALARM',
    message: 'MESSAGE',
    inbox: 'INBOX',
    menu: 'MENU',
    celebrity: 'CELEBRITY',
    subscription: 'SUBSCRIPTION',
    my: 'MY',
    faq: 'FAQ',
  },

  // 认证
  auth: {
    login: 'Login',
    signup: 'Sign Up',
    logout: 'Logout',
    signIn: 'SIGN IN',
    signOut: 'SIGN OUT',
    email: 'Email',
    password: 'Password',
    confirmPassword: 'Confirm Password',
    forgotPassword: 'I FORGOT MY PASSWORD.',
    resetPassword: 'Reset Password',
    loginWithGoogle: 'Login with Google',
    loginWithApple: 'Login with Apple',
    orLoginWith: 'Or login with',
    dontHaveAccount: 'Don\'t have an account?',
    alreadyHaveAccount: 'Already have an account?',
    signupNow: 'Sign up now',
    loginNow: 'Login now',
    agreeToTerms: 'I agree to the Terms of Service and Privacy Policy',
    passwordMismatch: 'Passwords do not match',
    invalidEmail: 'Invalid email address',
    passwordTooShort: 'Password must be at least 6 characters',
    loginFailed: 'Login failed. Please check your credentials.',
    signupFailed: 'Sign up failed. Please try again.',
    logoutConfirm: 'Are you sure you want to log out?',
    signOutConfirmMessage: 'If you sign out, you must sign in again\nwhen running the app.\nWould you like to sign out?',
    enterEmail: 'ENTER YOUR E-MAIL',
    enterPassword: 'ENTER PASSWORD',
    enterEmailFirst: 'Please enter your email address.',
    resetPasswordMessage: 'We will send a password reset link to your email',
    send: 'SEND',
    cancel: 'CANCEL',
    resetPasswordSuccess: 'Password reset link has been sent to your email',
    resetPasswordFailed: 'Failed to send password reset link',
    signUp: 'SIGN UP',
    signInWithAivgen: 'SIGN IN with AIVGEN',
    signInWithGoogle: 'SIGN IN with Google',
    signInWithFacebook: 'SIGN IN with Facebook',
    loginSuccess: 'Login successful',
    errorProcessingGoogleLogin: 'Error processing Google login',
    googlePlayServicesNotAvailable: 'Google Play services not available',
    loginFailedWithError: 'Login failed: {{error}}',
    facebookLoginInDevelopment: 'Facebook login feature is under development',
  },


  // 收件箱
  inbox: {
    title: 'INBOX',
    voiceAlarm: 'VOICE ALARM',
    voiceMessage: 'VOICE MESSAGE',
    coupon: 'COUPON',
    celebVoiceAlarmList: 'CELEB VOICE ALARM LIST',
    celebVoiceMessageList: 'CELEB VOICE MESSAGE LIST',
    celebCouponList: 'CELEB COUPON LIST',
    noAlarms: 'No alarms',
    noVoiceMessages: 'No voice messages',
    noCoupons: 'No coupons',
    addVoiceAlarm: 'Add voice alarm',
    addVoiceMessage: 'Add voice message',
    after: 'AFTER',
    download: 'DOWNLOAD',
    reject: 'REJECT',
  },

  // 消息
  message: {
    newMessage: 'New Message',
    replyMessage: 'Reply',
    forwardMessage: 'Forward',
    deleteMessage: 'Delete',
    deleteMessageConfirm: 'Are you sure you want to delete this message?',
    messageSent: 'Message sent successfully',
    messageDeleted: 'Message deleted successfully',
    noMessages: 'No messages',
    typeMessage: 'Type a message...',
    voiceMessage: 'Voice Message',
    addVoiceMessage: 'Add Voice Message',
    voiceMessageList: 'Voice Message List',
  },

  // 首页
  home: {
    welcome: 'Welcome',
    featuredContent: 'Featured Content',
    trendingNow: 'Trending Now',
    newReleases: 'New Releases',
    recommended: 'Recommended for You',
    categories: 'Categories',
    viewAll: 'View All',
    celebrity: 'CELEBRITY',
    trend: 'TREND',
  },

  // 名人
  celebrity: {
    title: 'CELEBRITY',
    category: 'Category',
    all: 'ALL',
    actor: 'ACTOR',
    singer: 'SINGER',
    model: 'MODEL',
    athlete: 'ATHLETE',
    comedian: 'COMEDIAN',
    influencer: 'INFLUENCER',
    other: 'OTHER',
    trending: 'TRENDING',
    popular: 'POPULAR',
    new: 'NEW',
    favorite: 'FAVORITE',
    search: 'Search',
    noResults: 'No results found',
    noFavorites: 'No favorites yet',
    addToFavorites: 'Add to favorites',
    removeFromFavorites: 'Remove from favorites',
    viewProfile: 'View Profile',
    viewAll: 'View All',
    seeMore: 'See More',
    subscribed: 'Subscribed',
    subscript: 'Not Subscribed',
    defaultName: 'Celebrity',
    unknown: 'Unknown',
    defaultTier: 'Tier 1',
    defaultBio: 'Hello, I am participating in a project to reproduce my voice using AI.',
    country: 'Country',
    voiceAlarm: 'CELEB VOICE ALARM',
    voiceMessage: 'CELEB VOICE MESSAGE',
  },

  // FAQ
  faq: {
    membership: 'MEMBERSHIP',
    voiceAlarm: 'VOICE ALARM',
    voiceMessage: 'VOICE MESSAGE',
    noMembershipFAQs: 'No membership FAQs available',
    noVoiceAlarmFAQs: 'No voice alarm FAQs available',
    noVoiceMessageFAQs: 'No voice message FAQs available',
    noAnswerAvailable: 'No answer available',
    failedToLoad: 'Failed to load FAQ data',
  },

  // 排序
  sorting: {
    title: 'SORTING',
    byCategory: 'CATEGORY',
    byName: 'NAME',
    byFavorite: 'FAVORITE',
    bySubscription: 'SUBSCRIPTION',
    byVoiceMessage: 'VOICE MESSAGE',
  },

  // 公告
  notice: {
    failedToLoad: 'Failed to load notices',
    noNotices: 'No notices available',
  },

  // 活动
  event: {
    failedToLoad: 'Failed to load events',
    noEvents: 'No events available',
  },

  // 关于
  about: {
    aboutAivgen: 'ABOUT AIVGEN',
    description: 'AIVgen is a voice alarm and message application that allows you to set alarms and send messages with celebrity voices. Our application uses advanced AI technology to create realistic voice simulations for an enhanced user experience.',
    contactUs: 'CONTACT US',
    email: 'Email',
    website: 'Website',
    copyright: '© 2024 AIVgen. All rights reserved.',
  },

  // 通知
  notifications: {
    allowText: 'Allow',
    toSendNotification: 'to send you notification?',
    allow: 'Allow',
    dontAllow: 'Don\'t allow',
    permissionRequired: 'Notification Permission Required',
    permissionMessage: 'To set alarms, please allow notifications in your device settings.',
  },

  // 错误消息
  errors: {
    somethingWentWrong: 'Something went wrong. Please try again.',
    networkError: 'Network error. Please check your connection.',
    serverError: 'Server error. Please try again later.',
    unauthorized: 'Unauthorized. Please log in again.',
    forbidden: 'You don\'t have permission to access this resource.',
    notFound: 'Resource not found.',
    timeout: 'Request timeout. Please try again.',
    unknown: 'Unknown error occurred.',
    failedToFetchData: 'Failed to fetch data',
    errorFetchingData: 'Error fetching data',
    noResponseFromApi: 'No response received from API',
  },


  // 设置
  settings: {
    title: 'Settings',
    language: 'Language',
    selectLanguage: 'Select Language',
    notifications: 'Notifications',
    darkMode: 'Dark Mode',
    account: 'Account',
    about: 'About',
    logout: 'Logout',
    version: 'Version',
    openSettings: 'Open Settings',
    sound: 'Sound',
    alarm: 'Alarm',
    renewSubscription: 'Renew Subscription',
    completeVoiceMessage: 'Complete Voice Message',
    event: 'Event',
    noLateNightAlarms: 'No Late Night Alarms',
    failedToLoadSettings: 'Failed to load settings',
    errorLoadingSettingsTryAgain: 'Failed to load settings, please try again later',
    failedToChangeSettings: 'Failed to change settings',
    errorChangingSettingsTryAgain: 'Failed to change settings, please try again later',
    languageUpdatedSuccess: 'Language updated successfully',
    languageUpdateFailed: 'Failed to update language preference',
    versionInfo: 'AIVgen Ver {{version}}',
    privacyPolicy: 'Privacy Policy',
    termsOfService: 'Terms of Service',
    contactUs: 'Contact Us',
    deleteAccount: 'Delete Account',
    deleteAccountConfirm: 'Are you sure you want to delete your account? This action cannot be undone.',
  },

  // 个人资料
  profile: {
    myProfile: 'MY PROFILE',
    birthday: 'BIRTHDAY',
    year: 'YEAR',
    month: 'MONTH',
    day: 'DAY',
    changePassword: 'CHANGE PASSWORD',
    enterYourNickname: 'Enter your nickname',
    passwordRequirement: 'Enter up to 15 characters using a combination of upper and lower case letters, numbers, and special characters.',
    failedToGetUserInfo: 'Failed to get user information',
    errorFetchingUserInfo: 'Error fetching user information',
    updateSuccess: 'Update successful',
    updateFailed: 'Update failed',
    errorUpdating: 'Error updating information',
    nicknameCannotBeEmpty: 'Nickname cannot be empty',
    nicknameTooLong: 'Nickname cannot exceed 15 characters',
    permissionDenied: 'Permission Denied',
    photoLibraryPermissionMessage: 'Camera and photo library access required',
    goToSettings: 'Go to Settings',
    errorSelectingImage: 'Error selecting image',
    failedToUploadImage: 'Failed to upload image',
    errorUploadingImage: 'Error uploading image',
    avatarUpdateSuccess: 'Avatar updated successfully',
    failedToUpdateAvatar: 'Failed to update avatar',
    selectImageSource: 'Select Image Source',
    chooseSource: 'Choose image source',
    camera: 'Camera',
    gallery: 'Gallery',
  },

  // 密码
  password: {
    enterCurrentPassword: 'Please enter your current password',
    enterNewPassword: 'Please enter your new password',
    confirmNewPassword: 'Please confirm your new password',
    passwordsDoNotMatch: 'Passwords do not match',
    lengthRequirement: 'Password must be between 8 and 24 characters',
    requireLowercaseAndNumbers: 'Password must contain lowercase letters and numbers',
    userInfoNotFound: 'User information not found',
    changeSuccess: 'Password changed successfully',
    changeFailed: 'Failed to change password',
    errorOccurred: 'An error occurred while changing password',
    enterCurrentPasswordLabel: 'ENTER CURRENT PASSWORD',
    enterPasswordLabel: 'ENTER PASSWORD',
    length: 'Length',
    lengthValue: '8-24 characters',
    requiredCondition: 'Required',
    requiredConditionValue: 'Lowercase letters and numbers',
    optionalCondition: 'Optional',
    optionalConditionValue: 'Uppercase letters and special characters',
    accept: 'ACCEPT',
  },


  // 忘记密码
  forgotPassword: {
    newPasswordSent: 'NEW PASSWORD WILL BE SENT',
    toEmailBelow: 'TO THE EMAIL BELOW',
    checkEmail: 'Please check your email for the verification code',
    codeSentSuccess: 'Verification code sent successfully',
    codeSentFailed: 'Failed to send verification code',
    codeError: 'An error occurred while sending the verification code',
  },

  // 搜索
  search: {
    search: 'Search',
    searchPlaceholder: 'Search for celebrities',
    noResults: 'NO RESULTS',
    unableToFindCelebrity: 'Unable to search for the celebrity',
    resultsFor: 'RESULTS FOR "{{keyword}}"',
    searchHistory: 'Search History',
    clearHistory: 'Clear History',
    recentSearches: 'Recent Searches',
    trending: 'Trending',
    placeholder: 'Search',
    celebNameSearch: 'Celeb name search',
    searchResults: 'Search Results',
  },

  // 名人
  celeb: {
    selectCeleb: 'SELECT CELEBRITY',
    selectCelebForVoiceMessage: 'SELECT A CELEB TO ADD VOICE MESSAGE',
    selectCelebForVoiceAlarm: 'SELECT A CELEB TO ADD VOICE ALARM',
    total: 'TOTAL',
    musician: 'MUSICIAN',
    actor: 'ACTOR',
    broadcaster: 'BROADCASTER',
    sports: 'SPORTS',
    errorFetchingCelebs: 'Error fetching celebrity data',
    noCelebsFound: 'No celebrities found',
  },

  // 日期选择器
  datePicker: {
    selectYearOfBirth: 'SELECT YEAR OF BIRTH',
    year: 'Year',
    month: 'Month',
    day: 'Day',
    submit: 'Submit',
  },

  // 全文模态框
  fullTextModal: {
    agreement: 'Agreement',
  },

  // 分享
  share: {
    selectSharingMethod: 'Please select a sharing method.',
    shareToFacebook: 'Share to Facebook',
    shareToTwitter: 'Share to Twitter',
    shareToInstagram: 'Share to Instagram',
    shareToWhatsApp: 'Share to WhatsApp',
    shareToLine: 'Share to Line',
    shareToWechat: 'Share to WeChat',
    shareToKakaoTalk: 'Share to KakaoTalk',
    copyLink: 'Copy Link',
    linkCopied: 'Link copied to clipboard',
  },

  // Toast消息
  toast: {
    success: 'Success',
    error: 'Error',
    info: 'Info',
    warning: 'Warning',
  },

  // 注册
  signup: {
    title: 'SIGN UP',
    enterEmail: 'ENTER EMAIL',
    enterPassword: 'ENTER PASSWORD',
    confirmPasswordPlaceholder: 'Confirm Password',
    passwordsMatch: 'Passwords match',
    passwordsDoNotMatch: 'Passwords do not match',
    enterCorrectEmail: 'Please enter a valid email address',
    passwordMinLength: 'Password must be at least 8 characters',
    configuration: 'Configuration',
    configurationValue: '<EMAIL>',
    length: 'Length',
    lengthValueEmail: 'Maximum 50 characters',
    lengthValuePassword: '8-24 characters',
    requiredCondition: 'Required',
    requiredConditionValue: 'Lowercase letters and numbers',
    optionalCondition: 'Optional',
    optionalConditionValue: 'Uppercase letters and special characters',
    agreement: 'AGREEMENT',
    agreeToTerms: 'I agree to the Terms of Service and Privacy Policy',
    termsOfService: 'Terms of Service',
    privacyPolicy: 'Privacy Policy',
    pleaseAgreeToTerms: 'Please agree to the Terms of Service and Privacy Policy',
    createProfile: 'CREATE PROFILE',
    nickname: 'NICKNAME',
    birthday: 'BIRTHDAY',
    year: 'YEAR',
    month: 'MONTH',
    day: 'DAY',
    signUpSuccess: 'Sign up successful',
    signUpFailed: 'Sign up failed',
    errorOccurred: 'An error occurred during sign up',
    enterNickname: 'ENTER YOUR NICKNAME',
    enterBirthday: 'ENTER YOUR BIRTHDAY',
    optional: '(OPTION)',
    nicknameMinLength: 'Nickname must be at least 3 characters.',
    registrationSuccessful: 'Registration successful!',
    registrationFailed: 'Registration failed',
    registrationFailedTryAgain: 'Registration failed. Please try again.',
  },

  // 底部标签栏
  tabBar: {
    home: 'HOME',
    subscript: 'SUBSCRIPTION',
    inbox: 'INBOX',
    my: 'MY',
  },

  // 订阅
  subscription: {
    title: 'SUBSCRIPTION',
    upgrade: 'UPGRADE',
    downgrade: 'DOWNGRADE',
    failedToLoadPackageInfo: 'Failed to load package information',
    noLowerTiers: 'There are no tiers lower than Tier 1.',
    downgradeText1: 'The selected Tier will take effect from the next Regular Payment Date. If you are currently on a higher tier than the selected tier, you must delete some alarms before',
    downgradeText2: 'when the new tier begins.',
    downgradeText3: 'After',
    downgradeText4: 'if the number of alarms you have exceeds the available "Alarm Slot", the oldest alarms will be automatically deleted.',
    confirmDowngrade: 'Are you sure you want to proceed with the downgrade?',
    loadingPackageInfo: 'Loading package information...',
    generationTime: 'Generation Time',
    seconds: 'seconds',
    surcharge: 'Surcharge',
    wantToDowngrade: 'I want to downgrade.',
    wantToUpgrade: 'I want to upgrade.',
    failedToGetPackageDetails: 'Failed to get package details',
    errorFetchingPackageDetails: 'Error fetching package details',
    subscriptionSuccess: 'Subscription successful',
    subscriptionFailed: 'Subscription failed',
    errorDuringSubscription: 'Error during subscription process',
    beingProcessed: 'being processed...',
    subscriptionIdEmpty: 'Subscription ID cannot be empty',
    productNotFound: 'Subscription product not found',
    userCancelled: 'Purchase cancelled by user',
    purchaseError: 'Error during purchase process: ',
    currentPlan: 'Current Plan',
    upgradePlan: 'Upgrade Plan',
    renewPlan: 'Renew Plan',
    cancelPlan: 'Cancel Plan',
    planDetails: 'Plan Details',
    planExpires: 'Expires on',
    paymentMethod: 'Payment Method',
    changePlan: 'Change Plan',
    subscriptionHistory: 'Subscription History',
    subscriptionCancelled: 'Subscription cancelled successfully',
    subscriptionRenewed: 'Subscription renewed successfully',
    subscriptionUpgraded: 'Subscription upgraded successfully',
    freeTrialEnds: 'Free trial ends on',
    subscribe: 'Subscribe',
    subscribeNow: 'Subscribe Now',
    monthly: 'Monthly',
    yearly: 'Yearly',
    lifetime: 'Lifetime',
    mostPopular: 'Most Popular',
    bestValue: 'Best Value',
    perMonth: '/month',
    perYear: '/year',
    oneTime: 'One-time payment',
    savePercent: 'Save {{percent}}%',
    features: 'Features',
    unlimitedAccess: 'Unlimited Access',
    noAds: 'No Advertisements',
    alreadyOwnedButNotFound: 'You already own this subscription, but the purchase record could not be found',
    alreadyOwnedProcessError: 'Error processing existing subscription',
    premiumContent: 'Premium Content',
    prioritySupport: 'Priority Support',
  },

  // 协议
  agreement: {
    termsOfUse: 'AGREEMENT TO TERMS OF USE',
    agree: 'Agreement',
    viewFullText: 'View full text',
    personalInfoCollection: 'PERSONAL INFORMATION COLLECTION',
    andUsageAgreement: 'AND USAGE AGREEMENT',
    privacyPolicy: 'Privacy Policy',
  },

  // 语音消息
  voiceMessage: {
    celebVoiceAlarmGenerating: 'Your celeb voice alarm is being generated.',
    createVoiceMessage: 'Create a voice message.',
    failedToFetchMessageDetails: 'Failed to fetch message details',
    errorFetchingMessageDetails: 'Error fetching message details',
    loadingVoiceMessage: 'Loading voice message...',
    alarmReadySoon: 'Your alarm will be ready soon\nand can be found in your alarm list',
    voiceMessagesInInbox: 'Voice messages can be found\nin your inbox',
    after: 'after',
    goToAlarmList: 'Go to Alarm List',
    goToInbox: 'Go to Inbox',
    title: 'VOICE MESSAGE',
    enterTitleAtLeastOneLetter: 'Please enter a message with at least one message.',
    apply: 'APPLY',
    backgroundConfirmMessage: 'The celeb voice message background is\nset to the selected photo.\nWould you like to apply it?',
    messageConfirmMessage: 'The celeb voice message is set to\nthe selected message list.\nWould you like to apply it?',
    message: 'MESSAGE',
    random: 'RANDOM',
    clickMessageHint: 'Click the message that you want to generate the celeb voice message.',
    enterMessagePlaceholder: 'Please enter a message',
    background: 'BACKGROUND',
    clickPhotoHint: 'Click the photo to set the background that you want to use with the voice message.',
    timeTypePrice: 'TIME TYPE (PRICE)',
    noTimeTypeData: 'No time type data available',
    failedToGetData: 'Failed to get data',
    errorFetchingData: 'Error fetching data',
    failedToLoadPolicy: 'Failed to load policy',
    errorLoadingPolicy: 'Error loading policy',
    pleaseSelectPackage: 'Please select a package',
    productIdCannotBeEmpty: 'Product ID cannot be empty',
    productNotFound: 'Product not found',
    createSuccess: 'Created successfully',
    saveFailed: 'Save failed',
    userCancelledPurchase: 'User cancelled purchase',
    purchaseError: 'Error during purchase: {{error}}',
    generalPurchaseError: 'Error during purchase and creation of voice message',
    selectPrice: 'Select price',
    productPolicy: 'Product Policy',
    alreadyOwnedButNotFound: 'You already own this item, but the purchase record could not be found',
    alreadyOwnedProcessError: 'Error processing existing purchase',
    errorFetchingCelebrityData: 'Error fetching celebrity data',
    selectVoiceMessage: 'SELECT VOICE MESSAGE',
    writeItYourself: 'Write it yourself',
    makeRecommendMessage: 'Make Recommend Message',
    enterNameAtLeastOneLetter: 'Please enter a name at least one letter.',
    failedToGetSceneTreeData: 'Failed to get scene tree data',
    to: 'To',
    enterNameOrNickname: 'Enter the name or nickname\nof the person receiving the message',
    selectOccasion: 'SELECT OCCASION',
    messageIdNotExist: 'Message ID does not exist',
    checkOutVoiceMessage: 'Check out this voice message from {{name}}!',
    failedToShare: 'Failed to share',
    messageDeletedSuccessfully: 'Message deleted successfully',
    failedToDeleteMessage: 'Failed to delete message',
    errorDeletingMessage: 'Error deleting message',
    share: 'SHARE',
    deleteConfirmationMessage: 'Deleting this will permanently remove\nthe voice message. Are you sure you\nwant to proceed with the deletion?',
  },

  // 日志消息
  logs: {
    callingVoiceMessageListAPI: 'Calling voice message list API, params:',
    voiceMessageListResponse: 'Voice message list response:',
    alarmListResponse: 'Alarm list response:',
    stoppingSoundPlayback: 'Stopping sound playback',
    errorStoppingSoundPlayback: 'Error stopping sound playback:',
    stoppingVibration: 'Stopping vibration',
    pausingSoundPlaybackForDelay: 'Pausing sound playback for delay',
    stoppingVibrationForDelay: 'Stopping vibration for delay',
    schedulingAlarmIn5Minutes: 'Scheduling alarm to trigger again in 5 minutes',
    delayedAlarmScheduledSuccessfully: 'Delayed alarm scheduled successfully',
    errorSchedulingDelayedAlarm: 'Error scheduling delayed alarm:',
    errorFetchingCelebrityData: 'Error fetching celebrity data:',
    errorGettingUserInfo: 'Error getting user info:',
  },

  // 闹钟
  alarm: {
    setAlarm: 'Set Alarm',
    editAlarm: 'Edit Alarm',
    deleteAlarm: 'Delete Alarm',
    alarmTime: 'Alarm Time',
    alarmSound: 'Alarm Sound',
    alarmVibration: 'Vibration',
    alarmRepeat: 'Repeat',
    alarmLabel: 'Label',
    alarmSnooze: 'Snooze',
    alarmVolume: 'Volume',
    alarmDays: 'Days',
    alarmDeleteConfirm: 'Are you sure you want to delete this alarm?',
    alarmSaved: 'Alarm saved successfully',
    alarmDeleted: 'Alarm deleted successfully',
    alarmTriggered: 'Alarm',
    callIn5Minutes: 'Call in 5 minutes',
    addVoiceAlarm: 'Add Voice Alarm',
    voiceAlarmList: 'Celeb voice alarm list',
    noAlarms: 'No alarms set',
    notificationPermissionRequired: 'Notification Permission Required',
    notificationPermissionMessage: 'To set alarms, please allow notifications in your device settings.',
    failedToSetAlarm: 'Failed to set alarm. Please try again.',
    monday: 'Monday',
    tuesday: 'Tuesday',
    wednesday: 'Wednesday',
    thursday: 'Thursday',
    friday: 'Friday',
    saturday: 'Saturday',
    sunday: 'Sunday',
    everyday: 'Everyday',
    weekdays: 'Weekdays',
    weekends: 'Weekends',
    never: 'Never',
    am: 'AM',
    pm: 'PM',
    time: 'TIME',
    stop: 'STOP',
    noAlarmIdProvided: 'No alarm ID provided',
    alarmDataEmpty: 'Alarm data is empty',
    failedToFetchAlarmData: 'Failed to fetch alarm data',
    fetchErrorTryAgain: 'Failed to fetch alarm data. Please try again.',
    delayedAlarmRinging: 'Your delayed alarm is ringing!',
    failedToGetData: 'Failed to get data',
    errorFetchingData: 'Error fetching data',
    enterTitleAtLeastOneLetter: 'Please enter a title with at least one letter.',
    enterMessageAtLeastOneLetter: 'Please enter a message with at least one letter.',
    selectAtLeastOneDay: 'Please select at least one day of the week.',
    alarmCreatedSuccessfully: 'Alarm created successfully!',
    failedToCreateAlarm: 'Failed to create alarm. Please try again.',
    errorCreatingAlarm: 'An error occurred while creating the alarm. Please try again.',
    celebVoiceAlarm: 'CELEB VOICE ALARM',
    apply: 'APPLY',
    backgroundConfirmMessage: 'The celeb voice alarm, phone background is\nset to the selected photo.\nWould you like to apply it?',
    alarmDeletedSuccessfully: 'Alarm deleted successfully!',
    failedToDeleteAlarm: 'Failed to delete alarm. Please try again.',
    errorDeletingAlarm: 'An error occurred while deleting the alarm. Please try again.',
    alarmUpdatedSuccessfully: 'Alarm updated successfully!',
    failedToUpdateAlarm: 'Failed to update alarm. Please try again.',
    errorUpdatingAlarm: 'An error occurred while updating the alarm. Please try again.',
    editVoiceAlarm: 'EDIT VOICE ALARM',
    title: 'Title',
    enterTitle: 'Enter title',
    week: 'WEEK',
    deleteConfirmationMessage: 'Deleting this will permanently remove\nthe alarm, and the number of alarms that\ncan be created will not be restored. Are\nyou sure you want to proceed with the\ndeletion?',
  },
};
