import React from 'react';
import { useTranslation } from 'react-i18next';

/**
 * 高阶组件，为组件注入翻译功能
 * @param Component 要包装的组件
 * @returns 包装后的组件
 */
export function withTranslation<P extends object>(
  Component: React.ComponentType<P & { t: (key: string, options?: any) => string }>
): React.FC<P> {
  return (props: P) => {
    const { t } = useTranslation();
    return <Component {...props} t={t} />;
  };
}

/**
 * 自定义钩子，简化翻译使用
 * @returns 翻译函数
 */
export function useAppTranslation() {
  const { t, i18n } = useTranslation();
  
  // 扩展翻译函数，添加错误处理
  const translate = (key: string, options?: any) => {
    const translation = t(key, options);
    
    // 如果翻译结果与键名相同，可能是未找到翻译
    if (translation === key && !key.includes('.')) {
      console.warn(`Translation not found for key: ${key}`);
      return key;
    }
    
    return translation;
  };
  
  return {
    t: translate,
    i18n,
    changeLanguage: i18n.changeLanguage,
    language: i18n.language,
  };
}

/**
 * 翻译文本组件
 */
export const TranslatedText: React.FC<{
  i18nKey: string;
  options?: any;
  children?: React.ReactNode;
}> = ({ i18nKey, options, children }) => {
  const { t } = useAppTranslation();
  
  // 如果提供了子元素，则使用子元素作为回退
  if (children) {
    const translation = t(i18nKey, options);
    return <>{translation === i18nKey ? children : translation}</>;
  }
  
  return <>{t(i18nKey, options)}</>;
};

export default withTranslation;
