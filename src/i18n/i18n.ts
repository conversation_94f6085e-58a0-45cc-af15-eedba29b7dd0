import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { NativeModules, Platform } from 'react-native';

// 导入翻译文件
import en from './locales/en';
import zh from './locales/zh';
import th from './locales/th';

// 获取设备语言
const getDeviceLanguage = () => {
  const deviceLanguage =
    Platform.OS === 'ios'
      ? NativeModules.SettingsManager.settings.AppleLocale ||
        NativeModules.SettingsManager.settings.AppleLanguages[0] // iOS 10+
      : NativeModules.I18nManager.localeIdentifier;

  // 提取语言代码（例如 'en_US' -> 'en'）
  return deviceLanguage.split('_')[0];
};

// 检查语言代码是否受支持
const isSupportedLanguage = (languageCode: string) => {
  return ['en', 'zh', 'th'].includes(languageCode);
};

// 获取初始语言
const getInitialLanguage = async () => {
  try {
    // 尝试从 AsyncStorage 获取保存的语言设置
    const savedLanguage = await AsyncStorage.getItem('@aivgen:language');
    
    if (savedLanguage) {
      return savedLanguage;
    }
    
    // 如果没有保存的语言设置，使用设备语言
    const deviceLanguage = getDeviceLanguage();
    
    // 如果设备语言受支持，使用它
    if (isSupportedLanguage(deviceLanguage)) {
      return deviceLanguage;
    }
    
    // 默认使用英语
    return 'en';
  } catch (error) {
    console.error('Error getting initial language:', error);
    return 'en'; // 出错时默认使用英语
  }
};

// 初始化 i18n
const initializeI18n = async () => {
  const initialLanguage = await getInitialLanguage();
  
  i18n
    .use(initReactI18next)
    .init({
      resources: {
        en: { translation: en },
        zh: { translation: zh },
        th: { translation: th },
      },
      lng: initialLanguage,
      fallbackLng: 'en',
      interpolation: {
        escapeValue: false,
      },
      compatibilityJSON: 'v3',
    });
};

// 切换语言
export const changeLanguage = async (languageCode: string) => {
  try {
    if (isSupportedLanguage(languageCode)) {
      await i18n.changeLanguage(languageCode);
      await AsyncStorage.setItem('@aivgen:language', languageCode);
      return true;
    }
    return false;
  } catch (error) {
    console.error('Error changing language:', error);
    return false;
  }
};

// 获取当前语言
export const getCurrentLanguage = () => {
  return i18n.language;
};

// 初始化 i18n
initializeI18n();

export default i18n;
