import React, { useEffect, useState, createRef } from 'react';
import { View, StyleSheet, Image, ActivityIndicator, AppState, AppStateStatus } from 'react-native';
import { IMAGES } from '../assets/images';
import { NavigationContainer, NavigationContainerRef } from '@react-navigation/native';
import { createStackNavigator } from '@react-navigation/stack';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';

// 创建导航引用，用于在组件外部访问导航
export const navigationRef = createRef<NavigationContainerRef<any>>();

// 导航函数，用于在组件外部导航
export function navigate(name: string, params?: any) {
  if (navigationRef.current) {
    console.log(`Navigating to ${name} with params:`, params);
    navigationRef.current.navigate(name, params);
  } else {
    console.error('Navigation ref is not initialized yet');
  }
}

// 存储待处理的导航重置
let pendingNavigationReset: { name: string; params?: any } | null = null;

// 重置导航状态函数，用于强制导航到特定页面
export function reset(name: string, params?: any) {
  console.log(`Attempting to reset navigation to ${name} with params:`, params);

  // 保存当前导航请求，以便在任何情况下都能恢复
  pendingNavigationReset = { name, params };

  try {
    if (navigationRef.current) {
      console.log('Navigation ref is available, executing reset');

      try {
        // 首先尝试使用 reset 方法
        navigationRef.current.reset({
          index: 0,
          routes: [{ name, params }],
        });
        console.log('Navigation reset executed successfully');
        pendingNavigationReset = null; // 清除待处理的导航，因为已成功
      } catch (error) {
        console.error('Error during navigation reset:', error);

        // 如果 reset 失败，尝试使用 navigate 方法
        try {
          console.log('Falling back to navigate method');
          navigationRef.current.navigate(name, params);
          console.log('Navigation fallback executed successfully');
          pendingNavigationReset = null; // 清除待处理的导航，因为已成功
        } catch (navigateError) {
          console.error('Error during navigation fallback:', navigateError);
          // 保留 pendingNavigationReset，以便稍后重试
        }
      }
    } else {
      console.error('Navigation ref is not initialized yet');

      // 保存导航请求，稍后尝试执行
      console.log('Saving navigation request for later execution');
      setTimeout(() => {
        console.log('Retrying navigation reset after delay');
        if (navigationRef.current) {
          try {
            navigationRef.current.reset({
              index: 0,
              routes: [{ name, params }],
            });
            console.log('Delayed navigation reset executed successfully');
            pendingNavigationReset = null; // 清除待处理的导航，因为已成功
          } catch (error) {
            console.error('Error during delayed navigation reset:', error);
            // 保留 pendingNavigationReset，以便稍后重试
          }
        } else {
          console.error('Navigation ref still not initialized after delay');
          // 保留 pendingNavigationReset，以便稍后重试
        }
      }, 3000);
    }
  } catch (unexpectedError) {
    // 捕获任何意外错误
    console.error('Unexpected error during navigation:', unexpectedError);
    // 保留 pendingNavigationReset，以便稍后重试
  }
}

// 导入用户存储服务
import { isLoggedIn, getUserInfo } from '../services/storage/userStorage';
// 导入事件发射器
import eventEmitter from '../services/events/eventEmitter';

// 导入屏幕
import {
  HomeScreen,
  LoginEntryScreen,
  LoginScreen,
  AgreementScreen,
  SignUpFormScreen,
  SignUpProfileScreen,
  CelebrityScreen,
  CelebrityDetailScreen,
  SubscriptionScreen,
  AlarmScreen,
  TierListScreen,
  TierDetailScreen,
  InboxScreen,
  SelectCelebScreen,
  MyProfileScreen,
  ChangePasswordScreen,
  MenuScreen,
  NoticeScreen,
  EventScreen,
  FAQScreen,
  AboutScreen,
  SettingScreen,
  VoiceMessageScreen,
  VoiceMessageDetailScreen,
  SelectVoiceMessageScreen,
  WriteVoiceMessageScreen,
  RecommendVoiceMessageScreen,
  VoiceMessageConfirmScreen,
  CreateVoiceAlarmScreen,
  EditVoiceAlarmScreen,
  SearchResultsScreen,
  AlarmTriggerScreen,
} from '../screens';

// 导入组件
import { StatusBarPlaceHolder, AlertModal } from '../components';

// 导入上下文
import { AlertProvider, useAlert } from '../context/AlertContext';

// 创建导航器
const Stack = createStackNavigator();
const Tab = createBottomTabNavigator();

// 主标签导航
const MainTabNavigator = () => {
  // 用户头像状态
  const [userAvatar, setUserAvatar] = useState<string | null>(null);
  // 当前选中的选项卡
  const [currentTab, setCurrentTab] = useState<string>('Home');

  // 获取用户头像的函数
  const fetchUserAvatar = async () => {
    try {
      const userInfo = await getUserInfo();
      if (userInfo && userInfo.headimage) {
        setUserAvatar(userInfo.headimage);
      }
    } catch (error) {
      console.error('Error fetching user avatar:', error);
    }
  };

  // 在组件挂载时获取用户头像并设置事件监听
  useEffect(() => {
    fetchUserAvatar();

    // 监听应用状态变化，当应用从后台返回前台时刷新头像
    const appStateSubscription = AppState.addEventListener('change', (nextAppState: AppStateStatus) => {
      if (nextAppState === 'active') {
        fetchUserAvatar();
      }
    });

    // 监听头像更新事件
    const handleAvatarUpdated = (newAvatarUrl: string) => {
      console.log('Avatar updated event received:', newAvatarUrl);
      setUserAvatar(newAvatarUrl);
    };

    // 添加头像更新事件监听器
    eventEmitter.on('AVATAR_UPDATED', handleAvatarUpdated);

    return () => {
      appStateSubscription.remove();
      eventEmitter.off('AVATAR_UPDATED', handleAvatarUpdated);
    };
  }, []);

  return (
    <Tab.Navigator
      screenOptions={{
        headerShown: false,
        tabBarStyle: {
          backgroundColor: '#000000',
          borderTopColor: '#000000',
          borderTopWidth: 0.01,
          height: 60,
          paddingTop: 5,
          paddingBottom: 5,
        },
        tabBarActiveTintColor: '#D9C091',
        tabBarInactiveTintColor: '#767676',
        tabBarLabelStyle: {
          fontFamily: 'Pretendard-Light',
          fontSize: 11,
          marginBottom: 5,
        },
      }}
      screenListeners={{
        state: (e) => {
          const state = e.data.state;
          if (state && state.index >= 0 && state.routes) {
            // 更新当前选中的选项卡
            setCurrentTab(state.routes[state.index].name);
          }
        },
      }}
    >
      <Tab.Screen
        name="Home"
        component={HomeScreen}
        options={{
          tabBarLabel: 'HOME',
          tabBarIcon: ({ color }) => (
            <Image source={IMAGES.tabIcons.home} style={{ width: 20, height: 20, tintColor: color }} />
          ),
        }}
      />
      <Tab.Screen
        name="Subscription"
        component={SubscriptionScreen}
        options={{
          tabBarLabel: 'SUBSCRIPTION',
          tabBarIcon: ({ color }) => (
            <Image source={IMAGES.tabIcons.subscript} style={{ width: 20, height: 20, tintColor: color }} />
          ),
        }}
      />
      <Tab.Screen
        name="Inbox"
        component={InboxScreen}
        options={{
          tabBarLabel: 'INBOX',
          tabBarIcon: ({ color }) => (
            <Image source={IMAGES.tabIcons.inbox} style={{ width: 20, height: 20, tintColor: color }} />
          ),
        }}
      />
      <Tab.Screen
        name="Profile"
        component={MyProfileScreen}
        options={{
          tabBarLabel: 'MY',
          tabBarIcon: ({ color, focused }) => {
            // 只有当前选中的选项卡是"Profile"时，才显示用户头像
            const showAvatar = currentTab === 'Profile' && userAvatar;

            return showAvatar ? (
              <Image
                source={{ uri: userAvatar }}
                style={{
                  width: 24,
                  height: 24,
                  borderRadius: 12,
                  borderWidth: focused ? 2 : 0,
                  borderColor: '#D9C091',
                }}
              />
            ) : (
              <Image source={IMAGES.tabIcons.my} style={{ width: 20, height: 20, tintColor: color }} />
            );
          },
        }}
      />
    </Tab.Navigator>
  );
};

// 登录后的主导航
const MainNavigator = () => {
  return (
    <Stack.Navigator
      screenOptions={{
        headerShown: false,
      }}
    >
      <Stack.Screen name="MainTabs" component={MainTabNavigator} />
      <Stack.Screen name="Celebrity" component={CelebrityScreen} />
      <Stack.Screen name="CelebrityDetail" component={CelebrityDetailScreen} />
      <Stack.Screen name="Alarm" component={AlarmScreen} />
      <Stack.Screen name="TierList" component={TierListScreen} />
      <Stack.Screen name="TierDetail" component={TierDetailScreen} />
      <Stack.Screen name="SelectCeleb" component={SelectCelebScreen} />
      <Stack.Screen name="ChangePassword" component={ChangePasswordScreen} />
      <Stack.Screen name="Menu" component={MenuScreen} />
      <Stack.Screen name="Notice" component={NoticeScreen} />
      <Stack.Screen name="Event" component={EventScreen} />
      <Stack.Screen name="FAQ" component={FAQScreen} />
      <Stack.Screen name="About" component={AboutScreen} />
      <Stack.Screen name="Setting" component={SettingScreen} />
      <Stack.Screen name="VoiceMessage" component={VoiceMessageScreen} />
      <Stack.Screen name="VoiceMessageDetail" component={VoiceMessageDetailScreen} />
      <Stack.Screen name="SelectVoiceMessage" component={SelectVoiceMessageScreen} />
      <Stack.Screen name="WriteVoiceMessage" component={WriteVoiceMessageScreen} />
      <Stack.Screen name="RecommendVoiceMessage" component={RecommendVoiceMessageScreen} />
      <Stack.Screen name="VoiceMessageConfirm" component={VoiceMessageConfirmScreen} />
      <Stack.Screen name="CreateVoiceAlarm" component={CreateVoiceAlarmScreen} />
      <Stack.Screen name="EditVoiceAlarm" component={EditVoiceAlarmScreen} />
      <Stack.Screen name="SearchResults" component={SearchResultsScreen} />
      <Stack.Screen name="AlarmTrigger" component={AlarmTriggerScreen} />
    </Stack.Navigator>
  );
};

// 身份验证导航
const AuthNavigator = () => {
  return (
    <Stack.Navigator
      screenOptions={{
        headerShown: false,
      }}
    >
      <Stack.Screen name="LoginEntry" component={LoginEntryScreen} />
      <Stack.Screen name="Login" component={LoginScreen} />
      <Stack.Screen name="Agreement" component={AgreementScreen} />
      <Stack.Screen name="SignUpForm" component={SignUpFormScreen} />
      <Stack.Screen name="SignUpProfile" component={SignUpProfileScreen} />
      <Stack.Screen name="Home" component={MainNavigator} />
    </Stack.Navigator>
  );
};

// 全局弹窗组件
const AlertModalContainer = () => {
  const { alertVisible, alertMessage, hideAlert } = useAlert();

  return (
    <AlertModal
      visible={alertVisible}
      message={alertMessage}
      onClose={hideAlert}
    />
  );
};

// 创建全局登录状态上下文
const AuthContext = React.createContext({
  isLoggedIn: false,
  setIsLoggedIn: (_value: boolean) => {},
});

// 提供登录状态的Provider
export const AuthProvider = ({ children }: { children: React.ReactNode }) => {
  const [isUserLoggedIn, setIsLoggedIn] = React.useState(false);

  return (
    <AuthContext.Provider value={{ isLoggedIn: isUserLoggedIn, setIsLoggedIn }}>
      {children}
    </AuthContext.Provider>
  );
};

// 使用登录状态的Hook
export const useAuth = () => React.useContext(AuthContext);

// 全局登出状态管理
let globalSetIsLoggedIn: ((value: boolean) => void) | null = null;

// 设置登出状态
export const setGlobalLoggedOut = () => {
  if (globalSetIsLoggedIn) {
    globalSetIsLoggedIn(false);
    return true;
  }
  return false;
};

// 根导航
const RootNavigator = () => {
  // 使用登录状态
  const [isUserLoggedIn, setIsUserLoggedIn] = useState(false);
  // 加载状态
  const [isLoading, setIsLoading] = useState(true);

  // 保存全局设置登录状态的函数引用
  globalSetIsLoggedIn = setIsUserLoggedIn;

  // 在组件挂载时检查用户登录状态
  useEffect(() => {
    const checkLoginStatus = async () => {
      try {
        // 检查用户是否已登录
        const loggedIn = await isLoggedIn();
        setIsUserLoggedIn(loggedIn);
        console.log('User login status:', loggedIn ? 'Logged in' : 'Not logged in');
      } catch (error) {
        console.error('Error checking login status:', error);
      } finally {
        // 无论结果如何，都将加载状态设置为完成
        setIsLoading(false);
      }
    };

    // 执行登录状态检查
    checkLoginStatus();
  }, []);

  // 如果正在加载，显示加载指示器
  if (isLoading) {
    return (
      <View style={[styles.container, styles.loadingContainer]}>
        <ActivityIndicator size="large" color="#FF0066" />
      </View>
    );
  }

  return (
    <AlertProvider>
      <View style={styles.container}>
        <StatusBarPlaceHolder />
        <NavigationContainer
          ref={navigationRef}
          onReady={() => {
            console.log('Navigation container is ready');
            // 检查是否有待处理的导航请求
            if (pendingNavigationReset) {
              console.log('Found pending navigation request, executing...');
              const { name, params } = pendingNavigationReset;
              setTimeout(() => {
                try {
                  if (navigationRef.current) {
                    navigationRef.current.reset({
                      index: 0,
                      routes: [{ name, params }],
                    });
                    console.log('Executed pending navigation successfully');
                    pendingNavigationReset = null;
                  }
                } catch (error) {
                  console.error('Error executing pending navigation:', error);
                }
              }, 500);
            }
          }}
        >
          {isUserLoggedIn ? (
            <MainNavigator />
          ) : (
            <AuthNavigator />
          )}
        </NavigationContainer>
        <AlertModalContainer />
      </View>
    </AlertProvider>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000000',
  },
  loadingContainer: {
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default RootNavigator;
