import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Image,
  StatusBar,
  Platform,
  TextInput,
} from 'react-native';
import { useNavigation, CommonActions } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { FONTS } from '../assets/fonts';
import { useTranslation } from 'react-i18next';

// 定义导航参数类型
type RootStackParamList = {
  Menu: undefined;
  [key: string]: undefined | object;
};

type HeaderNavigationProp = StackNavigationProp<RootStackParamList>;
// 导入图片资源
import { IMAGES } from '../assets/images';
export type RightComponentType = 'none' | 'search' | 'menu' | 'searchAndMenu' | 'menuAndSearch' | 'custom';

interface HeaderProps {
  showBack?: boolean;
  title?: string;
  rightComponent?: React.ReactNode;
  rightComponentType?: RightComponentType;
  onSearchPress?: () => void;
  onMenuPress?: () => void;
  red?: boolean;
  showLogo?: boolean;
}

const Header: React.FC<HeaderProps> = ({
  showBack = true,
  title = '',
  rightComponent = null,
  rightComponentType = 'none',
  onSearchPress,
  onMenuPress,
  red = false,
  showLogo = false,
}) => {
  const navigation = useNavigation<HeaderNavigationProp>();
  const { t } = useTranslation();
  const [showSearchBar, setShowSearchBar] = useState(false);
  const [searchText, setSearchText] = useState('');

  const handleBack = () => {
    if (navigation.canGoBack()) {
      navigation.goBack();
    }
  };

  const handleSearchPress = () => {
    // 无论是否提供了onSearchPress回调，都显示搜索框并清空搜索文本
    setShowSearchBar(true);
    setSearchText(''); // 清空搜索文本
    console.log('Search pressed');

    // 如果提供了onSearchPress回调，也调用它
    if (onSearchPress) {
      onSearchPress();
    }
  };

  const handleCloseSearch = () => {
    setShowSearchBar(false);
    setSearchText('');
  };

  // 处理搜索提交
  const handleSearchSubmit = () => {
    if (searchText.trim()) {
      // 如果搜索文本不为空，导航到搜索结果页面
      console.log('Navigating to search results with keyword:', searchText);
      navigation.navigate('SearchResults', { keyword: searchText });
      // 关闭搜索栏
      setShowSearchBar(false);
    }
  };

  const handleMenuPress = () => {
    try {
      navigation.navigate('Menu');
      console.log('Navigating to Menu screen');
    } catch (error) {
      console.error('Navigation error:', error);
      // 如果上面的方法失败，尝试使用CommonActions
      navigation.dispatch(
        CommonActions.navigate({
          name: 'Menu',
        })
      );
    }
  };

  // 预定义的右侧组件
  const renderRightComponent = () => {
    if (rightComponent && rightComponentType === 'custom') {
      return rightComponent;
    }

    switch (rightComponentType) {
      case 'search':
        return (
          <View style={styles.headerRight}>
            <TouchableOpacity style={styles.iconButton} onPress={handleSearchPress}>
              <Image source={IMAGES.searchIcon} style={styles.icon} />
            </TouchableOpacity>
          </View>
        );
      case 'menu':
        return (
          <View style={styles.headerRight}>
            <TouchableOpacity style={styles.iconButton} onPress={handleMenuPress}>
              <Image source={IMAGES.menuIcon} style={styles.icon} />
            </TouchableOpacity>
          </View>
        );
      case 'searchAndMenu':
        return (
          <View style={styles.headerRight}>
            <TouchableOpacity style={styles.iconButton} onPress={handleSearchPress}>
              <Image source={IMAGES.searchIcon} style={styles.icon} />
            </TouchableOpacity>
            <TouchableOpacity style={styles.iconButton} onPress={handleMenuPress}>
              <Image source={IMAGES.menuIcon} style={styles.icon} />
            </TouchableOpacity>
          </View>
        );
      case 'menuAndSearch':
        return (
          <View style={styles.headerRight}>
            <TouchableOpacity style={styles.iconButton} onPress={handleSearchPress}>
              <Image source={IMAGES.searchIcon} style={styles.icon} />
            </TouchableOpacity>
            <TouchableOpacity style={styles.iconButton} onPress={handleMenuPress}>
              <Image source={IMAGES.menuIcon} style={styles.icon} />
            </TouchableOpacity>
          </View>
        );
      case 'none':
      default:
        return <View style={styles.placeholder} />;
    }
  };

  return (
    <View style={styles.headerContainer}>
      {Platform.OS === 'android' && (
        <StatusBar
          barStyle="light-content"
          backgroundColor="#000000"
          translucent={true}
        />
      )}

      {showSearchBar ? (
        // 搜索栏布局
        <View style={styles.header}>
          <View style={styles.searchInputContainer}>
            <Image source={IMAGES.searchIcon} style={styles.searchIcon} />
            <TextInput
              style={styles.searchInput}
              placeholder={t('search.celebNameSearch')}
              placeholderTextColor="#999999"
              value={searchText}
              onChangeText={setSearchText}
              onBlur={handleSearchSubmit}
              onSubmitEditing={handleSearchSubmit}
              returnKeyType="search"
              autoFocus
            />
          </View>
          <TouchableOpacity onPress={handleCloseSearch} style={styles.closeButton}>
            <Text style={styles.closeButtonText}>×</Text>
          </TouchableOpacity>
        </View>
      ) : (
        // 正常Header布局
        <>
          {showBack ? (
            // 有返回按钮的布局
            <View style={styles.header}>
              <TouchableOpacity
                onPress={handleBack}
                style={[styles.backButton, { zIndex: 2 }]}
              >
                <Image source={IMAGES.backIcon} style={styles.backIcon} />
              </TouchableOpacity>

              {showLogo ? (
                <View style={styles.logoContainer}>
                  <Image source={IMAGES.logo} style={styles.logo} />
                </View>
              ) : title ? (
                red ? (
                  <View style={styles.centeredTitleContainer}>
                    <Text style={[styles.title, styles.titleRed]}>{title}</Text>
                  </View>
                ) : (
                  <View style={styles.leftTitleContainer}>
                    <Text style={[styles.title, styles.titleWhite]}>{title}</Text>
                  </View>
                )
              ) : null}

              <View style={{ zIndex: 2 }}>
                {renderRightComponent()}
              </View>
            </View>
          ) : (
            // 没有返回按钮的布局
            <View style={styles.header}>
              <View style={styles.placeholder} />
              {title ? (
                <Text style={[
                  styles.title,
                  styles.titleCentered,
                  red ? styles.titleRed : null
                ]}>{title}</Text>
              ) : (
                <View style={styles.logoContainer}>
                  <Image source={IMAGES.logo} style={styles.logo} />
                </View>
              )}
              {renderRightComponent()}
            </View>
          )}
        </>
      )}
    </View>
  );
};

// 获取状态栏高度
const STATUSBAR_HEIGHT = Platform.OS === 'ios' ? 44 : StatusBar.currentHeight || 0;

const styles = StyleSheet.create({
  headerContainer: {
    paddingTop: Platform.OS === 'android' ? STATUSBAR_HEIGHT : 0,
    backgroundColor: '#000000',
    borderBottomWidth: 0,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 10,
    height: 50, // 增加高度
    position: 'relative', // 添加相对定位，使logoContainer的绝对定位相对于header
  },
  leftContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  backButton: {
    padding: 10,
  },
  backIcon: {
    width: 15,
    height: 20,
    tintColor: '#FFFFFF',
    resizeMode: 'contain',
  },
  logoContainer: {
    position: 'absolute',
    left: 0,
    right: 0,
    alignItems: 'center',
    justifyContent: 'center',
    zIndex: 1,
  },
  logo: {
    width: 120,
    height: 40,
    resizeMode: 'contain',
  },
  title: {
    fontSize: 22,
    fontFamily: FONTS.bebasNeue.regular,
     color: '#FF004A',
  },
  titleWhite: {
    color: '#FFFFFF',
  },
  titleRed: {
    color: '#FF004A',
  },
  titleCentered: {
    position: 'absolute',
    left: 0,
    right: 0,
    textAlign: 'center',
  },
  placeholder: {
    width: 40,
    zIndex: 2,
  },
  headerRight: {
    flexDirection: 'row',
    alignItems: 'center',
    zIndex: 2,
  },
  iconButton: {
    padding: 10,
  },
  icon: {
    width: 20,
    height: 20,
    tintColor: '#FFFFFF',
  },
  // 搜索栏样式
  searchInputContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#333333',
    borderRadius: 20,
    paddingHorizontal: 15,
    height: 40, // 增加高度以适应搜索栏
    marginRight: 10,
  },
  searchIcon: {
    width: 20,
    height: 20,
    tintColor: '#999999',
    marginRight: 10,
  },
  searchInput: {
    flex: 1,
    color: '#FFFFFF',
    fontSize: 16,
    height: 40, // 增加高度以适应搜索输入框容器
    paddingVertical: 0, // 确保文本垂直居中
  },
  closeButton: {
    width: 30,
    height: 30,
    alignItems: 'center',
    justifyContent: 'center',
  },
  closeButtonText: {
    color: '#FFFFFF',
    fontSize: 24,
    fontWeight: 'bold',
  },
  centeredTitleContainer: {
    position: 'absolute',
    left: 0,
    right: 0,
    top: 0,
    bottom: 0,
    alignItems: 'center',
    justifyContent: 'center',
    zIndex: 1,
  },
  leftTitleContainer: {
    flex: 1,
    marginLeft: 10,
  },
});

export default Header;
