import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Modal,
  SafeAreaView,
} from 'react-native';
import { useTranslation } from 'react-i18next';
import { changeLanguage, getCurrentLanguage } from '../i18n/i18n';
import { FONTS } from '../assets/fonts';

interface LanguageSelectorProps {
  isVisible: boolean;
  onClose: () => void;
}

const LanguageSelector: React.FC<LanguageSelectorProps> = ({ isVisible, onClose }) => {
  const { t } = useTranslation();
  const [selectedLanguage, setSelectedLanguage] = useState(getCurrentLanguage());

  // 当模态框显示时更新选中的语言
  useEffect(() => {
    if (isVisible) {
      setSelectedLanguage(getCurrentLanguage());
    }
  }, [isVisible]);

  // 语言选项
  const languages = [
    { code: 'en', name: 'English' },
    { code: 'zh', name: '中文' },
    { code: 'th', name: 'ภาษาไทย' },
  ];

  // 处理语言选择
  const handleSelectLanguage = async (languageCode: string) => {
    const success = await changeLanguage(languageCode);
    if (success) {
      setSelectedLanguage(languageCode);
    }
  };

  // 处理确认按钮点击
  const handleConfirm = () => {
    onClose();
  };

  return (
    <Modal
      visible={isVisible}
      animationType="slide"
      transparent={true}
      onRequestClose={onClose}
    >
      <SafeAreaView style={styles.modalContainer}>
        <View style={styles.modalContent}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>{t('settings.selectLanguage')}</Text>
          </View>

          <View style={styles.languageList}>
            {languages.map((language) => (
              <TouchableOpacity
                key={language.code}
                style={[
                  styles.languageItem,
                  selectedLanguage === language.code && styles.selectedLanguageItem,
                ]}
                onPress={() => handleSelectLanguage(language.code)}
              >
                <Text
                  style={[
                    styles.languageText,
                    selectedLanguage === language.code && styles.selectedLanguageText,
                  ]}
                >
                  {language.name}
                </Text>
                {selectedLanguage === language.code && (
                  <View style={styles.checkmark} />
                )}
              </TouchableOpacity>
            ))}
          </View>

          <TouchableOpacity style={styles.confirmButton} onPress={handleConfirm}>
            <Text style={styles.confirmButtonText}>{t('common.confirm')}</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContent: {
    width: '80%',
    backgroundColor: '#1A1A1A',
    borderRadius: 10,
    padding: 20,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#D9C091',
  },
  modalHeader: {
    width: '100%',
    alignItems: 'center',
    marginBottom: 20,
  },
  modalTitle: {
    fontFamily: FONTS.bold,
    fontSize: 18,
    color: '#FFFFFF',
    textAlign: 'center',
  },
  languageList: {
    width: '100%',
    marginBottom: 20,
  },
  languageItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 15,
    paddingHorizontal: 10,
    borderBottomWidth: 1,
    borderBottomColor: '#333333',
  },
  selectedLanguageItem: {
    backgroundColor: 'rgba(217, 192, 145, 0.1)',
  },
  languageText: {
    fontFamily: FONTS.regular,
    fontSize: 16,
    color: '#FFFFFF',
  },
  selectedLanguageText: {
    color: '#D9C091',
    fontFamily: FONTS.bold,
  },
  checkmark: {
    width: 20,
    height: 20,
    borderRadius: 10,
    backgroundColor: '#D9C091',
    alignItems: 'center',
    justifyContent: 'center',
  },
  confirmButton: {
    width: '100%',
    height: 50,
    backgroundColor: '#D9C091',
    borderRadius: 10,
    alignItems: 'center',
    justifyContent: 'center',
  },
  confirmButtonText: {
    fontFamily: FONTS.bold,
    fontSize: 16,
    color: '#000000',
  },
});

export default LanguageSelector;
