import React, { useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
  Animated,
  Dimensions,
  TouchableWithoutFeedback,
  Image,
} from 'react-native';
import { FONTS } from '../assets/fonts';
import { useTranslation } from 'react-i18next';

const { height } = Dimensions.get('window');

// 分享选项类型
export type ShareOption = {
  id: string;
  label: string;
  icon: any; // 图标资源
};

interface ShareBottomSheetProps {
  visible: boolean;
  onClose: () => void;
  onSelect: (option: string) => void;
  options: ShareOption[];
}

const ShareBottomSheet: React.FC<ShareBottomSheetProps> = ({
  visible,
  onClose,
  onSelect,
  options,
}) => {
  const { t } = useTranslation();
  const slideAnim = useRef(new Animated.Value(height)).current;

  useEffect(() => {
    if (visible) {
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }).start();
    } else {
      Animated.timing(slideAnim, {
        toValue: height,
        duration: 300,
        useNativeDriver: true,
      }).start();
    }
  }, [visible, slideAnim]);

  const handleSelect = (id: string) => {
    onSelect(id);
    onClose();
  };

  if (!visible) return null;

  return (
    <Modal
      transparent
      visible={visible}
      animationType="none"
      onRequestClose={onClose}
    >
      <TouchableWithoutFeedback onPress={onClose}>
        <View style={styles.overlay}>
          <TouchableWithoutFeedback>
            <Animated.View
              style={[
                styles.container,
                {
                  transform: [{ translateY: slideAnim }],
                },
              ]}
            >
              {/* 拖动手柄 */}
              <View style={styles.handleContainer}>
                <View style={styles.handle} />
              </View>

              {/* 标题和分隔线 */}
              <Text style={styles.title}>{t('share.selectSharingMethod')}</Text>
              <View style={styles.titleDivider} />

              {/* 分享选项 */}
              <View style={styles.optionsContainer}>
                {options.map((option) => (
                  <TouchableOpacity
                    key={option.id}
                    style={styles.optionButton}
                    onPress={() => handleSelect(option.id)}
                  >
                    <View style={styles.iconContainer}>
                      <Image source={option.icon} style={styles.optionIcon} />
                    </View>
                  </TouchableOpacity>
                ))}
              </View>
            </Animated.View>
          </TouchableWithoutFeedback>
        </View>
      </TouchableWithoutFeedback>
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    justifyContent: 'flex-end',
  },
  container: {
    backgroundColor: '#121212',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingBottom: 30,
    width: '100%',
  },
  handleContainer: {
    alignItems: 'center',
    paddingTop: 15,
    paddingBottom: 10,
  },
  handle: {
    width: 40,
    height: 4,
    backgroundColor: '#666',
    borderRadius: 2,
  },
  title: {
    color: '#FFFFFF',
    fontFamily: FONTS.pretendard.light,
    fontSize: 18,
    textAlign: 'center',
    marginBottom: 20,
  },
  titleDivider: {
    width: '90%',
    height: 1,
    backgroundColor: '#333',
    alignSelf: 'center',
    marginBottom: 20,
  },
  optionsContainer: {
    flexDirection: 'row',
    justifyContent:'space-around',
    paddingHorizontal: 20,
    flexWrap: 'wrap',
    width: '100%',
  },
  optionButton: {
    marginBottom: 20,
    alignItems: 'center',
  },
  iconContainer: {
    width: 40,
    height:  40,
    borderRadius: 30,
    justifyContent: 'center',
    alignItems: 'center',
    overflow: 'hidden',
  },
  optionIcon: {
    width: 40,
    height:  40,
    resizeMode: 'contain',
  },
});

export default ShareBottomSheet;
