import React, { useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
  Animated,
  Dimensions,
  TouchableWithoutFeedback,
} from 'react-native';
import { FONTS } from '../assets/fonts';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../context/LanguageContext';
import { languageCodeMap } from '../i18n';
import { post } from '../services/api/apiService';
import { getUserInfo } from '../services/storage/userStorage';
import { useNavigation, NavigationProp, ParamListBase } from '@react-navigation/native';
import { showSuccess, showError } from '../services/toast';

const { height } = Dimensions.get('window');

// 语言选项类型
export type LanguageOption = 'ENGLISH' | 'KOREAN' | 'JAPANESE' | 'CHINESE' | 'THAI';

// 语言显示名称映射
const languageDisplayNames: Record<LanguageOption, string> = {
  'ENGLISH': 'English',
  'KOREAN': '한국어(Korean)',
  'JAPANESE': '日本語(Japanese)',
  'CHINESE': '中文(Chinese)',
  'THAI': 'ภาษาไทย(Thai)',
};

interface LanguageBottomSheetProps {
  visible: boolean;
  selectedLanguage: LanguageOption;
  onSelect: (language: LanguageOption) => void;
  onClose: () => void;
}

const LanguageBottomSheet: React.FC<LanguageBottomSheetProps> = ({
  visible,
  selectedLanguage,
  onSelect,
  onClose,
}) => {
  const slideAnim = useRef(new Animated.Value(height)).current;
  const { t } = useTranslation();
  const { setLanguageByOption } = useLanguage();
  const navigation = useNavigation<NavigationProp<ParamListBase>>();

  // 可用的语言选项
  const languageOptions: LanguageOption[] = ['ENGLISH', 'THAI'];

  useEffect(() => {
    if (visible) {
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }).start();
    } else {
      Animated.timing(slideAnim, {
        toValue: height,
        duration: 300,
        useNativeDriver: true,
      }).start();
    }
  }, [visible, slideAnim]);

  // 更新用户语言偏好
  const updateUserLanguagePreference = async (language: string) => {
    try {
      const userInfo = await getUserInfo();
      if (userInfo && userInfo.id) {
        // 调用API更新用户语言偏好
        const response = await post('/account/update', {
          id: userInfo.id,
          lang: language,
        });
        console.log(response.data);
        if (response.data && (response.data.code === 200 || response.data.code === '200')) {
          // 显示成功消息
          showSuccess(t('settings.languageUpdatedSuccess'));

          // 导航到首页
          navigation.reset({
            index: 0,
            routes: [{ name: 'MainTabs', params: { screen: 'Home' } }],
          });
        } else {
          showError(t('settings.languageUpdateFailed'));
        }
      }
    } catch (error) {
      console.error('Error updating user language preference:', error);
      showError(t('settings.languageUpdateFailed'));
    }
  };

  const handleSelect = async (language: LanguageOption) => {
    // 更新全局语言设置
    await setLanguageByOption(language);

    // 更新用户语言偏好
    const langCode = languageCodeMap[language];
    if (langCode) {
      await updateUserLanguagePreference(langCode);
    }

    // 通知父组件
    onSelect(language);
    onClose();
  };

  if (!visible) {
    return null;
  }

  return (
    <Modal
      transparent
      visible={visible}
      animationType="none"
      onRequestClose={onClose}
    >
      <TouchableWithoutFeedback onPress={onClose}>
        <View style={styles.overlay}>
          <TouchableWithoutFeedback>
            <Animated.View
              style={[
                styles.container,
                {
                  transform: [{ translateY: slideAnim }],
                },
              ]}
            >
              {/* 拖动手柄 */}
              <View style={styles.handleContainer}>
                <View style={styles.handle} />
              </View>

              {/* 标题和分隔线 */}
              <Text style={styles.title}>{t('settings.language')}</Text>
              <View style={styles.titleDivider} />

              {/* 语言选项列表 */}
              <View style={styles.optionsContainer}>
                {languageOptions.map((language) => (
                  <TouchableOpacity
                    key={language}
                    style={[
                      styles.optionButton,
                      selectedLanguage === language && styles.selectedOption,
                    ]}
                    onPress={() => handleSelect(language)}
                  >
                    <Text style={[
                      styles.optionText,
                      selectedLanguage === language && styles.selectedOptionText,
                    ]}>
                      {languageDisplayNames[language]}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </Animated.View>
          </TouchableWithoutFeedback>
        </View>
      </TouchableWithoutFeedback>
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  container: {
    backgroundColor: '#121212',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingBottom: 30,
    width: '90%',
    alignSelf: 'center',
    marginHorizontal: '5%',
  },
  handleContainer: {
    alignItems: 'center',
    paddingTop: 15,
    paddingBottom: 10,
  },
  handle: {
    width: 40,
    height: 5,
    backgroundColor: '#666',
    borderRadius: 3,
  },
  title: {
    color: '#D9C091',
    fontFamily: FONTS.bebasNeue.regular,
    fontSize: 20,
    letterSpacing: 1,
    textAlign: 'center',
    marginBottom: 10,
  },
  titleDivider: {
    width: '90%',
    height: 1,
    backgroundColor: '#333',
    alignSelf: 'center',
    marginBottom: 15,
  },
  optionsContainer: {
    paddingHorizontal: 30,
  },
  optionButton: {
    height: 50,
    justifyContent: 'center',
    alignItems: 'center',
    marginVertical: 2,
  },
  selectedOption: {
    // 选中状态的样式
  },
  optionText: {
    color: '#FFFFFF',
    fontFamily: FONTS.pretendard.light,
    fontSize: 18,
  },
  selectedOptionText: {
    color: '#D9C091', // 金色文字
    fontWeight: 'bold',
  },
});

export default LanguageBottomSheet;
