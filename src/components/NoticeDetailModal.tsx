import React from 'react';
import {
  View,
  Text,
  Modal,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Dimensions,
  Image,
} from 'react-native';
import { FONTS } from '../assets/fonts';
import { IMAGES } from '../assets/images';

interface NoticeDetailModalProps {
  visible: boolean;
  onClose: () => void;
  notice: {
    title?: string;
    writer?: string;
    date?: string;
    content: string;
  };
}

const { width, height } = Dimensions.get('window');

const NoticeDetailModal: React.FC<NoticeDetailModalProps> = ({
  visible,
  onClose,
  notice,
}) => {
  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="slide"
      onRequestClose={onClose}
    >
      <View style={styles.overlay}>
        <TouchableOpacity
          style={styles.closeArea}
          activeOpacity={1}
          onPress={onClose}
        />
        <View style={styles.modalContainer}>
          {/* 标题栏 */}
          <View style={styles.headerContainer}>
            <View style={styles.titleContainer}>
              <View style={styles.titleColorBlock} />
              <Text style={styles.headerTitle}>{notice.title}</Text>
              <TouchableOpacity style={styles.closeButton} onPress={onClose}>
                <Text style={styles.closeText}>×</Text>
              </TouchableOpacity>
            </View>
            <View style={styles.titleUnderline} />
          </View>

          {/* 作者和日期 */}
          <View style={styles.metaContainer}>
            <Text style={styles.writerText}>{notice.writer}</Text>
            <Text style={styles.dateText}>{notice.date || ''}</Text>
          </View>

          {/* 内容区域 */}
          <ScrollView style={styles.contentContainer}>
            <Text style={styles.contentText}>{notice.content}</Text>
          </ScrollView>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    justifyContent: 'flex-end',
  },
  closeArea: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: height * 0.7,
  },
  modalContainer: {
    maxHeight: height * 0.7,
    backgroundColor: '#333333',
    borderTopLeftRadius: 15,
    borderTopRightRadius: 15,
    overflow: 'hidden',
    marginHorizontal: 20,
  },
  headerContainer: {
    paddingTop: 25,
    paddingBottom: 0,
    position: 'relative',
  },
  titleContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    position: 'relative',
    marginBottom: 15,
  },
  titleColorBlock: {
    position: 'absolute',
    left: '50%',
    marginLeft: -35,
    top: -10,
    width: 70,
    height: 3,
    backgroundColor: '#D9C091',
    borderRadius: 1.5,
  },
  headerTitle: {
    color: '#D9C091', // 金色文字
    fontSize: 24,
    fontFamily: FONTS.bebasNeue.regular,
    textAlign: 'center',
    marginTop: 15,
    marginBottom: 15,
  },
  titleUnderline: {
    height: 1,
    backgroundColor: '#828282',
    marginHorizontal: 20,
    marginBottom: 15,
  },
  closeButton: {
    position: 'absolute',
    right: 20,
    top: -10,
    width: 24,
    height: 24,
    justifyContent: 'center',
    alignItems: 'center',
  },
  closeText: {
    color: '#FFFFFF',
    fontSize: 24,
    fontWeight: 'bold',
    lineHeight: 24,
  },
  metaContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingBottom: 15,
  },
  writerText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontFamily: FONTS.pretendard.light,
  },
  dateText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontFamily: FONTS.pretendard.light,
  },
  divider: {
    display: 'none', // 设计图中没有这条分隔线
  },
  contentContainer: {
    marginHorizontal: 20,
    paddingBottom: 30,
    maxHeight: height * 0.45,
    borderTopWidth:1,
    borderTopColor:'#828282',
    paddingTop: 20,
    minHeight: '40%'
  },
  contentText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontFamily: FONTS.pretendard.light,
    lineHeight: 24,
  },
});

export default NoticeDetailModal;
