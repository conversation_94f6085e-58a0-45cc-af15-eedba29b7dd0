import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
} from 'react-native';
import { colors, spacing } from '../styles/theme';
import { useTranslation } from 'react-i18next';

type TabItem = {
  key: string;
  label: string;
  icon?: string; // 实际使用时可以替换为图标组件
};

type BottomTabBarProps = {
  tabs: TabItem[];
  activeTab: string;
  onTabPress: (tabKey: string) => void;
};

const BottomTabBar = ({ tabs, activeTab, onTabPress }: BottomTabBarProps) => {
  const { t } = useTranslation();

  return (
    <View style={styles.container}>
      {tabs.map((tab) => (
        <TouchableOpacity
          key={tab.key}
          style={[
            styles.tabItem,
            activeTab === tab.key && styles.activeTabItem,
          ]}
          onPress={() => onTabPress(tab.key)}
        >
          <View style={styles.tabIconContainer}>
            {/* 这里可以放置图标 */}
            <View
              style={[
                styles.tabIcon,
                activeTab === tab.key && styles.activeTabIcon,
              ]}
            />
          </View>
          <Text
            style={[
              styles.tabLabel,
              activeTab === tab.key && styles.activeTabLabel,
            ]}
          >
            {t(`navigation.${tab.key.toLowerCase()}`)}
          </Text>
        </TouchableOpacity>
      ))}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    backgroundColor: colors.background,
    borderTopWidth: 1,
    borderTopColor: colors.border,
    paddingBottom: spacing.md, // 为了适应底部安全区域
  },
  tabItem: {
    flex: 1,
    alignItems: 'center',
    paddingVertical: spacing.sm,
  },
  activeTabItem: {
    // 可以添加活跃状态的样式
  },
  tabIconContainer: {
    marginBottom: spacing.xs,
  },
  tabIcon: {
    width: 24,
    height: 24,
    backgroundColor: colors.secondaryText,
    borderRadius: 12,
  },
  activeTabIcon: {
    backgroundColor: colors.primary,
  },
  tabLabel: {
    fontSize: 10,
    color: colors.secondaryText,
  },
  activeTabLabel: {
    color: colors.primary,
    fontWeight: 'bold',
  },
});

export default BottomTabBar;
