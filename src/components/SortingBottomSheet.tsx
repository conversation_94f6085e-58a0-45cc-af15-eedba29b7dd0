import React, { useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
  Animated,
  Dimensions,
  TouchableWithoutFeedback,
  ActivityIndicator,
} from 'react-native';
import { FONTS } from '../assets/fonts';
import { useTranslation } from 'react-i18next';

const { height } = Dimensions.get('window');

type SortingOption = {
  id: string;
  label: string;
};

type SortingBottomSheetProps = {
  visible: boolean;
  onClose: () => void;
  onSelect: (option: string) => void;
  options: SortingOption[];
  title?: string;
  isLoading?: boolean;
};

const SortingBottomSheet = ({
  visible,
  onClose,
  onSelect,
  options,
  title,
  isLoading = false,
}: SortingBottomSheetProps) => {
  const slideAnim = useRef(new Animated.Value(height)).current;
  const { t } = useTranslation();

  useEffect(() => {
    if (visible) {
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }).start();
    } else {
      Animated.timing(slideAnim, {
        toValue: height,
        duration: 300,
        useNativeDriver: true,
      }).start();
    }
  }, [visible, slideAnim]);

  const handleSelect = (id: string) => {
    onSelect(id);
    onClose();
  };

  if (!visible) return null;

  return (
    <Modal
      transparent
      visible={visible}
      animationType="none"
      onRequestClose={onClose}
    >
      <TouchableWithoutFeedback onPress={onClose}>
        <View style={styles.overlay}>
          <TouchableWithoutFeedback>
            <Animated.View
              style={[
                styles.container,
                {
                  transform: [{ translateY: slideAnim }],
                },
              ]}
            >
              {/* 拖动手柄 */}
              <View style={styles.handleContainer}>
                <View style={styles.handle} />
              </View>

              {/* 标题和分隔线 */}
              <Text style={styles.title}>{title || t('sorting.title')}</Text>
              <View style={styles.titleDivider} />

              {/* 选项列表 */}
              <View style={styles.optionsContainer}>
                {isLoading ? (
                  <View style={styles.loadingContainer}>
                    <ActivityIndicator size="small" color="#D9C091" />
                    <Text style={styles.loadingText}>{t('common.loading')}</Text>
                  </View>
                ) : options.map((option) => (
                  <TouchableOpacity
                    key={option.id}
                    style={styles.optionButton}
                    onPress={() => handleSelect(option.id)}
                  >
                    <Text style={styles.optionText}>{option.label}</Text>
                  </TouchableOpacity>
                ))}
              </View>
            </Animated.View>
          </TouchableWithoutFeedback>
        </View>
      </TouchableWithoutFeedback>
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  container: {
    backgroundColor: '#121212',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingBottom: 30,
    width: '90%',
    alignSelf: 'center',
    marginHorizontal: '5%',
  },
  handleContainer: {
    alignItems: 'center',
    paddingTop: 15,
    paddingBottom: 10,
  },
  handle: {
    width: 40,
    height: 5,
    backgroundColor: '#666',
    borderRadius: 3,
  },
  title: {
    color: '#D9C091',
    fontFamily: FONTS.bebasNeue.regular,
    fontSize: 20,
    letterSpacing: 1,
    textAlign: 'center',
    marginBottom: 10,
  },
  titleDivider: {
    width: '90%',
    height: 1,
    backgroundColor: '#333',
    alignSelf: 'center',
    marginBottom: 15,
  },
  optionsContainer: {
    paddingHorizontal: 30,
  },
  optionButton: {
    height: 50,
    justifyContent: 'center',
    alignItems: 'center',
    marginVertical: 2,
  },
  optionText: {
    color: '#FFFFFF',
    fontFamily: FONTS.bebasNeue.regular,
    fontSize: 20,
    letterSpacing: 1.5,
  },
  loadingContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 20,
  },
  loadingText: {
    color: '#FFFFFF',
    fontFamily: FONTS.pretendard.light,
    fontSize: 14,
    marginTop: 10,
  },
});

export default SortingBottomSheet;
