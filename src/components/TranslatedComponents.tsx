import React from 'react';
import { Text, TextProps, Button, ButtonProps, TouchableOpacity, TouchableOpacityProps, View, ViewProps } from 'react-native';
import { useAppTranslation, TranslatedText } from '../i18n/withTranslation';

/**
 * 翻译文本组件
 */
export const TText: React.FC<TextProps & { i18nKey: string; options?: any }> = ({
  i18nKey,
  options,
  children,
  ...props
}) => {
  const { t } = useAppTranslation();
  
  return (
    <Text {...props}>
      {children ? children : t(i18nKey, options)}
    </Text>
  );
};

/**
 * 翻译按钮组件
 */
export const TButton: React.FC<ButtonProps & { i18nKey: string; options?: any }> = ({
  i18nKey,
  options,
  title,
  ...props
}) => {
  const { t } = useAppTranslation();
  
  return (
    <Button
      {...props}
      title={title || t(i18nKey, options)}
    />
  );
};

/**
 * 翻译触摸按钮组件
 */
export const TTouchableOpacity: React.FC<TouchableOpacityProps & {
  i18nKey?: string;
  options?: any;
  textStyle?: TextProps['style'];
}> = ({
  i18nKey,
  options,
  children,
  textStyle,
  ...props
}) => {
  const { t } = useAppTranslation();
  
  return (
    <TouchableOpacity {...props}>
      {i18nKey ? (
        <Text style={textStyle}>{t(i18nKey, options)}</Text>
      ) : (
        children
      )}
    </TouchableOpacity>
  );
};

/**
 * 翻译标题组件
 */
export const THeader: React.FC<ViewProps & {
  i18nKey: string;
  options?: any;
  titleStyle?: TextProps['style'];
}> = ({
  i18nKey,
  options,
  titleStyle,
  ...props
}) => {
  const { t } = useAppTranslation();
  
  return (
    <View {...props}>
      <Text style={titleStyle}>{t(i18nKey, options)}</Text>
    </View>
  );
};

/**
 * 翻译错误消息组件
 */
export const TErrorMessage: React.FC<TextProps & { i18nKey: string; options?: any }> = ({
  i18nKey,
  options,
  style,
  ...props
}) => {
  const { t } = useAppTranslation();
  
  return (
    <Text
      style={[{ color: 'red' }, style]}
      {...props}
    >
      {t(i18nKey, options)}
    </Text>
  );
};

/**
 * 翻译成功消息组件
 */
export const TSuccessMessage: React.FC<TextProps & { i18nKey: string; options?: any }> = ({
  i18nKey,
  options,
  style,
  ...props
}) => {
  const { t } = useAppTranslation();
  
  return (
    <Text
      style={[{ color: 'green' }, style]}
      {...props}
    >
      {t(i18nKey, options)}
    </Text>
  );
};

/**
 * 翻译标签组件
 */
export const TLabel: React.FC<TextProps & { i18nKey: string; options?: any }> = ({
  i18nKey,
  options,
  style,
  ...props
}) => {
  const { t } = useAppTranslation();
  
  return (
    <Text
      style={[{ fontWeight: 'bold' }, style]}
      {...props}
    >
      {t(i18nKey, options)}
    </Text>
  );
};

/**
 * 翻译占位符组件
 */
export const TPlaceholder: React.FC<{ i18nKey: string; options?: any }> = ({
  i18nKey,
  options,
}) => {
  const { t } = useAppTranslation();
  
  return null; // 这个组件不渲染任何内容，只用于获取翻译文本
};

export default {
  TText,
  TButton,
  TTouchableOpacity,
  THeader,
  TErrorMessage,
  TSuccessMessage,
  TLabel,
  TPlaceholder,
  TranslatedText,
};
