import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Image,
  Dimensions,
  Modal,
} from 'react-native';
import { IMAGES } from '../assets/images';

interface SimpleFullTextModalProps {
  visible: boolean;
  onClose: () => void;
  title: string;
  content: string;
  onAgree?: () => void;
  isAgreed?: boolean;
}

const { height } = Dimensions.get('window');

const SimpleFullTextModal: React.FC<SimpleFullTextModalProps> = ({
  visible,
  onClose,
  title,
  content,
  onAgree,
  isAgreed = false,
}) => {
  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="slide"
      onRequestClose={onClose}
    >
      <View style={styles.overlay}>
        <View style={styles.modalContainer}>
          <View style={styles.modalContent}>
          {/* 标题 */}
          <Text style={styles.title}>{title}</Text>
          <View style={styles.divider} />

          {/* 内容 */}
          <ScrollView style={styles.contentScroll}>
            <Text style={styles.content}>
              {/* 简单处理HTML标签 */}
              {typeof content === 'string'
                ? content.replace(/<[^>]*>/g, '') // 移除HTML标签
                : content}
            </Text>
          </ScrollView>

          {/* 底部按钮 */}
          {onAgree && (
            <View style={styles.footer}>
              <TouchableOpacity
                style={styles.checkboxRow}
                onPress={onAgree}
              >
                <Image
                  source={isAgreed ? IMAGES.checkboxOn : IMAGES.checkboxOff}
                  style={styles.checkboxImage}
                />
                <Text style={styles.agreementText}>Agreement</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[styles.button, !isAgreed && styles.buttonDisabled]}
                onPress={onClose}
                disabled={!isAgreed}
              >
                <Text style={styles.buttonText}>NEXT</Text>
                <Image source={IMAGES.arrowRight} style={styles.arrowIcon} />
              </TouchableOpacity>
            </View>
          )}
          </View>
        </View>
        <TouchableOpacity
          style={styles.closeArea}
          onPress={onClose}
          activeOpacity={1}
        />
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  closeArea: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: height * 0.7,
  },
  modalContainer: {
    backgroundColor: '#FFFFFF',
    borderTopLeftRadius: 15,
    borderTopRightRadius: 15,
    height: height * 0.7,
  },
  modalContent: {
    padding: 20,
    flex: 1,
  },
  title: {
    fontSize: 22,
    fontFamily: 'BebasNeue-Regular',
    textAlign: 'center',
    marginBottom: 10,
    color: '#000000',
  },
  divider: {
    height: 1,
    backgroundColor: '#DDDDDD',
    marginBottom: 15,
  },
  contentScroll: {
    flex: 1,
    marginBottom: 15,
  },
  content: {
    fontSize: 16,
    fontFamily: 'Pretendard-Light',
    color: '#333333',
    lineHeight: 24,
  },
  footer: {
    borderTopWidth: 1,
    borderTopColor: '#DDDDDD',
    paddingTop: 15,
  },
  checkboxRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 15,
  },
  checkboxImage: {
    width: 18,
    height: 18,
    resizeMode: 'contain',
    marginRight: 8,
  },
  agreementText: {
    fontSize: 16,
    fontFamily: 'Pretendard-Light',
    color: '#333333',
    marginLeft: 5,
  },
  button: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#FF0066',
    borderRadius: 5,
    paddingVertical: 10,
    paddingHorizontal: 20,
    height: 50,
  },
  buttonDisabled: {
    backgroundColor: '#666666',
  },
  buttonText: {
    fontSize: 16,
    fontFamily: 'BebasNeue-Regular',
    color: '#FFFFFF',
    marginRight: 5,
  },
  arrowIcon: {
    width: 12,
    height: 12,
    tintColor: '#FFFFFF',
  },
});

export default SimpleFullTextModal;
