import React from 'react';
import {
  View,
  Text,
  Modal,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Image,
  Dimensions,
  TouchableWithoutFeedback,
  SafeAreaView,
} from 'react-native';
import { FONTS } from '../assets/fonts';
import { IMAGES } from '../assets/images';
import { useTranslation } from 'react-i18next';

interface FullTextModalProps {
  visible: boolean;
  onClose: () => void;
  title: string;
  content: string;
  onAgree?: () => void;
  isAgreed?: boolean;
}

const { height } = Dimensions.get('window');

const FullTextModal: React.FC<FullTextModalProps> = ({
  visible,
  onClose,
  title,
  content,
  onAgree,
  isAgreed = false,
}) => {
  const { t } = useTranslation();
  return (
    <Modal
      visible={visible}
      animationType="slide"
      transparent={true}
      onRequestClose={onClose}
    >
      <View style={styles.modalOverlay}>
        <View style={styles.container}>
          {/* 标题 */}
          <View style={styles.headerContainer}>
            <Text style={styles.title}>{title}</Text>
            <View style={styles.divider} />
          </View>

          {/* 滚动内容 */}
          <ScrollView
            style={styles.scrollView}
            contentContainerStyle={styles.scrollViewContent}
            showsVerticalScrollIndicator={true}
            nestedScrollEnabled={true}
          >
            <Text style={styles.contentText}>{content}</Text>
          </ScrollView>

          {/* 底部按钮 */}
          {onAgree && (
            <View style={styles.bottomContainer}>
              <TouchableOpacity
                style={styles.checkboxRow}
                onPress={onAgree}
                activeOpacity={0.7}
              >
                <Image
                  source={isAgreed ? IMAGES.checkboxOn : IMAGES.checkboxOff}
                  style={styles.checkboxImage}
                />
                <Text style={styles.agreementText}>{t('fullTextModal.agreement')}</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[styles.nextButton, !isAgreed && styles.nextButtonDisabled]}
                onPress={onClose}
                disabled={!isAgreed}
              >
                <Text style={styles.nextButtonText}>{t('common.next')}</Text>
                <Image source={IMAGES.arrowRight} style={styles.arrowIcon} />
              </TouchableOpacity>
            </View>
          )}
        </View>

        {/* 点击外部关闭 */}
        <TouchableWithoutFeedback onPress={onClose}>
          <View style={styles.closeOverlay} />
        </TouchableWithoutFeedback>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  closeOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: height * 0.7,
  },
  container: {
    width: '100%',
    height: height * 0.7,
    backgroundColor: '#FFFFFF',
    borderTopLeftRadius: 15,
    borderTopRightRadius: 15,
    overflow: 'hidden',
    padding: 20,
    paddingBottom: 10,
    flexDirection: 'column',
    justifyContent: 'space-between',
  },
  headerContainer: {
    marginBottom: 10,
  },
  title: {
    color: '#000000',
    fontSize: 22,
    fontFamily: 'BebasNeue-Regular',
    textAlign: 'center',
    marginVertical: 10,
  },
  divider: {
    height: 1,
    backgroundColor: '#DDDDDD',
    marginBottom: 10,
  },
  scrollView: {
    flex: 1,
    marginBottom: 10,
    maxHeight: height * 0.4,
  },
  scrollViewContent: {
    paddingBottom: 20,
  },
  contentText: {
    color: '#333333',
    fontSize: 16,
    fontFamily: 'Pretendard-Light',
    lineHeight: 24,
  },
  bottomContainer: {
    paddingTop: 15,
    borderTopWidth: 1,
    borderTopColor: '#DDDDDD',
    paddingBottom: 10,
  },
  checkboxRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 15,
    paddingVertical: 5, // 增加垂直方向的点击区域
  },
  checkboxImage: {
    width: 18,
    height: 18,
    resizeMode: 'contain',
    marginRight: 8,
  },
  agreementText: {
    color: '#333333',
    fontSize: 16,
    fontFamily: 'Pretendard-Light',
    marginLeft: 5,
  },
  nextButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#FF0066',
    borderRadius: 5,
    paddingVertical: 10,
    paddingHorizontal: 20,
    width: '100%',
    height: 50,
  },
  nextButtonDisabled: {
    backgroundColor: '#666666',
  },
  nextButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontFamily: 'BebasNeue-Regular',
    marginRight: 5,
    letterSpacing: 1,
  },
  arrowIcon: {
    width: 12,
    height: 12,
    tintColor: '#FFFFFF',
  },
});

export default FullTextModal;
