import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  Modal,
  TouchableOpacity,
  Image,
} from 'react-native';
import { FONTS } from '../assets/fonts';
import { useTranslation } from 'react-i18next';

interface NotificationPermissionModalProps {
  visible: boolean;
  onAllow: () => void;
  onDeny: () => void;
}

const NotificationPermissionModal: React.FC<NotificationPermissionModalProps> = ({
  visible,
  onAllow,
  onDeny,
}) => {
  const { t } = useTranslation();

  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="fade"
    >
      <View style={styles.modalContainer}>
        <View style={styles.modalContent}>
          {/* 通知图标 */}
          <View style={styles.iconContainer}>
            <Image
              source={require('../assets/images/notification-bell.png')}
              style={styles.bellIcon}
              defaultSource={require('../assets/images/notification-bell.png')}
            />
          </View>

          {/* 通知文本 */}
          <Text style={styles.titleText}>
            {t('notifications.allowText')} <Text style={styles.appNameText}>AIVGEN</Text> {t('notifications.toSendNotification')}
          </Text>

          {/* 按钮 */}
          <TouchableOpacity
            style={styles.allowButton}
            onPress={onAllow}
          >
            <Text style={styles.allowButtonText}>{t('notifications.allow')}</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.denyButton}
            onPress={onDeny}
          >
            <Text style={styles.denyButtonText}>{t('notifications.dontAllow')}</Text>
          </TouchableOpacity>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
  },
  modalContent: {
    width: '90%',
    backgroundColor: '#222222',
    borderRadius: 10,
    padding: 20,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#D9C091',
  },
  iconContainer: {
    width: 60,
    height: 60,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 20,
  },
  bellIcon: {
    width: 40,
    height: 40,
    tintColor: '#FFFFFF',
  },
  titleText: {
    color: '#FFFFFF',
    fontSize: 18,
    fontFamily: FONTS.pretendard.light,
    textAlign: 'center',
    marginBottom: 30,
  },
  appNameText: {
    fontFamily: FONTS.bebasNeue.regular,
    fontWeight: 'bold',
  },
  allowButton: {
    width: '100%',
    height: 50,
    backgroundColor: '#007AFF',
    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 10,
  },
  allowButtonText: {
    color: '#FFFFFF',
    fontSize: 18,
    fontFamily: FONTS.pretendard.light,
    fontWeight: 'bold',
  },
  denyButton: {
    width: '100%',
    height: 50,
    backgroundColor: '#FF3B30',
    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
  },
  denyButtonText: {
    color: '#FFFFFF',
    fontSize: 18,
    fontFamily: FONTS.pretendard.light,
    fontWeight: 'bold',
  },
});

export default NotificationPermissionModal;
