import React, { useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
  Dimensions,
  TouchableWithoutFeedback,
  Animated,
  ActivityIndicator,
} from 'react-native';
import { FONTS } from '../assets/fonts';
import { useTranslation } from 'react-i18next';

const { width, height } = Dimensions.get('window');

interface SignOutConfirmModalProps {
  visible: boolean;
  onConfirm: () => void;
  onCancel: () => void;
  isLoading?: boolean;
}

const SignOutConfirmModal: React.FC<SignOutConfirmModalProps> = ({
  visible,
  onConfirm,
  onCancel,
  isLoading = false,
}) => {
  const slideAnim = useRef(new Animated.Value(height)).current;
  const { t } = useTranslation();

  useEffect(() => {
    if (visible) {
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }).start();
    } else {
      Animated.timing(slideAnim, {
        toValue: height,
        duration: 300,
        useNativeDriver: true,
      }).start();
    }
  }, [visible, slideAnim]);
  if (!visible) return null;

  return (
    <Modal
      transparent
      visible={visible}
      animationType="none"
      onRequestClose={onCancel}
    >
      <TouchableWithoutFeedback onPress={onCancel}>
        <View style={styles.overlay}>
          <TouchableWithoutFeedback>
            <Animated.View
              style={[
                styles.container,
                {
                  transform: [{ translateY: slideAnim }],
                },
              ]}>
              {/* 顶部拖动条 */}
              <View style={styles.handleContainer}>
                <View style={styles.handle} />
              </View>

              {/* 标题 */}
              <Text style={styles.title}>{t('auth.signOut')}</Text>

              {/* 分隔线 */}
              <View style={styles.separator} />

              {/* 内容 */}
              <View style={styles.contentContainer}>
                <Text style={styles.message}>
                  {t('auth.signOutConfirmMessage')}
                </Text>

                {/* 确认按钮 */}
                <TouchableOpacity
                  style={styles.confirmButton}
                  onPress={onConfirm}
                  disabled={isLoading}
                >
                  {isLoading ? (
                    <ActivityIndicator color="#FFFFFF" size="small" />
                  ) : (
                    <Text style={styles.confirmButtonText}>{t('auth.signOut')}</Text>
                  )}
                </TouchableOpacity>
              </View>

              {/* 关闭按钮 */}
              <TouchableOpacity
                style={styles.closeButton}
                onPress={onCancel}
              >
                <Text style={styles.closeButtonText}>×</Text>
              </TouchableOpacity>
            </Animated.View>
          </TouchableWithoutFeedback>
        </View>
      </TouchableWithoutFeedback>
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  container: {
    backgroundColor: '#1A1A1A',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingBottom: 30,
    width: '90%',
    alignSelf: 'center',
    marginHorizontal: '5%',
    position: 'relative',
  },
  handleContainer: {
    alignItems: 'center',
    paddingTop: 15,
    paddingBottom: 10,
  },
  handle: {
    width: 40,
    height: 5,
    backgroundColor: '#666',
    borderRadius: 3,
  },
  title: {
    color: '#FFFFFF',
    fontSize: 24,
    fontFamily: FONTS.bebasNeue.regular,
    textAlign: 'center',
    marginBottom: 10,
    letterSpacing: 1,
  },
  separator: {
    height: 1,
    backgroundColor: '#333333',
    width: '100%',
  },
  contentContainer: {
    padding: 25,
    alignItems: 'center',
  },
  message: {
    color: '#FFFFFF',
    fontSize: 16,
    fontFamily: FONTS.pretendard.light,
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 20,
  },
  confirmButton: {
    backgroundColor: '#FF0050',
    width: '100%',
    height: 60,
    borderRadius: 30,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 20,
  },
  confirmButtonText: {
    color: '#FFFFFF',
    fontSize: 18,
    fontFamily: FONTS.bebasNeue.regular,
    letterSpacing: 1,
  },
  closeButton: {
    position: 'absolute',
    top: 10,
    right: 10,
    width: 30,
    height: 30,
    justifyContent: 'center',
    alignItems: 'center',
  },
  closeButtonText: {
    color: '#FFFFFF',
    fontSize: 24,
    fontWeight: 'bold',
  },
});

export default SignOutConfirmModal;
