import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
  Dimensions,
} from 'react-native';
import { FONTS } from '../assets/fonts';

// 语言选项类型
export type LanguageOption = 'ENGLISH' | 'KOREAN' | 'JAPANESE' | 'CHINESE' | 'THAI';

// 语言显示名称映射
const languageDisplayNames: Record<LanguageOption, string> = {
  'ENGLISH': 'English',
  'KOREAN': '한국어(Korean)',
  'JAPANESE': '日本語(Japanese)',
  'CHINESE': '中文(Chinese)',
  'THAI': 'ภาษาไทย(Thai)',
};

interface LanguageModalProps {
  visible: boolean;
  selectedLanguage: LanguageOption;
  onSelect: (language: LanguageOption) => void;
  onClose: () => void;
}

const { width, height } = Dimensions.get('window');

const LanguageModal: React.FC<LanguageModalProps> = ({
  visible,
  selectedLanguage,
  onSelect,
  onClose,
}) => {
  // 可用的语言选项
  const languageOptions: LanguageOption[] = ['ENGLISH', 'KOREAN', 'JAPANESE', 'CHINESE', 'THAI'];

  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="fade"
      onRequestClose={onClose}
    >
      <TouchableOpacity
        style={styles.modalOverlay}
        activeOpacity={1}
        onPress={onClose}
      >
        <View style={styles.modalContainer}>
          {/* 模态窗口内容，点击时不关闭模态窗口 */}
          <TouchableOpacity
            activeOpacity={1}
            style={styles.modalContent}
            onPress={(e) => e.stopPropagation()}
          >
            {/* 标题 */}
            <Text style={styles.modalTitle}>LANGUAGE</Text>
            
            {/* 分隔线 */}
            <View style={styles.separator} />
            
            {/* 语言选项列表 */}
            {languageOptions.map((language) => (
              <TouchableOpacity
                key={language}
                style={[
                  styles.languageOption,
                  selectedLanguage === language && styles.selectedLanguageOption
                ]}
                onPress={() => {
                  onSelect(language);
                  onClose();
                }}
              >
                <Text style={[
                  styles.languageText,
                  selectedLanguage === language && styles.selectedLanguageText
                ]}>
                  {languageDisplayNames[language]}
                </Text>
                
                {/* 选项之间的分隔线 */}
                {language !== 'THAI' && <View style={styles.optionSeparator} />}
              </TouchableOpacity>
            ))}
          </TouchableOpacity>
        </View>
      </TouchableOpacity>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContainer: {
    width: width * 0.85,
    backgroundColor: '#1A1A1A',
    borderRadius: 15,
    overflow: 'hidden',
  },
  modalContent: {
    width: '100%',
  },
  modalTitle: {
    color: '#D9C091', // 金色文字
    fontSize: 24,
    fontFamily: FONTS.bebasNeue.regular,
    textAlign: 'center',
    paddingVertical: 20,
    letterSpacing: 1,
  },
  separator: {
    height: 1,
    backgroundColor: '#333333',
    width: '100%',
  },
  languageOption: {
    paddingVertical: 20,
    alignItems: 'center',
  },
  selectedLanguageOption: {
    // 选中状态的样式
  },
  languageText: {
    color: '#FFFFFF',
    fontSize: 18,
    fontFamily: FONTS.pretendard.light,
    textAlign: 'center',
  },
  selectedLanguageText: {
    color: '#FFFFFF',
    fontWeight: 'bold',
  },
  optionSeparator: {
    height: 1,
    backgroundColor: '#333333',
    width: '100%',
    position: 'absolute',
    bottom: 0,
  },
});

export default LanguageModal;
