import React, { useState, useRef, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Modal,
  Dimensions,
} from 'react-native';
import { FONTS } from '../assets/fonts';
import { useTranslation } from 'react-i18next';

interface DatePickerModalProps {
  visible: boolean;
  onClose: () => void;
  onConfirm: (year: string, month: string, day: string) => void;
  initialYear?: string;
  initialMonth?: string;
  initialDay?: string;
}

const DatePickerModal = React.memo(({
  visible,
  onClose,
  onConfirm,
  initialYear,
  initialMonth,
  initialDay,
}: DatePickerModalProps) => {
  const { t } = useTranslation();

  // 获取当前日期
  const currentDate = new Date();
  const currentYear = currentDate.getFullYear().toString();
  const currentMonth = String(currentDate.getMonth() + 1).padStart(2, '0');
  const currentDay = String(currentDate.getDate()).padStart(2, '0');

  // 选择器状态
  const [selectedYear, setSelectedYear] = useState(initialYear || currentYear);
  const [selectedMonth, setSelectedMonth] = useState(initialMonth || currentMonth);
  const [selectedDay, setSelectedDay] = useState(initialDay || currentDay);
  const [daysInMonth, setDaysInMonth] = useState(31); // 当前选中月份的天数

  // 创建滚动视图的引用
  const yearScrollViewRef = useRef<ScrollView>(null);
  const monthScrollViewRef = useRef<ScrollView>(null);
  const dayScrollViewRef = useRef<ScrollView>(null);

  // 获取指定年月的天数 - 使用useMemo缓存结果
  const getDaysInMonth = useCallback((year: number, month: number) => {
    // 注意：JavaScript 的 Date 月份是从 0 开始的，所以需要 month - 1
    return new Date(year, month, 0).getDate();
  }, []);

  // 滚动锁定标志，防止无限滚动
  const isScrollingRef = useRef(false);
  const initialScrollDoneRef = useRef(false);

  // 创建一个防抖函数
  const createDebounce = useCallback((func: Function, delay: number) => {
    let timer: ReturnType<typeof setTimeout> | null = null;

    return (...args: any[]) => {
      if (timer) {
        clearTimeout(timer);
      }
      timer = setTimeout(() => func(...args), delay);
    };
  }, []);

  // 应用防抖到滚动处理函数
  const handleYearScroll = useCallback((event: any) => {
    // 立即提取所需的值，避免合成事件被重用的问题
    const offsetY = event.nativeEvent.contentOffset.y;

    const debouncedFn = createDebounce((scrollOffsetY: number) => {
      // 如果初始滚动还没有完成，则不处理
      if (!initialScrollDoneRef.current) {
        return;
      }

      const index = Math.round(scrollOffsetY / 50); // 50 是每个选项的高度
      // 只有在索引有效时才处理
      if (index >= 0 && index < 100) { // 100 是年份选项的数量
        const year = (parseInt(currentYear, 10) - index).toString();
        // 检查选择的年份是否在未来
        const isFutureYear = parseInt(year, 10) > parseInt(currentYear, 10);

        // 如果是未来的年份，则选择当前年份
        if (isFutureYear) {
          setSelectedYear(currentYear);
          // 滚动到当前年份的位置 - 避免在滚动处理函数内触发滚动
          requestAnimationFrame(() => {
            yearScrollViewRef.current?.scrollTo({ y: 0, animated: true });
          });
        } else {
          setSelectedYear(year);
        }

        // 批量处理状态更新
        requestAnimationFrame(() => {
          // 如果选择的是当前年份，则限制月份不能超过当前月份
          if (year === currentYear && parseInt(selectedMonth, 10) > parseInt(currentMonth, 10)) {
            setSelectedMonth(currentMonth);
            // 滚动到当前月份的位置
            monthScrollViewRef.current?.scrollTo({ y: (parseInt(currentMonth, 10) - 1) * 50, animated: true });
          }

          // 更新当前月份的天数，考虑闰年
          const yearToUse = isFutureYear ? currentYear : year;
          const monthToUse = (yearToUse === currentYear && parseInt(selectedMonth, 10) > parseInt(currentMonth, 10)) ? currentMonth : selectedMonth;
          const days = getDaysInMonth(parseInt(yearToUse, 10), parseInt(monthToUse, 10));
          setDaysInMonth(days);

          // 如果当前选中的日期超过了该月的天数，则将其调整为该月的最后一天
          if (parseInt(selectedDay, 10) > days) {
            setSelectedDay(String(days).padStart(2, '0'));
          }

          // 如果选择的是当前年月，限制日期不能超过当天
          if (yearToUse === currentYear && monthToUse === currentMonth && parseInt(selectedDay, 10) > parseInt(currentDay, 10)) {
            setSelectedDay(currentDay);
          }
        });
      }

    }, 50);
    
    // 传递提取的值而不是事件对象
    debouncedFn(offsetY);
  }, [createDebounce, currentYear, currentMonth, currentDay, selectedMonth, selectedDay, getDaysInMonth]);
  
  // 当组件接收到新的初始值时更新状态
  useEffect(() => {
    // 批量更新状态以减少渲染次数
    let needDaysUpdate = false;
    
    if (initialYear) {
      setSelectedYear(initialYear);
      needDaysUpdate = true;
    }
    if (initialMonth) {
      setSelectedMonth(initialMonth);
      needDaysUpdate = true;
    }
    if (initialDay) {
      setSelectedDay(initialDay);
    }

    // 计算并设置当前月份的天数
    if (needDaysUpdate && initialYear && initialMonth) {
      const days = getDaysInMonth(parseInt(initialYear, 10), parseInt(initialMonth, 10));
      setDaysInMonth(days);
    }
  }, [initialYear, initialMonth, initialDay, getDaysInMonth]);
  
  // 当日期选择器显示时，设置初始滚动位置
  useEffect(() => {
    if (visible) {
      initialScrollDoneRef.current = false;
      isScrollingRef.current = true;

      // 使用requestAnimationFrame确保在下一帧渲染前执行，提高性能
      requestAnimationFrame(() => {
        // 计算年份的偏移量，当前年份与最新年份的差值 * 每个选项的高度
        const yearIndex = parseInt(currentYear, 10) - parseInt(selectedYear, 10);
        const yearOffset = yearIndex * 50; // 50 是每个选项的高度
        yearScrollViewRef.current?.scrollTo({ y: yearOffset, animated: false });

        // 计算月份的偏移量，月份从 1 开始，所以需要 -1
        const monthIndex = parseInt(selectedMonth, 10) - 1;
        const monthOffset = monthIndex * 50;
        monthScrollViewRef.current?.scrollTo({ y: monthOffset, animated: false });

        // 计算日期的偏移量，日期从 1 开始，所以需要 -1
        const dayIndex = parseInt(selectedDay, 10) - 1;
        const dayOffset = dayIndex * 50;
        dayScrollViewRef.current?.scrollTo({ y: dayOffset, animated: false });

        // 设置标志表示初始滚动已完成，延迟时间略微延长以确保渲染完成
        setTimeout(() => {
          initialScrollDoneRef.current = true;
          isScrollingRef.current = false;
        }, 600);
      });
    } else {
      // 当日期选择器关闭时重置标志
      initialScrollDoneRef.current = false;
      isScrollingRef.current = false;
    }
  }, [visible, selectedYear, selectedMonth, selectedDay, currentYear]);



  // 检查日期是否超过当前日期
  const isDateInFuture = (year: string, month: string, day: string) => {
    const selectedDate = new Date(
      parseInt(year, 10),
      parseInt(month, 10) - 1,
      parseInt(day, 10)
    );
    const today = new Date();

    // 将时间部分设置为0，只比较日期
    today.setHours(0, 0, 0, 0);
    selectedDate.setHours(0, 0, 0, 0);

    return selectedDate > today;
  };

  // 处理月份滚动并更新状态
  const updateMonthSelection = useCallback((offsetY: number) => {
    // 如果初始滚动还没有完成，则不处理
    if (!initialScrollDoneRef.current) {
      return;
    }

    const index = Math.round(offsetY / 50);
    // 只有在索引有效时才处理
    if (index >= 0 && index < 12) { // 12 是月份选项的数量
      const month = String(index + 1).padStart(2, '0');
      // 检查选择的月份是否在未来
      const isFutureMonth = parseInt(selectedYear, 10) === parseInt(currentYear, 10) && parseInt(month, 10) > parseInt(currentMonth, 10);

      // 如果是未来的月份，则选择当前月份
      if (isFutureMonth) {
        setSelectedMonth(currentMonth);
        // 滚动到当前月份的位置 - 使用requestAnimationFrame优化渲染
        requestAnimationFrame(() => {
          monthScrollViewRef.current?.scrollTo({ y: (parseInt(currentMonth, 10) - 1) * 50, animated: true });
        });
      } else {
        setSelectedMonth(month);
      }

      // 使用requestAnimationFrame优化后续更新
      requestAnimationFrame(() => {
        // 更新当前月份的天数
        const monthToUse = isFutureMonth ? currentMonth : month;
        const days = getDaysInMonth(parseInt(selectedYear, 10), parseInt(monthToUse, 10));
        setDaysInMonth(days);

        // 如果当前选中的日期超过了该月的天数，则将其调整为该月的最后一天
        if (parseInt(selectedDay, 10) > days) {
          setSelectedDay(String(days).padStart(2, '0'));
        }

        // 如果选择的是当前年月，限制日期不能超过当天
        if (selectedYear === currentYear && monthToUse === currentMonth && parseInt(selectedDay, 10) > parseInt(currentDay, 10)) {
          setSelectedDay(currentDay);
        }
      });
    }

    // 重置滚动标志
    setTimeout(() => {
      isScrollingRef.current = false;
    }, 100);
  }, [selectedYear, currentYear, currentMonth, currentDay, selectedDay, getDaysInMonth]);

  // 应用防抖到月份滚动处理函数
  const handleMonthScroll = useCallback((event: any) => {
    // 立即提取所需的值，而不是将事件对象传递给防抖函数
    const offsetY = event.nativeEvent.contentOffset.y;
    const debouncedFn = createDebounce(updateMonthSelection, 50);
    debouncedFn(offsetY);
  }, [createDebounce, updateMonthSelection]);
  
  // 处理月份滚动结束事件，确保快速滚动时也能选中正确的月份
  const handleMonthScrollEnd = useCallback((event: any) => {
    const offsetY = event.nativeEvent.contentOffset.y;
    updateMonthSelection(offsetY);
  }, [updateMonthSelection]);

  // 处理日期滚动并更新状态
  const updateDaySelection = useCallback((offsetY: number) => {
    // 如果初始滚动还没有完成，则不处理
    if (!initialScrollDoneRef.current) {
      return;
    }

    const index = Math.round(offsetY / 50);

    // 限制日期范围
    let maxDay = daysInMonth;

    // 如果是当前年月，限制日期不能超过当天
    if (selectedYear === currentYear && selectedMonth === currentMonth) {
      maxDay = Math.min(daysInMonth, parseInt(currentDay, 10));
    }

    if (index >= 0 && index < maxDay) { // 使用计算出的最大日期
      const day = String(index + 1).padStart(2, '0');
      setSelectedDay(day);
    } else if (index >= maxDay) {
      // 如果超过了最大日期，则设置为最大日期
      setSelectedDay(String(maxDay).padStart(2, '0'));
      // 滚动到最大日期的位置 - 使用requestAnimationFrame优化渲染
      requestAnimationFrame(() => {
        dayScrollViewRef.current?.scrollTo({ y: (maxDay - 1) * 50, animated: true });
      });
    }

    // 重置滚动标志
    setTimeout(() => {
      isScrollingRef.current = false;
    }, 100);
  }, [selectedYear, selectedMonth, currentYear, currentMonth, currentDay, daysInMonth]);

  // 应用防抖到日期滚动处理函数
  const handleDayScroll = useCallback((event: any) => {
    // 立即提取所需的值，而不是将事件对象传递给防抖函数
    const offsetY = event.nativeEvent.contentOffset.y;
    const debouncedFn = createDebounce(updateDaySelection, 50);
    debouncedFn(offsetY);
  }, [createDebounce, updateDaySelection]);
  
  // 处理日期滚动结束事件，确保快速滚动时也能选中正确的日期
  const handleDayScrollEnd = useCallback((event: any) => {
    const offsetY = event.nativeEvent.contentOffset.y;
    updateDaySelection(offsetY);
  }, [updateDaySelection]);

  // 确认日期选择
  const confirmDateSelection = () => {
    // 检查选择的日期是否在未来
    if (isDateInFuture(selectedYear, selectedMonth, selectedDay)) {
      // 如果是未来的日期，则重置为当前日期
      setSelectedYear(currentYear);
      setSelectedMonth(currentMonth);
      setSelectedDay(currentDay);

      // 调用回调函数，使用当前日期
      onConfirm(currentYear, currentMonth, currentDay);
    } else {
      // 如果不是未来的日期，则正常调用回调函数
      onConfirm(selectedYear, selectedMonth, selectedDay);
    }
  };



  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="slide"
      onRequestClose={onClose}
    >
      <View style={styles.modalOverlay}>
        <View style={styles.datePickerModalContainer}>
          <View style={styles.datePickerHeader}>
            <View style={styles.datePickerTitleIndicator} />
            <Text style={styles.datePickerTitle}>{t('datePicker.selectYearOfBirth')}</Text>
          </View>

          <View style={styles.datePickerSeparator} />

          <View style={styles.datePickerLabels}>
            <Text style={styles.datePickerLabel}>{t('datePicker.year')}</Text>
            <Text style={styles.datePickerLabel}>{t('datePicker.month')}</Text>
            <Text style={styles.datePickerLabel}>{t('datePicker.day')}</Text>
          </View>

          <View style={styles.datePickerSelectors}>
            {/* 选中行的覆盖层 */}
            <View style={styles.selectedRowOverlay} />

            {/* 年份选择器 */}
            <ScrollView
              ref={yearScrollViewRef}
              style={styles.datePickerColumn}
              showsVerticalScrollIndicator={false}
              contentContainerStyle={styles.scrollViewContent}
              snapToInterval={50}
              decelerationRate="fast"
              onScroll={handleYearScroll}
              scrollEventThrottle={32}
            >
              {Array.from({ length: 100 }, (_, i) => {
                // 使用外部已定义的 currentYear 变量
                const year = (parseInt(currentYear, 10) - i).toString();
                return (
                  <View
                    key={`year-${i}`}
                    style={styles.datePickerItem}
                  >
                    <Text style={[styles.datePickerItemText, selectedYear === year && styles.selectedDatePickerItemText]}>{year}</Text>
                  </View>
                );
              })}
            </ScrollView>

            {/* 月份选择器 */}
            <ScrollView
              ref={monthScrollViewRef}
              style={styles.datePickerColumn}
              showsVerticalScrollIndicator={false}
              contentContainerStyle={styles.scrollViewContent}
              snapToInterval={50}
              decelerationRate="fast"
              onScroll={handleMonthScroll}
              onMomentumScrollEnd={handleMonthScrollEnd}
              scrollEventThrottle={32}
            >
              {Array.from({ length: 12 }, (_, i) => {
                const month = String(i + 1).padStart(2, '0');
                // 判断是否是当前年，如果是，则限制月份不能超过当前月
                const isCurrentYear = selectedYear === currentYear;
                const isDisabled = isCurrentYear && (i + 1) > parseInt(currentMonth, 10);

                return (
                  <View
                    key={`month-${i}`}
                    style={styles.datePickerItem}
                  >
                    <Text
                      style={[
                        styles.datePickerItemText,
                        selectedMonth === month && styles.selectedDatePickerItemText,
                        isDisabled && styles.disabledDatePickerItemText,
                      ]}
                    >
                      {month}
                    </Text>
                  </View>
                );
              })}
            </ScrollView>

            {/* 日期选择器 */}
            <ScrollView
              ref={dayScrollViewRef}
              style={styles.datePickerColumn}
              showsVerticalScrollIndicator={false}
              contentContainerStyle={styles.scrollViewContent}
              snapToInterval={50}
              decelerationRate="fast"
              onScroll={handleDayScroll}
              onMomentumScrollEnd={handleDayScrollEnd}
              scrollEventThrottle={32}
            >
              {Array.from({ length: daysInMonth }, (_, i) => {
                const day = String(i + 1).padStart(2, '0');
                // 判断是否是当前年月，如果是，则限制日期不能超过当天
                const isCurrentYearMonth = selectedYear === currentYear && selectedMonth === currentMonth;
                const isDisabled = isCurrentYearMonth && (i + 1) > parseInt(currentDay, 10);

                return (
                  <View
                    key={`day-${i}`}
                    style={styles.datePickerItem}
                  >
                    <Text
                      style={[
                        styles.datePickerItemText,
                        selectedDay === day && styles.selectedDatePickerItemText,
                        isDisabled && styles.disabledDatePickerItemText,
                      ]}
                    >
                      {day}
                    </Text>
                  </View>
                );
              })}
            </ScrollView>
          </View>

          <View style={styles.datePickerButtons}>
            <TouchableOpacity
              style={styles.cancelButton}
              onPress={onClose}
            >
              <Text style={styles.cancelButtonText}>{t('common.cancel')}</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.submitButton}
              onPress={confirmDateSelection}
            >
              <Text style={styles.submitButtonText}>{t('datePicker.submit')}</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
});

const { width } = Dimensions.get('window');

const styles = StyleSheet.create({
  // 日期选择器模态框样式
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    justifyContent: 'flex-end', // 改为底部对齐
    alignItems: 'center',
  },
  datePickerModalContainer: {
    width: width - 40, // 左右各有 20 的边距
    marginHorizontal: 20,
    backgroundColor: '#222',
    borderTopLeftRadius: 10,
    borderTopRightRadius: 10,
    overflow: 'hidden',
  },
  datePickerHeader: {
    paddingVertical: 15,
    alignItems: 'center',
    justifyContent: 'center',
    position: 'relative',
  },
  datePickerTitle: {
    color: '#D9C091',
    fontSize: 20,
    fontFamily: FONTS.bebasNeue.regular,
    letterSpacing: 1,
    marginBottom: 10,
    marginTop: 30,
  },
  datePickerTitleIndicator: {
    width: '30%',
    height: 5,
    backgroundColor: '#767676',
    borderRadius: 5,
  },
  datePickerSeparator: {
    height: 1,
    backgroundColor: '#333',
    width: '100%',
  },
  datePickerLabels: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingVertical: 15,
  },
  datePickerLabel: {
    color: '#999',
    fontSize: 16,
    fontFamily: FONTS.pretendard.light,
    flex: 1,
    textAlign: 'center',
  },
  datePickerSelectors: {
    flexDirection: 'row',
    height: 250,
    position: 'relative',
  },
  datePickerColumn: {
    flex: 1,
  },
  scrollViewContent: {
    paddingVertical: 100,
  },
  datePickerItem: {
    height: 50,
    alignItems: 'center',
    justifyContent: 'center',
  },
  datePickerItemText: {
    color: '#666',
    fontSize: 20,
    fontFamily: FONTS.bebasNeue.regular,
  },
  // 选中行的样式
  selectedRowOverlay: {
    position: 'absolute',
    top: '50%',
    marginTop: -25,
    left: 0,
    right: 0,
    height: 50,
    borderTopWidth: 1,
    borderBottomWidth: 1,
    borderColor: '#444',
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    zIndex: 1,
  },
  // 选中项的样式
  selectedDatePickerItemText: {
    color: '#FFFFFF',
    fontFamily: FONTS.bebasNeue.regular,
    fontSize: 20,
  },
  // 禁用项的样式
  disabledDatePickerItemText: {
    color: '#444444',
    opacity: 0.5,
  },
  datePickerButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: 20,
    paddingBottom: 30,
  },
  cancelButton: {
    flex: 1,
    height: 50,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 10,
    borderRadius: 5,
    backgroundColor: '#FF0066',
  },
  submitButton: {
    flex: 1,
    height: 50,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 10,
    borderRadius: 5,
    backgroundColor: '#007BFF',
  },
  cancelButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontFamily: FONTS.pretendard.light,
  },
  submitButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontFamily: FONTS.pretendard.light,
    fontWeight: 'bold',
  },
});

export default DatePickerModal;
