import React, { useEffect, useRef, useState } from 'react';
import {
  View,
  Text,
  Modal,
  StyleSheet,
  TouchableOpacity,
  Dimensions,
  Image,
  Animated,
  TouchableWithoutFeedback,
  ActivityIndicator,
} from 'react-native';
import { FONTS } from '../assets/fonts';
import { IMAGES } from '../assets/images';
import { get } from '../services/api/apiService';
import { showSuccess, showError } from '../services/toast';
import { useTranslation } from 'react-i18next';

interface ForgotPasswordModalProps {
  visible: boolean;
  email: string;
  onClose: () => void;
}

const { width: screenWidth } = Dimensions.get('window');
// 计算按钮高度，基于750设计稿的比例
const buttonHeight = (screenWidth / 750) * 88;

const ForgotPasswordModal: React.FC<ForgotPasswordModalProps> = ({
  visible,
  email,
  onClose
}) => {
  const { t } = useTranslation();

  // 创建动画值
  const slideAnim = useRef(new Animated.Value(0)).current;
  // 加载状态
  const [isLoading, setIsLoading] = useState(false);

  // 当visible变化时，执行动画
  useEffect(() => {
    if (visible) {
      // 显示时，从底部滑入
      Animated.timing(slideAnim, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }).start();
    } else {
      // 隐藏时，滑出到底部
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }).start();
    }
  }, [visible, slideAnim]);
  // 计算动画的平移值，从底部往上移动
  const translateY = slideAnim.interpolate({
    inputRange: [0, 1],
    outputRange: [300, 0], // 从底部往上移动300像素
  });

  return (
    <Modal
      transparent
      visible={visible}
      animationType="none"
      onRequestClose={onClose}
    >
      <TouchableWithoutFeedback onPress={onClose}>
        <View style={styles.overlay}>
          <TouchableWithoutFeedback onPress={(e) => e.stopPropagation()}>
            <Animated.View
              style={[
                styles.container,
                { transform: [{ translateY }] }
              ]}
            >
              <View style={styles.handle} />

              {/* 彩色块 */}

              <Text style={styles.title}>{t('forgotPassword.newPasswordSent')}</Text>
              <Text style={styles.subtitle}>{t('forgotPassword.toEmailBelow')}</Text>


              <View style={styles.emailContainer}>
                <Image source={IMAGES.emailIcon} style={styles.emailIcon} />
                <Text style={styles.email}>{email}</Text>
              </View>

              <Text style={styles.instruction}>{t('forgotPassword.checkEmail')}</Text>

              <TouchableOpacity
                style={styles.button}
                onPress={async () => {
                  if (!email || !email.includes('@')) {
                    showError(t('auth.invalidEmail'));
                    return;
                  }

                  try {
                    setIsLoading(true);
                    // 调用发送验证码接口
                    const response = await get(`/send`, { email });
                    console.log('Send verification code response:', response.data);

                    if (response.data && response.data.code === '200') {
                      // 成功发送验证码
                      showSuccess(t('forgotPassword.codeSentSuccess'));
                      // 关闭弹窗
                    } else {
                      // 发送失败
                      showError(response.data?.msg || t('forgotPassword.codeSentFailed'));
                    }
                    onClose();

                  } catch (error) {
                    console.error('Error sending verification code:', error);
                    showError(t('forgotPassword.codeError'));
                  } finally {
                    setIsLoading(false);
                  }
                }}
                disabled={isLoading}
              >
                {isLoading ? (
                  <ActivityIndicator color="#FFFFFF" size="small" />
                ) : (
                  <>
                    <Image source={IMAGES.sendIcon} style={styles.sendIcon} />
                    <Text style={styles.buttonText}>{t('common.send')}</Text>
                  </>
                )}
              </TouchableOpacity>
            </Animated.View>
          </TouchableWithoutFeedback>
        </View>
      </TouchableWithoutFeedback>
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end', // 将内容对齐到底部
    alignItems: 'center',
  },
  container: {
    width: '100%',
    backgroundColor: '#222222',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    overflow: 'hidden',
    paddingBottom: 30,
    paddingTop: 10,
    paddingHorizontal: 20, // 添加左右间距
    alignItems: 'center',
  },
  handle: {
    width: '40%',
    height: 5,
    backgroundColor: '#444444',
    borderRadius: 5,
    marginBottom: 20,
    marginTop: 10,
  },
  title: {
    color: '#FFFFFF',
    fontSize: 20,
    fontFamily: FONTS.bebasNeue.regular,
    textAlign: 'center',
    letterSpacing: 1,
    marginTop: 10,
  },
  subtitle: {
    color: '#FFFFFF',
    fontSize: 20,
    fontFamily: FONTS.bebasNeue.regular,
    textAlign: 'center',
    letterSpacing: 1,
    marginBottom: 30,
  },
  emailContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
  },
  emailIcon: {
    width: 24,
    height: 24,
    marginRight: 10,
    tintColor: '#FFFFFF',
  },
  email: {
    color: '#FFFFFF',
    fontSize: 16,
    fontFamily: FONTS.pretendard.light,
  },
  instruction: {
    color: '#FFFFFF',
    fontSize: 14,
    fontFamily: FONTS.pretendard.light,
    marginBottom: 30,
  },
  button: {
    backgroundColor: '#FF0066',
    paddingHorizontal: 30,
    borderRadius: 5,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    width: '90%',
    marginTop: 10,
    height: buttonHeight, // 使用计算出的按钮高度（基于750的设计稿比例）
    marginHorizontal: 20, // 添加左右间距
  },
  sendIcon: {
    width: 24,
    height: 24,
    marginRight: 10,
    tintColor: '#FFFFFF',
  },
  buttonText: {
    color: '#FFFFFF',
    fontSize: 18,
    fontFamily: FONTS.bebasNeue.regular,
    letterSpacing: 1,
  },
  colorBlock: {
    width: 60,
    height: 6,
    backgroundColor: '#FF0066',
    marginVertical: 10,
  },
  divider: {
    height: 1,
    backgroundColor: '#444444',
    width: '100%',
    marginVertical: 10,
  },
});

export default ForgotPasswordModal;
