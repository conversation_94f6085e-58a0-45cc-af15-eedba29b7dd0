import React from 'react';
import {
  View,
  Text,
  Modal,
  StyleSheet,
  TouchableOpacity,
  Dimensions,
} from 'react-native';
import { FONTS } from '../assets/fonts';
import { useTranslation } from 'react-i18next';

interface AlertModalProps {
  visible: boolean;
  message: string;
  onClose: () => void;
}

const { width } = Dimensions.get('window');

const AlertModal: React.FC<AlertModalProps> = ({ visible, message, onClose }) => {
  const { t } = useTranslation();

  return (
    <Modal
      transparent
      visible={visible}
      animationType="fade"
      onRequestClose={onClose}
    >
      <View style={styles.overlay}>
        <View style={styles.container}>
          <Text style={styles.message}>{message}</Text>
          <View style={styles.divider} />
          <TouchableOpacity style={styles.button} onPress={onClose}>
            <Text style={styles.buttonText}>{t('common.ok')}</Text>
          </TouchableOpacity>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  container: {
    width: width * 0.85,
    backgroundColor: '#222222',
    borderRadius: 12,
    overflow: 'hidden',
    paddingBottom: 10,
  },
  message: {
    color: '#FF0066',
    fontSize: 16,
    fontFamily: FONTS.pretendard.light,
    textAlign: 'center',
    paddingVertical: 30,
    paddingHorizontal: 20,
  },
  divider: {
    height: 1,
    backgroundColor: '#444444',
    marginHorizontal: 0,
    marginVertical: 10,
  },
  button: {
    backgroundColor: '#007AFF',
    paddingVertical: 15,
    alignItems: 'center',
    margin: 15,
    borderRadius: 8,
  },
  buttonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontFamily: FONTS.pretendard.light,
    fontWeight: '500',
  },
});

export default AlertModal;
