import React from 'react';
import { View, TouchableOpacity, StyleSheet, Animated } from 'react-native';
import LinearGradient from 'react-native-linear-gradient';

interface CustomSwitchProps {
  value: boolean;
  onValueChange: (value: boolean) => void;
  disabled?: boolean;
}

const CustomSwitch: React.FC<CustomSwitchProps> = ({ value, onValueChange, disabled = false }) => {
  // 创建动画值
  const switchAnim = React.useRef(new Animated.Value(value ? 1 : 0)).current;

  // 当值改变时，执行动画
  React.useEffect(() => {
    Animated.timing(switchAnim, {
      toValue: value ? 1 : 0,
      duration: 200,
      useNativeDriver: false,
    }).start();
  }, [value, switchAnim]);

  // 计算滑块的位置
  const translateX = switchAnim.interpolate({
    inputRange: [0, 1],
    outputRange: [4, 24],
  });

  // 处理点击事件
  const handlePress = () => {
    if (!disabled) {
      onValueChange(!value);
    }
  };

  return (
    <TouchableOpacity
      activeOpacity={0.8}
      onPress={handlePress}
      disabled={disabled}
      style={styles.container}
    >
      <View style={[styles.track, !value && styles.trackOff]}>
        {value && (
          <LinearGradient
            colors={['#AD00FF', '#0237BD']}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 0 }}
            style={styles.gradient}
          />
        )}
        <Animated.View
          style={[
            styles.thumb,
            { transform: [{ translateX }] },
            disabled && styles.thumbDisabled,
          ]}
        />
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 5,
  },
  track: {
    width: 51,
    height: 31,
    borderRadius: 15.5,
    backgroundColor: '#333333',
    justifyContent: 'center',
    overflow: 'hidden',
  },
  trackOff: {
    backgroundColor: '#333333',
  },
  gradient: {
    position: 'absolute',
    left: 0,
    right: 0,
    top: 0,
    bottom: 0,
  },
  thumb: {
    width: 23,
    height: 23,
    borderRadius: 11.5,
    backgroundColor: '#FFFFFF',
  },
  thumbDisabled: {
    backgroundColor: '#CCCCCC',
  },
});

export default CustomSwitch;
