import React from 'react';
import { View, StatusBar, StyleSheet, Platform } from 'react-native';

const STATUSBAR_HEIGHT = Platform.OS === 'ios' ? 44 : StatusBar.currentHeight || 0;

const StatusBarPlaceHolder = () => {
  if (Platform.OS === 'ios') {
    return (
      <View style={styles.statusBar}>
        <StatusBar barStyle="light-content" />
      </View>
    );
  }
  return null;
};

const styles = StyleSheet.create({
  statusBar: {
    height: STATUSBAR_HEIGHT,
    backgroundColor: '#000000',
  },
});

export default StatusBarPlaceHolder;
