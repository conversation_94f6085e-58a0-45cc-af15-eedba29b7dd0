#!/bin/bash

# 卸载现有的 React Navigation 依赖
npm uninstall @react-navigation/native @react-navigation/stack @react-navigation/bottom-tabs react-native-screens react-native-safe-area-context react-native-gesture-handler @react-native-masked-view/masked-view

# 安装特定版本的 React Navigation 依赖
npm install @react-navigation/native@6.1.9
npm install @react-navigation/stack@6.3.20
npm install @react-navigation/bottom-tabs@6.5.11
npm install react-native-screens@3.29.0
npm install react-native-safe-area-context@4.8.2
npm install react-native-gesture-handler@2.14.1
npm install @react-native-masked-view/masked-view@0.3.0

# 清理缓存
npm cache clean --force

# 重新启动 Metro 服务器
echo "依赖安装完成，请重新启动 Metro 服务器和应用程序"
