/**
 * 这个脚本用于扫描项目中的所有文件，提取硬编码文本
 * 使用方法: node scripts/extract-texts.js
 */

const fs = require('fs');
const path = require('path');
const glob = require('glob');

// 要扫描的文件类型
const filePatterns = [
  'src/**/*.tsx',
  'src/**/*.ts',
  'src/**/*.js',
  'src/**/*.jsx',
];

// 要排除的目录
const excludeDirs = [
  'node_modules',
  'build',
  'dist',
  'coverage',
  'i18n',
];

// 要排除的文件
const excludeFiles = [
  'i18n.ts',
  'index.ts',
];

// 正则表达式，用于匹配可能的硬编码文本
const textPatterns = [
  /<Text[^>]*>([^<]+)<\/Text>/g,
  /<Text[^>]*>\s*{\s*['"]([^'"]+)['"]\s*}\s*<\/Text>/g,
  /title=['"]([^'"]+)['"]/g,
  /placeholder=['"]([^'"]+)['"]/g,
  /label=['"]([^'"]+)['"]/g,
  /headerTitle=['"]([^'"]+)['"]/g,
  /message=['"]([^'"]+)['"]/g,
  /description=['"]([^'"]+)['"]/g,
  /alert\(['"]([^'"]+)['"]/g,
  /Alert\.alert\(['"]([^'"]+)['"](?:,\s*['"]([^'"]+)['"])?/g,
  /showAlert\(['"]([^'"]+)['"]/g,
  /showSuccess\(['"]([^'"]+)['"]/g,
  /showError\(['"]([^'"]+)['"]/g,
  /showInfo\(['"]([^'"]+)['"]/g,
  /showWarning\(['"]([^'"]+)['"]/g,
];

// 要忽略的文本
const ignoreTexts = [
  '',
  ' ',
  '...',
  '›',
  '•',
  '-',
  '+',
  '*',
  '/',
  '=',
  '<',
  '>',
  '{',
  '}',
  '(',
  ')',
  '[',
  ']',
  '|',
  '\\',
  ':',
  ';',
  ',',
  '.',
  '?',
  '!',
  '@',
  '#',
  '$',
  '%',
  '^',
  '&',
  '_',
  '~',
  '`',
];

// 存储提取的文本
const extractedTexts = new Set();

// 扫描文件
function scanFiles() {
  filePatterns.forEach(pattern => {
    const files = glob.sync(pattern, { ignore: excludeDirs.map(dir => `**/${dir}/**`) });
    
    files.forEach(file => {
      if (excludeFiles.some(excludeFile => file.endsWith(excludeFile))) {
        return;
      }
      
      const content = fs.readFileSync(file, 'utf8');
      
      textPatterns.forEach(pattern => {
        let match;
        while ((match = pattern.exec(content)) !== null) {
          const text = match[1];
          if (text && !ignoreTexts.includes(text) && text.trim().length > 0) {
            extractedTexts.add(text.trim());
          }
          
          // 如果有第二个捕获组（例如 Alert.alert 的第二个参数）
          if (match[2] && !ignoreTexts.includes(match[2]) && match[2].trim().length > 0) {
            extractedTexts.add(match[2].trim());
          }
        }
      });
    });
  });
}

// 将提取的文本组织到翻译文件中
function organizeTexts() {
  const texts = Array.from(extractedTexts).sort();
  
  // 创建翻译对象
  const translations = {};
  
  // 简单分类
  const categories = {
    common: [],
    navigation: [],
    auth: [],
    settings: [],
    alarm: [],
    message: [],
    home: [],
    subscription: [],
    errors: [],
    other: [],
  };
  
  // 根据关键词分类
  texts.forEach(text => {
    if (/OK|Cancel|Confirm|Save|Delete|Edit|Loading|Error|Success|Retry|Back|Next|Search|No data|See All|Close/i.test(text)) {
      categories.common.push(text);
    } else if (/Home|Alarm|Message|Inbox|Menu|Navigation/i.test(text)) {
      categories.navigation.push(text);
    } else if (/Login|Sign[- ]?[Uu]p|Logout|Email|Password|Forgot|Reset|Google|Apple|Account/i.test(text)) {
      categories.auth.push(text);
    } else if (/Setting|Sound|Language|Notification|Dark Mode|About|Version|Privacy|Terms|Contact|Delete Account/i.test(text)) {
      categories.settings.push(text);
    } else if (/Alarm|Time|Sound|Vibration|Repeat|Label|Snooze|Volume|Days|Monday|Tuesday|Wednesday|Thursday|Friday|Saturday|Sunday/i.test(text)) {
      categories.alarm.push(text);
    } else if (/Message|Reply|Forward|Delete Message|Send|Type/i.test(text)) {
      categories.message.push(text);
    } else if (/Welcome|Featured|Trending|New Releases|Recommended|Categories|View All|Celebrity|Trend/i.test(text)) {
      categories.home.push(text);
    } else if (/Subscription|Plan|Upgrade|Renew|Cancel|Payment|Monthly|Yearly|Lifetime/i.test(text)) {
      categories.subscription.push(text);
    } else if (/Error|Failed|Network|Server|Unauthorized|Forbidden|Not Found|Timeout|Unknown/i.test(text)) {
      categories.errors.push(text);
    } else {
      categories.other.push(text);
    }
  });
  
  // 构建翻译对象
  Object.keys(categories).forEach(category => {
    if (categories[category].length > 0) {
      translations[category] = {};
      categories[category].forEach(text => {
        // 创建键名（将文本转换为小驼峰式）
        const key = text
          .toLowerCase()
          .replace(/[^a-z0-9]+(.)/g, (_, char) => char.toUpperCase())
          .replace(/[^a-z0-9]/g, '');
        
        translations[category][key] = text;
      });
    }
  });
  
  return translations;
}

// 生成翻译文件
function generateTranslationFiles(translations) {
  // 确保目录存在
  const dir = path.join(__dirname, '../src/i18n/locales');
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
  }
  
  // 生成英文翻译文件
  const enContent = `export default ${JSON.stringify(translations, null, 2)};`;
  fs.writeFileSync(path.join(dir, 'en.ts'), enContent);
  
  // 生成中文翻译文件（复制英文，供翻译人员填写）
  fs.writeFileSync(path.join(dir, 'zh.ts'), enContent);
  
  // 生成泰语翻译文件（复制英文，供翻译人员填写）
  fs.writeFileSync(path.join(dir, 'th.ts'), enContent);
  
  console.log(`提取了 ${extractedTexts.size} 个文本，已生成翻译文件。`);
}

// 主函数
function main() {
  scanFiles();
  const translations = organizeTexts();
  generateTranslationFiles(translations);
}

main();
