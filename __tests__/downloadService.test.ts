import { downloadAudioFile, checkLocalAudioFile, deleteLocalAudioFile } from '../src/services/downloadService';

// Mock react-native-fs
jest.mock('react-native-fs', () => ({
  DocumentDirectoryPath: '/mock/documents',
  exists: jest.fn(),
  mkdir: jest.fn(),
  downloadFile: jest.fn(),
  stat: jest.fn(),
  readDir: jest.fn(),
  unlink: jest.fn(),
}));

// Mock react-native Platform
jest.mock('react-native', () => ({
  Platform: {
    OS: 'ios',
  },
}));

import RNFS from 'react-native-fs';

describe('downloadService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('downloadAudioFile', () => {
    it('should download audio file successfully', async () => {
      const mockUrl = 'https://example.com/audio.wav';
      const mockAlarmId = 'test-alarm-1';
      const mockLocalPath = '/mock/documents/audio/alarm_test-alarm-1_1234567890.wav';

      // Mock RNFS methods
      (RNFS.exists as jest.Mock)
        .mockResolvedValueOnce(true) // audio directory exists
        .mockResolvedValueOnce(false); // file doesn't exist yet

      (RNFS.downloadFile as jest.Mock).mockReturnValue({
        promise: Promise.resolve({ statusCode: 200 }),
      });

      (RNFS.stat as jest.Mock).mockResolvedValue({
        size: 1024,
      });

      const result = await downloadAudioFile(mockUrl, mockAlarmId);

      expect(RNFS.downloadFile).toHaveBeenCalledWith({
        fromUrl: mockUrl,
        toFile: expect.stringContaining('alarm_test-alarm-1_'),
        progress: expect.any(Function),
      });

      expect(result).toContain('alarm_test-alarm-1_');
      expect(result).toContain('.wav');
    });

    it('should return existing file if already downloaded', async () => {
      const mockUrl = 'https://example.com/audio.wav';
      const mockAlarmId = 'test-alarm-2';

      // Mock RNFS methods
      (RNFS.exists as jest.Mock)
        .mockResolvedValueOnce(true) // audio directory exists
        .mockResolvedValueOnce(true); // file already exists

      (RNFS.stat as jest.Mock).mockResolvedValue({
        size: 1024,
      });

      const result = await downloadAudioFile(mockUrl, mockAlarmId);

      expect(RNFS.downloadFile).not.toHaveBeenCalled();
      expect(result).toContain('alarm_test-alarm-2_');
    });

    it('should throw error for invalid URL', async () => {
      const invalidUrl = 'invalid-url';
      const mockAlarmId = 'test-alarm-3';

      await expect(downloadAudioFile(invalidUrl, mockAlarmId)).rejects.toThrow(
        'Invalid URL provided for audio download'
      );
    });

    it('should throw error if download fails', async () => {
      const mockUrl = 'https://example.com/audio.wav';
      const mockAlarmId = 'test-alarm-4';

      // Mock RNFS methods
      (RNFS.exists as jest.Mock)
        .mockResolvedValueOnce(true) // audio directory exists
        .mockResolvedValueOnce(false); // file doesn't exist yet

      (RNFS.downloadFile as jest.Mock).mockReturnValue({
        promise: Promise.resolve({ statusCode: 404 }),
      });

      await expect(downloadAudioFile(mockUrl, mockAlarmId)).rejects.toThrow(
        'Download failed with status code: 404'
      );
    });
  });

  describe('checkLocalAudioFile', () => {
    it('should find existing audio file', async () => {
      const mockAlarmId = 'test-alarm-5';
      const mockFilePath = '/mock/documents/audio/alarm_test-alarm-5_1234567890.wav';

      (RNFS.exists as jest.Mock).mockResolvedValue(true);
      (RNFS.readDir as jest.Mock).mockResolvedValue([
        {
          name: 'alarm_test-alarm-5_1234567890.wav',
          path: mockFilePath,
        },
        {
          name: 'other-file.mp3',
          path: '/mock/documents/audio/other-file.mp3',
        },
      ]);

      const result = await checkLocalAudioFile(mockAlarmId);

      expect(result).toBe(mockFilePath);
    });

    it('should return null if no matching file found', async () => {
      const mockAlarmId = 'test-alarm-6';

      (RNFS.exists as jest.Mock).mockResolvedValue(true);
      (RNFS.readDir as jest.Mock).mockResolvedValue([
        {
          name: 'other-alarm_123_456.wav',
          path: '/mock/documents/audio/other-alarm_123_456.wav',
        },
      ]);

      const result = await checkLocalAudioFile(mockAlarmId);

      expect(result).toBeNull();
    });

    it('should return null if audio directory does not exist', async () => {
      const mockAlarmId = 'test-alarm-7';

      (RNFS.exists as jest.Mock).mockResolvedValue(false);

      const result = await checkLocalAudioFile(mockAlarmId);

      expect(result).toBeNull();
      expect(RNFS.readDir).not.toHaveBeenCalled();
    });
  });

  describe('deleteLocalAudioFile', () => {
    it('should delete existing file', async () => {
      const mockFilePath = '/mock/documents/audio/test-file.wav';

      (RNFS.exists as jest.Mock).mockResolvedValue(true);
      (RNFS.unlink as jest.Mock).mockResolvedValue(undefined);

      const result = await deleteLocalAudioFile(mockFilePath);

      expect(RNFS.unlink).toHaveBeenCalledWith(mockFilePath);
      expect(result).toBe(true);
    });

    it('should return false if file does not exist', async () => {
      const mockFilePath = '/mock/documents/audio/non-existent-file.wav';

      (RNFS.exists as jest.Mock).mockResolvedValue(false);

      const result = await deleteLocalAudioFile(mockFilePath);

      expect(RNFS.unlink).not.toHaveBeenCalled();
      expect(result).toBe(false);
    });
  });
});
