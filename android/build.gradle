buildscript {
    ext {
        buildToolsVersion = "35.0.0"
        minSdkVersion = 24
        compileSdkVersion = 35
        targetSdkVersion = 35
        ndkVersion = "27.1.12297006"
        kotlinVersion = "2.0.21"

        // AndroidX for react-native-iap
        androidXAnnotation = "1.1.0"
        androidXBrowser = "1.0.0"
    }
    repositories {
        google()
        mavenCentral()
    }
    dependencies {
        classpath("com.android.tools.build:gradle")
        classpath("com.facebook.react:react-native-gradle-plugin")
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlinVersion"
        classpath("com.google.gms:google-services:4.4.1") // Google Services plugin
    }
}

apply plugin: "com.facebook.react.rootproject"
