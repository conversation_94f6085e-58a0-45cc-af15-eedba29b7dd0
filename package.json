{"name": "Aivgen", "version": "0.0.1", "private": true, "scripts": {"android": "react-native run-android", "play": "cd android && ./gradlew installPlayDebug && cd .. && npx react-native log-android", "dev": "adb reverse tcp:8081 tcp:8081 && cd android && ./gradlew installPlayDebug && cd .. && adb shell am start -n com.shinest.aivgen/.MainActivity", "apk": "cd android && ./gradlew installPlayRelease && cd ..", "android:play:build": "cd android && ./gradlew assemblePlayRelease && cd ..", "aab": "cd android && ./gradlew bundlePlayRelease && cd ..", "android:amazon": "cd android && ./gradlew installAmazonDebug && cd ..", "android:amazon:release": "cd android && ./gradlew installAmazonRelease && cd ..", "android:amazon:bundle": "cd android && ./gradlew bundleAmazonRelease && cd ..", "android:log": "npx react-native log-android", "android:reset": "adb shell am force-stop com.shinest.aivgen && adb reverse tcp:8081 tcp:8081 && cd android && ./gradlew clean && cd .. && npm start --reset-cache", "ios": "react-native run-ios", "ios:iphone": "react-native run-ios --simulator=\"iPhone 15\"", "ios:ipad": "react-native run-ios --simulator=\"iPad Pro (12.9-inch) (6th generation)\"", "ios:devices": "xcrun simctl list devices", "ios:pods": "cd ios && pod install && cd ..", "lint": "eslint .", "start": "react-native start", "test": "jest"}, "dependencies": {"@notifee/react-native": "^9.1.8", "@react-native-async-storage/async-storage": "^2.1.2", "@react-native-google-signin/google-signin": "^13.2.0", "@react-native-masked-view/masked-view": "^0.3.2", "@react-navigation/bottom-tabs": "^7.3.10", "@react-navigation/native": "^7.1.6", "@react-navigation/stack": "^7.2.10", "axios": "^1.8.4", "i18next": "^25.1.2", "pretty-format": "^29.7.0", "react": "19.0.0", "react-i18next": "^15.5.1", "react-native": "0.79.0", "react-native-gesture-handler": "^2.25.0", "react-native-iap": "^12.16.2", "react-native-image-picker": "^8.2.0", "react-native-linear-gradient": "^2.8.3", "react-native-reanimated": "^3.17.3", "react-native-reanimated-carousel": "^4.0.2", "react-native-safe-area-context": "^5.3.0", "react-native-screens": "^4.10.0", "react-native-sound": "^0.11.2", "react-native-sound-player": "^0.14.5", "react-native-toast-message": "^2.3.0"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/preset-env": "^7.25.3", "@babel/runtime": "^7.25.0", "@react-native-community/cli": "18.0.0", "@react-native-community/cli-platform-android": "18.0.0", "@react-native-community/cli-platform-ios": "18.0.0", "@react-native/babel-preset": "0.79.0", "@react-native/eslint-config": "0.79.0", "@react-native/metro-config": "0.79.0", "@react-native/typescript-config": "0.79.0", "@types/jest": "^29.5.13", "@types/react": "^19.0.0", "@types/react-test-renderer": "^19.0.0", "eslint": "^8.19.0", "jest": "^29.6.3", "prettier": "2.8.8", "react-test-renderer": "19.0.0", "typescript": "5.0.4"}, "engines": {"node": ">=18"}}