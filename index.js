/**
 * @format
 */

import 'react-native-gesture-handler';
import {AppRegistry} from 'react-native';
import App from './App';
import {name as appName} from './app.json';
import notifee, { EventType } from '@notifee/react-native';

// 注册后台事件处理程序
notifee.onBackgroundEvent(async ({ type, detail }) => {
  const { notification } = detail;

  // 处理通知点击事件
  if (type === EventType.PRESS) {
    console.log('User pressed notification in background', notification);
    console.log('Press action details:', detail.pressAction);

    // 如果有导航数据，可以在这里处理
    if (notification?.data?.screen === 'AlarmTrigger' && notification?.data?.alarmId) {
      // 在后台事件中，我们需要确保应用启动后能正确导航到闹钟页面
      console.log('Alarm notification pressed in background');
      console.log('Alarm ID from notification:', notification.data.alarmId);

      try {
        // 取消当前通知，避免重复
        if (notification.id) {
          await notifee.cancelNotification(notification.id);
        }

        // 保存闹钟ID到 AsyncStorage，以便应用启动时可以使用
        const AsyncStorage = require('@react-native-async-storage/async-storage').default;
        console.log('Saving alarm ID to AsyncStorage:', notification.data.alarmId);

        // 清除之前的数据
        await AsyncStorage.multiRemove([
          '@aivgen:pending_alarm_id',
          '@aivgen:notification_clicked',
          '@aivgen:notification_timestamp',
          '@aivgen:target_screen'
        ]);

        // 保存多个键值对，确保应用能够正确识别通知点击事件
        await AsyncStorage.setItem('@aivgen:pending_alarm_id', notification.data.alarmId.toString());
        await AsyncStorage.setItem('@aivgen:notification_clicked', 'true');
        await AsyncStorage.setItem('@aivgen:notification_timestamp', Date.now().toString());
        await AsyncStorage.setItem('@aivgen:target_screen', 'AlarmTrigger');

        // 打印确认消息
        const savedId = await AsyncStorage.getItem('@aivgen:pending_alarm_id');
        const notificationClicked = await AsyncStorage.getItem('@aivgen:notification_clicked');
        console.log('Verified saved data:');
        console.log('- alarm ID:', savedId);
        console.log('- notification_clicked:', notificationClicked);

        // 不要尝试在后台重新加载 JS 包，这可能导致 Context 为 null
        console.log('Notification data saved to AsyncStorage, app will handle navigation on next launch');

        // 在后台模式下，我们需要创建一个特殊的标记，以便应用启动时知道要导航到哪里
        await notifee.setNotificationCategories([
          {
            id: 'alarm_trigger',
            actions: [
              {
                id: 'open_alarm',
                title: 'Tap to open alarm',
              },
            ],
          },
        ]);
      } catch (error) {
        console.error('Error handling background notification:', error);
      }
    }
  }

  // 处理通知触发事件
  if (type === EventType.DELIVERED) {
    console.log('Notification delivered in background', notification);
  }
});

AppRegistry.registerComponent(appName, () => App);
