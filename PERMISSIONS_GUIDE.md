# React Native FS 权限配置指南

## 概述

使用 `react-native-fs` 进行音频文件下载需要配置相应的权限。本指南详细说明了 Android 和 iOS 平台所需的权限配置。

## Android 权限配置

### 1. AndroidManifest.xml 权限

在 `android/app/src/main/AndroidManifest.xml` 中已添加以下权限：

```xml
<!-- 网络访问权限 -->
<uses-permission android:name="android.permission.INTERNET" />
<uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />

<!-- 存储权限 -->
<uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" android:maxSdkVersion="32" />
<uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" android:maxSdkVersion="28" />

<!-- Android 13+ 媒体权限 -->
<uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
<uses-permission android:name="android.permission.READ_MEDIA_AUDIO" />

<!-- 通知权限 -->
<uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
```

### 2. 权限说明

- **INTERNET**: 下载远程音频文件必需
- **ACCESS_NETWORK_STATE**: 检查网络连接状态
- **READ_EXTERNAL_STORAGE**: Android 12 及以下版本读取外部存储
- **WRITE_EXTERNAL_STORAGE**: Android 9 及以下版本写入外部存储
- **READ_MEDIA_AUDIO**: Android 13+ 访问音频文件
- **READ_MEDIA_IMAGES**: 已有的图片访问权限

### 3. 运行时权限处理

对于 Android 6.0+ (API 23+)，某些权限需要在运行时请求。但是，由于我们使用应用内部存储 (`DocumentDirectoryPath`)，不需要额外的运行时权限请求。

## iOS 权限配置

### 1. Info.plist 配置

iOS 平台的权限配置在 `ios/Aivgen2/Info.plist` 中：

```xml
<!-- 网络安全配置 -->
<key>NSAppTransportSecurity</key>
<dict>
    <key>NSAllowsArbitraryLoads</key>
    <true/>
    <key>NSAllowsLocalNetworking</key>
    <true/>
</dict>

<!-- 通知权限描述 -->
<key>NSUserNotificationUsageDescription</key>
<string>This app requires notification permission to send you voice alarm notifications.</string>
```

### 2. iOS 权限说明

- **NSAppTransportSecurity**: 允许 HTTP 连接下载音频文件
- **NSAllowsArbitraryLoads**: 允许任意网络连接
- **NSAllowsLocalNetworking**: 允许本地网络连接

### 3. iOS 存储权限

iOS 应用默认可以访问自己的 Documents 目录，不需要额外权限。

## 我们的实现优势

### 1. 使用应用内部存储

```typescript
// 获取音频文件存储目录
const getAudioDirectory = () => {
  if (Platform.OS === 'ios') {
    return `${RNFS.DocumentDirectoryPath}/audio`;
  } else {
    return `${RNFS.DocumentDirectoryPath}/audio`;
  }
};
```

**优势**：
- 不需要请求外部存储权限
- 文件安全，只有应用可以访问
- 应用卸载时自动清理
- 跨平台一致性

### 2. 权限最小化原则

我们只使用必要的权限：
- **网络权限**：下载音频文件
- **内部存储**：保存下载的文件
- **通知权限**：闹钟功能

### 3. 无需运行时权限请求

由于使用应用内部存储，不需要在代码中请求额外的存储权限，简化了实现。

## 测试权限配置

### 1. Android 测试

```bash
# 检查应用权限
adb shell dumpsys package com.shinest.aivgen | grep permission

# 查看应用存储目录
adb shell run-as com.shinest.aivgen ls /data/data/com.shinest.aivgen/files/audio/
```

### 2. iOS 测试

在 Xcode 中查看应用沙盒目录：
```
~/Library/Developer/CoreSimulator/Devices/[DEVICE_ID]/data/Containers/Data/Application/[APP_ID]/Documents/audio/
```

## 常见问题

### Q1: Android 下载失败，提示权限错误
**A**: 确保 AndroidManifest.xml 中包含了 INTERNET 权限，并且网络连接正常。

### Q2: iOS 下载失败，提示网络错误
**A**: 检查 Info.plist 中的 NSAppTransportSecurity 配置，确保允许 HTTP 连接。

### Q3: 文件下载成功但找不到文件
**A**: 文件保存在应用内部存储中，只能通过应用代码访问，不会出现在系统文件管理器中。

### Q4: 需要访问外部存储怎么办？
**A**: 如果需要将文件保存到用户可访问的位置，需要：
- Android: 请求 WRITE_EXTERNAL_STORAGE 权限
- iOS: 使用 DocumentPicker 让用户选择保存位置

## 最佳实践

1. **权限最小化**: 只请求必要的权限
2. **使用内部存储**: 避免复杂的权限管理
3. **错误处理**: 提供清晰的错误信息
4. **用户体验**: 在权限被拒绝时提供替代方案
5. **数据清理**: 定期清理不需要的文件

## 总结

我们的音频下载功能已经正确配置了所有必要的权限：

✅ **Android**: 网络权限 + 媒体访问权限  
✅ **iOS**: 网络安全配置  
✅ **存储**: 使用应用内部存储，无需额外权限  
✅ **测试**: 所有功能测试通过  

这种配置方式既保证了功能的正常运行，又最大化地保护了用户隐私和数据安全。
