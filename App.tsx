/**
 * Aivgen App
 *
 * @format
 */

import React, { useEffect, useRef } from 'react';
import { AppState, AppStateStatus } from 'react-native';
import 'react-native-gesture-handler';
import 'react-native-reanimated';
import notifee, { EventType } from '@notifee/react-native';
import { navigate, reset } from './src/navigation';

// 导入Toast组件
import { Toast } from './src/services/toast';
import toastConfig from './src/services/toast/toastConfig';
import notificationService from './src/services/notificationService';

// 导入语言上下文
import { LanguageProvider } from './src/context/LanguageContext';
import './src/i18n';

// 导入导航配置
import RootNavigator from './src/navigation';

function App(): React.JSX.Element {
  // 创建 AppState 引用
  const appState = useRef(AppState.currentState);

  // 检查 AsyncStorage 中的通知数据并处理导航
  const checkNotificationData = async () => {
    console.log('Checking notification data after app state change');

    try {
      const AsyncStorage = require('@react-native-async-storage/async-storage').default;
      const notificationClicked = await AsyncStorage.getItem('@aivgen:notification_clicked');
      const targetScreen = await AsyncStorage.getItem('@aivgen:target_screen');
      const pendingAlarmId = await AsyncStorage.getItem('@aivgen:pending_alarm_id');

      console.log('APP STATE CHANGE - AsyncStorage data:');
      console.log('- notification_clicked:', notificationClicked);
      console.log('- target_screen:', targetScreen);
      console.log('- pending_alarm_id:', pendingAlarmId);

      // 如果有通知点击标记，直接导航到目标页面
      if (notificationClicked === 'true' && targetScreen === 'AlarmTrigger' && pendingAlarmId) {
        console.log('APP STATE CHANGE - Found notification click data, navigating to AlarmTrigger');

        // 清除通知点击标记
        await AsyncStorage.multiRemove([
          '@aivgen:notification_clicked',
          '@aivgen:target_screen',
          '@aivgen:pending_alarm_id',
          '@aivgen:notification_timestamp',
        ]);

        // 直接导航到闹钟触发页面
        console.log('APP STATE CHANGE - Executing reset to AlarmTrigger with ID:', pendingAlarmId);
        reset('AlarmTrigger', { alarmId: pendingAlarmId });
      }
    } catch (error) {
      console.error('Error checking notification data after app state change:', error);
    }
  };

  // 监听应用状态变化
  useEffect(() => {
    const subscription = AppState.addEventListener('change', (nextAppState: AppStateStatus) => {
      console.log('App state changed from', appState.current, 'to', nextAppState);

      // 当应用从后台或非活动状态变为活动状态时
      if (
        appState.current.match(/inactive|background/) &&
        nextAppState === 'active'
      ) {
        console.log('App has come to the foreground!');
        checkNotificationData();
      }

      appState.current = nextAppState;
    });

    return () => {
      subscription.remove();
    };
  }, []);

  // 设置通知事件监听器
  useEffect(() => {
    // 创建通知渠道
    const setupNotifications = async () => {
      await notificationService.createChannel();
    };

    // 检查是否有待处理的闹钟通知
    const checkPendingAlarms = async () => {
      try {
        console.log('Checking for pending alarms on app start');

        // 导入 AsyncStorage
        const AsyncStorage = require('@react-native-async-storage/async-storage').default;

        // 首先检查 AsyncStorage 中是否有通知点击标记
        const notificationClicked = await AsyncStorage.getItem('@aivgen:notification_clicked');
        const targetScreen = await AsyncStorage.getItem('@aivgen:target_screen');
        const pendingAlarmId = await AsyncStorage.getItem('@aivgen:pending_alarm_id');
        const timestamp = await AsyncStorage.getItem('@aivgen:notification_timestamp');

        console.log('AsyncStorage check results:');
        console.log('- notification_clicked:', notificationClicked);
        console.log('- target_screen:', targetScreen);
        console.log('- pending_alarm_id:', pendingAlarmId);
        console.log('- timestamp:', timestamp);

        // 如果有通知点击标记，并且时间戳在最近 5 分钟内，则导航到目标页面
        if (notificationClicked === 'true' && targetScreen === 'AlarmTrigger' && pendingAlarmId) {
          const now = Date.now();
          const clickTime = parseInt(timestamp || '0', 10);
          const timeDiff = now - clickTime;

          console.log('Time difference since notification click:', timeDiff, 'ms');

          // 只处理最近 5 分钟内的点击（300000 毫秒）
          if (timeDiff < 300000) {
            console.log('Recent notification click detected, navigating to AlarmTrigger');

            // 清除通知点击标记
            await AsyncStorage.multiRemove([
              '@aivgen:notification_clicked',
              '@aivgen:target_screen',
              '@aivgen:pending_alarm_id',
              '@aivgen:notification_timestamp',
            ]);

            // 使用 reset 函数强制导航到闹钟触发页面
            // 增加延迟时间，确保导航已完全初始化
            setTimeout(() => {
              try {
                console.log('Executing navigation reset to AlarmTrigger with ID:', pendingAlarmId);
                reset('AlarmTrigger', { alarmId: pendingAlarmId });
              } catch (navError) {
                console.error('Navigation error during reset:', navError);
                // 如果导航失败，尝试再次延迟
                setTimeout(() => {
                  try {
                    console.log('Retrying navigation reset to AlarmTrigger with ID:', pendingAlarmId);
                    reset('AlarmTrigger', { alarmId: pendingAlarmId });
                  } catch (retryError) {
                    console.error('Navigation retry failed:', retryError);
                  }
                }, 3000);
              }
            }, 2500);

            return; // 找到并处理了通知，不需要继续检查
          } else {
            console.log('Notification click is too old, ignoring');
            // 清除过期的通知点击标记
            await AsyncStorage.multiRemove([
              '@aivgen:notification_clicked',
              '@aivgen:target_screen',
              '@aivgen:pending_alarm_id',
              '@aivgen:notification_timestamp',
            ]);
          }
        }

        // 1. 检查是否有显示中的通知
        const notifications = await notifee.getDisplayedNotifications();
        console.log('Displayed notifications count:', notifications.length);

        // 查找闹钟通知
        const alarmNotification = notifications.find(
          item => item.notification?.data?.screen === 'AlarmTrigger'
        );

        // 如果找到闹钟通知，导航到闹钟页面
        if (alarmNotification?.notification?.data?.alarmId) {
          const alarmId = alarmNotification.notification.data.alarmId;
          console.log('Found displayed alarm notification with ID:', alarmId);

          // 取消当前通知，避免重复
          if (alarmNotification.id) {
            await notifee.cancelNotification(alarmNotification.id);
          }

          // 使用 reset 函数强制导航到闹钟触发页面
          // 增加延迟时间，确保导航已完全初始化
          setTimeout(() => {
            try {
              console.log('Executing navigation reset to AlarmTrigger with ID:', alarmId);
              reset('AlarmTrigger', { alarmId });
            } catch (navError) {
              console.error('Navigation error during reset:', navError);
              // 如果导航失败，尝试再次延迟
              setTimeout(() => {
                try {
                  console.log('Retrying navigation reset to AlarmTrigger with ID:', alarmId);
                  reset('AlarmTrigger', { alarmId });
                } catch (retryError) {
                  console.error('Navigation retry failed:', retryError);
                }
              }, 3000);
            }
          }, 2500);

          return; // 找到并处理了通知，不需要继续检查
        }

        // 2. 检查初始通知（冷启动情况）
        console.log('Checking for initial notification (cold start case)');
        const initialNotification = await notifee.getInitialNotification();
        console.log('Initial notification:', initialNotification);

        if (initialNotification) {
          console.log('Initial notification data:', initialNotification.notification?.data);

          if (initialNotification?.notification?.data?.screen === 'AlarmTrigger' &&
              initialNotification?.notification?.data?.alarmId) {
            const alarmId = initialNotification.notification.data.alarmId;
            console.log('App was cold started from notification with alarm ID:', alarmId);

            // 取消当前通知，避免重复
            if (initialNotification.notification.id) {
              await notifee.cancelNotification(initialNotification.notification.id);
            }

            // 使用 reset 函数强制导航到闹钟触发页面
            console.log('Executing navigation reset to AlarmTrigger with ID:', alarmId);
            setTimeout(() => {
              reset('AlarmTrigger', { alarmId });
            }, 1500);

            return; // 找到并处理了通知，不需要继续检查
          }
        }

        // 3. 检查是否有闹钟触发的类别
        const categories = await notifee.getNotificationCategories();
        const alarmTriggerCategory = categories.find(cat => cat.id === 'alarm_trigger');

        if (alarmTriggerCategory) {
          console.log('Found alarm trigger category, app was launched from alarm notification');

          // 清除类别，避免下次启动时重复导航
          await notifee.setNotificationCategories([]);
        }
      } catch (error) {
        console.error('Error checking pending alarms:', error);
      }
    };

    // 设置前台事件监听器
    const unsubscribe = notifee.onForegroundEvent(async ({ type, detail }) => {
      const { notification, pressAction } = detail;

      // 处理通知点击事件
      if (type === EventType.PRESS) {
        console.log('User pressed notification in foreground', notification);
        console.log('Press action:', pressAction);

        // 取消当前通知，避免重复
        if (notification?.id) {
          await notifee.cancelNotification(notification.id);
        }

        // 如果有导航数据，处理导航
        if (notification?.data?.screen === 'AlarmTrigger') {
          // 导航到闹钟触发页面
          const alarmId = notification.data.alarmId;
          console.log('Navigating to AlarmTrigger with ID:', alarmId);

          // 使用导出的 navigate 函数导航到闹钟触发页面
          // 延迟一下，确保导航已经准备好
          setTimeout(() => {
            navigate('AlarmTrigger', { alarmId });
          }, 100);
        }
      }
    });

    // 执行初始化
    console.log('App.tsx: useEffect - App is starting up');

    // 设置通知渠道
    setupNotifications();

    // 检查是否有待处理的闹钟通知
    // 使用 setTimeout 确保导航已经初始化
    setTimeout(async () => {
      console.log('App.tsx: Delayed checkPendingAlarms call');

      // 直接检查 AsyncStorage 中的数据
      try {
        const AsyncStorage = require('@react-native-async-storage/async-storage').default;
        const notificationClicked = await AsyncStorage.getItem('@aivgen:notification_clicked');
        const targetScreen = await AsyncStorage.getItem('@aivgen:target_screen');
        const pendingAlarmId = await AsyncStorage.getItem('@aivgen:pending_alarm_id');

        console.log('DIRECT CHECK - AsyncStorage data:');
        console.log('- notification_clicked:', notificationClicked);
        console.log('- target_screen:', targetScreen);
        console.log('- pending_alarm_id:', pendingAlarmId);

        // 如果有通知点击标记，直接导航到目标页面
        if (notificationClicked === 'true' && targetScreen === 'AlarmTrigger' && pendingAlarmId) {
          console.log('DIRECT NAVIGATION - Found notification click data, navigating to AlarmTrigger');

          // 清除通知点击标记
          await AsyncStorage.multiRemove([
            '@aivgen:notification_clicked',
            '@aivgen:target_screen',
            '@aivgen:pending_alarm_id',
            '@aivgen:notification_timestamp',
          ]);

          // 直接导航到闹钟触发页面
          console.log('DIRECT NAVIGATION - Executing reset to AlarmTrigger with ID:', pendingAlarmId);
          reset('AlarmTrigger', { alarmId: pendingAlarmId });
        } else {
          // 如果没有通知点击标记，执行正常的检查
          checkPendingAlarms();
        }
      } catch (error) {
        console.error('Error in direct AsyncStorage check:', error);
        // 如果直接检查失败，执行正常的检查
        checkPendingAlarms();
      }
    }, 2000);

    // 清理函数
    return () => {
      unsubscribe();
    };
  }, []);

  return (
    <LanguageProvider>
      <RootNavigator />
      <Toast config={toastConfig} />
    </LanguageProvider>
  );
}

export default App;
